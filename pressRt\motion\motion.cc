#include "motion.h"
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "core.h"
#include "trapTraj.h"
#include "dsp/pid.h"
#include "math.h"
#include "SysShareMemoryDefine.h"
#include "DriverInfo_RayNen.h"
#include "rttimer.h"
  
ExecuteFeedback  sPowerUpExefeed;
ExecuteFeedback  sPowerDownExefeed;
ExecuteFeedback  sJogMoveExefeed;
ExecuteFeedback  sResetFaultExeFeed;

PlanMode        curPlanMode;
#define pow2(x) 			((x)*(x))
#define pow3(x) 			((x)*(x)*(x))
#define pow4(x) 			((x)*(x)*(x)*(x))
#define MaxInputPostion 	2
#define MinVelocity  		0.1

bool gPrintMotionDetail = 1;
bool gMcInScpMode = 1;
bool bAxialForceReverse = false;        //轴力反向

float32 fJogDistanceLast = 0;
float32 fWantMove = 0;
bool bJogMode = false;
bool bPosIsok = false;
bool bStopExe = false;
uint64 StopPlan_ticks, StopPlanStart_ticks;

/* MC_MoveJog                      点动执行指令。
* @param[in]     pAxis             = 需要控制的轴
* @param[in]     bJogForward       = 正向
* @param[in]     bJogBackward      = 反向
* @param[in]     fAimPos           = 点动最终目标
* @param[in]     fMoveStep         = 单步距离
* @param[in]     Velocity          = 点动单步速度
* @return        ErrorCode         = 0x4FF1 位置超限
*/
ErrorInfo MC_MoveJog(Axis_Para* pAxis, bool bJogForward, bool bJogBackward, float32 *pfAimPos, float32 *pfMoveStep, float32 *pVelocity)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    bool bForward = bJogForward;
    bool bBackward = bJogBackward;
    bool bClean;
    bool bTargetGet;
    bool bExecuteJog;
    float32 fSetMoveStep;

    bExecuteJog = false;
    if (*pfAimPos != fJogDistanceLast)
    {
        bClean = false;
        bTargetGet = false;
    }

    if (!bTargetGet)
    {
        if (!bClean)
        {
            fWantMove = *pfAimPos - pSysShareData->sSysFbkVar.fPosFbk_NoFilter;//servo_Ref^.LinePositionLogic;
            fJogDistanceLast = *pfAimPos;
        }

        if (abs(fWantMove) >= abs(*pfMoveStep))
        {
            if (fWantMove > 0)
                fSetMoveStep = abs(*pfMoveStep);
            else
                fSetMoveStep = -abs(*pfMoveStep);
        }
        else if (abs(fWantMove) < abs(*pfMoveStep))     //靠进临界
        {
            float32 fSlowDown = (pow(pSysShareData->sSysFbkVar.fVelFbk_NoFilter, 2) / (2 * pSysShareData->sSysFbkVar.fAccMax));
            if (abs(fWantMove) <= fSlowDown)
            {
                MC_Stop(pAxis);
            }
            else
            {
                if (fWantMove > 0)
                    fSetMoveStep = abs(fWantMove);
                else
                    fSetMoveStep = -abs(fWantMove);
            }
        }

        bExecuteJog = false;
        bTargetGet = true;
        bClean = false;
        if(!pAxis->Context.bStopMove)
            bExecuteJog = true;
    }
    if (bExecuteJog && !errorInfo.ErrCode)
    {
        if (bAppJogHomeCmd)
        {
            if (pAxis->Context.sMotionPlanCmd[0].bPlaned)
            {
                if (pAxis->Context.iretEval >= 2)   //避免没有加速到最大速度被打断
                {
                    errorInfo = MC_MoveRelativePos(pAxis,fSetMoveStep, *pVelocity, Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax), MC_Aborting);
                }
            }
            else
            {
                errorInfo = MC_MoveRelativePos(pAxis,fSetMoveStep, *pVelocity, Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax), MC_Aborting);
            }
        }
        else
        {
            if ((pSysShareData->sSysFbkVar.fPosFbk > Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin)) &&
                (pSysShareData->sSysFbkVar.fPosFbk < Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax)))       //当前点在范围内
            {
                if ((pSysShareData->sSysFbkVar.fPosFbk + fSetMoveStep) >= Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin) &&
                    (pSysShareData->sSysFbkVar.fPosFbk + fSetMoveStep) <= Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax))
                {
                    if (pAxis->Context.sMotionPlanCmd[0].bPlaned)    //避免没有加速到最大速度被打断
                    {
                        if (pAxis->Context.iretEval >= 2)
                        {
                            //rt_dbgPrint(1, 0, "0 fSetMoveStep:%f  fPosFbk:%f fRefPos:%f target:%f [%f,%f]\n", 
                            //    fSetMoveStep, 
                            //    pSysShareData->sSysFbkVar.fPosFbk,
                            //    pAxis->LogicRef.fPos,
                            //    fSetMoveStep + pSysShareData->sSysFbkVar.fPosFbk,
                            //    Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin),
                            //    Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax));
                            errorInfo = MC_MoveRelativePos(pAxis,fSetMoveStep, *pVelocity, Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax), MC_Aborting);
                        }
                    }
                    else 
                    {
                        errorInfo = MC_MoveRelativePos(pAxis,fSetMoveStep, *pVelocity, Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax), MC_Aborting);
                    }
                 }
                else
                {
                    if (bForward && !bBackward) //正向
                        fSetMoveStep = Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax) - pSysShareData->sSysFbkVar.fPosFbk;
                    else if (!bForward && bBackward)
                        fSetMoveStep = Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin) - pSysShareData->sSysFbkVar.fPosFbk;
                    else if (bForward && bBackward)
                        fSetMoveStep = pSysShareData->sSysJogPara.fHomePos - pSysShareData->sSysFbkVar.fPosFbk;

                    if (pAxis->Context.sMotionPlanCmd[0].bPlaned)    //避免没有加速到最大速度被打断
                    {
                        if (pAxis->Context.iretEval >= 2)
                        {
                            errorInfo = MC_MoveRelativePos(pAxis, fSetMoveStep, *pVelocity, Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax), MC_Aborting);
                        }
                    }
                    else
                    {
                        errorInfo = MC_MoveRelativePos(pAxis, fSetMoveStep, *pVelocity, Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax), MC_Aborting);
                    }
                }
            }
            else                                                                            //点在范围外
            {
                if ((pSysShareData->sSysFbkVar.fPosFbk <= (Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin)) && (fSetMoveStep + pSysShareData->sSysFbkVar.fPosFbk) > pSysShareData->sSysFbkVar.fPosFbk) ||       //超范围，向正常范围内移动
                    (pSysShareData->sSysFbkVar.fPosFbk >= (Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax)) && (fSetMoveStep + pSysShareData->sSysFbkVar.fPosFbk) < pSysShareData->sSysFbkVar.fPosFbk))
                {
                    if (pAxis->Context.sMotionPlanCmd[0].bPlaned)    //避免没有加速到最大速度被打断
                    {
                        if (pAxis->Context.iretEval >= 2)
                        {
                            errorInfo = MC_MoveRelativePos(pAxis,fSetMoveStep, *pVelocity,Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax), MC_Aborting);
                        }
                    }
                    else
                    {
                        errorInfo = MC_MoveRelativePos(pAxis,fSetMoveStep, *pVelocity, Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax), MC_Aborting);
                    }
                }
                else
                {
                    if (fSetMoveStep + pSysShareData->sSysFbkVar.fPosFbk < (Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin) - 0.001))
                    {
                        errorInfo.ErrCode = 22002;
                        errorInfo.eErrLever = Warm;
                    }
                    else if (fSetMoveStep + pSysShareData->sSysFbkVar.fPosFbk > (Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax) + 0.001))
                    {
                        errorInfo.ErrCode = 22003;
                        errorInfo.eErrLever = Warm;
                    }
                }
            }
        }
    }
    ErrorInfoPack(&errorInfo, "MC_MoveJog", "");
    return errorInfo;
}

/* MC_JogErrorCheck            点动执行开始执行前需要检查下达的指令是否合理。
* @param[in]     pAxis             = 需要控制的轴
* @return        ErrorInfo
*/
float32 fJogVleocity, fJogDistance;
ErrorInfo  MC_JogErrorCheck(float32* pfAimPos, Axis_Para* pAxis, JogType eOrderType)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    float32 fSlowDownDistance = pow(pSysShareData->sSysFbkVar.fVelFbk_NoFilter, 2) / (2 * Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax));
    if (eOrderType == Motion_JogForward)
    {
        if ((pSysShareData->sSysFbkVar.fPosFbk + fSlowDownDistance)>= (Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax)))
        {
            errorInfo.ErrCode = 22004;
            errorInfo.eErrLever = Warm;
        }

        if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorForceActived)
        {
            if (pSysShareData->sSysFbkVar.fSenosrVarFbk > (Min((pSysShareData->sSysFbkVar.fSenosrVarMax), pSysShareData->sSysJogPara.fForceMax)))//  向下 保证不超过压力   //向上 保证不超过拉力 
            {
                errorInfo.ErrCode = 22007;
                errorInfo.eErrLever = Warm;
            }
        }

        if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorExtPosActived)
        {
            if (pSysShareData->sSysFbkVar.fExtPosFbk > (Min(pSysShareData->sSysFbkVar.fExtPosMax, pSysShareData->sSysJogPara.fExtPosMax) + 0.001))
            {
                errorInfo.ErrCode = 22020;
                errorInfo.eErrLever = Warm;
            }

            if (!errorInfo.ErrCode && pSysShareData->sSysJogPara.fExtPosMin > 0)      //>0 可能是特地设的点动保护位置  需要进行小值保护
            {
                if (pSysShareData->sSysFbkVar.fExtPosFbk < (Max(pSysShareData->sSysFbkVar.fExtPosMin, pSysShareData->sSysJogPara.fExtPosMin) - 0.001))
                {
                    errorInfo.ErrCode = 22020;
                    errorInfo.eErrLever = Warm;
                }
            }
        }
    }
    else if (eOrderType == Motion_JogBackward)
    {
        if ((pSysShareData->sSysFbkVar.fPosFbk - fSlowDownDistance) < (Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin)))
        {
            errorInfo.ErrCode = 22004;
            errorInfo.eErrLever = Warm;
        }

        if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorForceActived)
        {
            if (pSysShareData->sSysFbkVar.fSenosrVarFbk < (Max(pSysShareData->sSysFbkVar.fSenosrVarMin, pSysShareData->sSysJogPara.fForceMin)))
            {
                errorInfo.ErrCode = 22007;
                errorInfo.eErrLever = Warm;
            }
        }

        if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorExtPosActived)
        {
            if (pSysShareData->sSysFbkVar.fExtPosFbk > (Min(pSysShareData->sSysFbkVar.fExtPosMax, pSysShareData->sSysJogPara.fExtPosMax) + 0.001))
            {
                errorInfo.ErrCode = 22020;
                errorInfo.eErrLever = Warm;
            }

            if (!errorInfo.ErrCode && pSysShareData->sSysJogPara.fExtPosMin > 0)      //>0 可能是特地设的点动保护位置  需要进行小值保护
            {
                if (pSysShareData->sSysFbkVar.fExtPosFbk < (Max(pSysShareData->sSysFbkVar.fExtPosMin, pSysShareData->sSysJogPara.fExtPosMin) - 0.001))
                {
                    errorInfo.ErrCode = 22020;
                    errorInfo.eErrLever = Warm;
                }
            }
        }
    }
    else
    {
        if (bAppJogHomeCmd)
        {
            if (!errorInfo.ErrCode)
            {
                if (((pSysShareData->sSysFbkVar.fPosFbk > (pSysShareData->sSysFbkVar.fPosMax - 0.003)) && *pfAimPos > pSysShareData->sSysFbkVar.fPosFbk) ||
                    ((pSysShareData->sSysFbkVar.fPosFbk < (pSysShareData->sSysFbkVar.fPosMin + 0.003)) && *pfAimPos < pSysShareData->sSysFbkVar.fPosFbk))
                {
                    errorInfo.ErrCode = 22004;
                    errorInfo.eErrLever = Warm;
                }
            }

            if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorForceActived)
            {
                if ((pSysShareData->sSysFbkVar.fSenosrVarFbk > (Min(pSysShareData->sSysFbkVar.fSenosrVarMax, pSysShareData->sSysJogPara.fProfileHomeForceMax)) && *pfAimPos > pSysShareData->sSysFbkVar.fPosFbk) ||
                    (pSysShareData->sSysFbkVar.fSenosrVarFbk < (Max(pSysShareData->sSysFbkVar.fSenosrVarMin, pSysShareData->sSysJogPara.fProfileHomeForceMin)) && *pfAimPos < pSysShareData->sSysFbkVar.fPosFbk))
                {
                    errorInfo.ErrCode = 22060;
                    errorInfo.eErrLever = Warm;
                }
            }
        }
        else
        {
            if ((*pfAimPos > (Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax) + 0.001)) ||
                (*pfAimPos < (Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin) - 0.001)))
            {
                errorInfo.ErrCode = 22050;
                errorInfo.eErrLever = Warm;
                printf("fAimPos:%f [%f,%f]\n", 
                    *pfAimPos, 
                    (Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin) - 0.001),
                    (Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax) + 0.001));
            }

            if (!errorInfo.ErrCode)
            {
                if (((pSysShareData->sSysFbkVar.fPosFbk > (pSysShareData->sSysFbkVar.fPosMax + 0.001)) && *pfAimPos > pSysShareData->sSysFbkVar.fPosFbk) ||
                    ((pSysShareData->sSysFbkVar.fPosFbk < (pSysShareData->sSysFbkVar.fPosMin - 0.001)) && *pfAimPos < pSysShareData->sSysFbkVar.fPosFbk))
                {
                    errorInfo.ErrCode = 22030;
                    errorInfo.eErrLever = Warm;
                }
            }

            if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorForceActived)
            {
                if ((pSysShareData->sSysFbkVar.fSenosrVarFbk > Min(pSysShareData->sSysFbkVar.fSenosrVarMax, pSysShareData->sSysJogPara.fForceMax) && *pfAimPos > pSysShareData->sSysFbkVar.fPosFbk) ||
                    (pSysShareData->sSysFbkVar.fSenosrVarFbk < Max(pSysShareData->sSysFbkVar.fSenosrVarMin, pSysShareData->sSysJogPara.fForceMin) && *pfAimPos < pSysShareData->sSysFbkVar.fPosFbk))
                {
                    rt_dbgPrint(1, 0, "JogErrorCheck  Force:%f [%f,%f]\n",
                        pSysShareData->sSysFbkVar.fSenosrVarFbk,
                        Max(pSysShareData->sSysFbkVar.fSenosrVarMin, pSysShareData->sSysJogPara.fForceMin),
                        Min(pSysShareData->sSysFbkVar.fSenosrVarMax, pSysShareData->sSysJogPara.fForceMax));
                    errorInfo.ErrCode = 22007;
                    errorInfo.eErrLever = Warm;
                }
            }

            if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorExtPosActived)
            {
                if (pSysShareData->sSysFbkVar.fExtPosFbk > (Min(pSysShareData->sSysFbkVar.fExtPosMax, pSysShareData->sSysJogPara.fExtPosMax) + 0.001))
                {
                    errorInfo.ErrCode = 22020;
                    errorInfo.eErrLever = Warm;
                }

                if (!errorInfo.ErrCode && pSysShareData->sSysJogPara.fExtPosMin > 0)      //>0 可能是特地设的点动保护位置  需要进行小值保护
                {
                    if (pSysShareData->sSysFbkVar.fExtPosFbk < (Max(pSysShareData->sSysFbkVar.fExtPosMin, pSysShareData->sSysJogPara.fExtPosMin) - 0.001))
                    {
                        errorInfo.ErrCode = 22020;
                        errorInfo.eErrLever = Warm;
                    }
                }
            }
        }
    }

    if (errorInfo.ErrCode)
    {
        if (pAxis->Busy)
            MC_Stop(pAxis);
    }
   ErrorInfoPack(&errorInfo, "MC_JogErrorCheck", "");
    return errorInfo;
}

/* MatchChannel
* @param[in]     char* pTargetChannelName
* @return        ErrorInfo
*/
ErrorInfo MatchChannel(char* pTargetChannelName)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    bool bFindVar = false;
    for (uint8 i = 0; i < pSysShareData->sSysAvaliablePara.SenActivedCount; i++)
    {
        if (strcmp(pTargetChannelName, pSysShareData->sSysAvaliablePara.sSensorUnit[i].spName) == 0)
        {
            bFindVar = true;
            break;
        }
    }
    if (!bFindVar)
    {
        errorInfo.ErrCode = 22008;
        errorInfo.eErrLever = Warm;
    }

    ErrorInfoPack(&errorInfo, "MatchChannel", "");
    return errorInfo;
}

/* MC_JogOrder
* @param[in]     pAxis             = 需要控制的轴
* @param[in]     psExeFeedback     = 执行点动指令的反馈
* @param[in]     eManualOrder      = 点动的类型  JogForward:3      JogBackward:4       JogHome:5       JogRefPos:6
* @return        ErrorInfo
*/
float32 fLastTargetVar = 0;
bool bJogForward, bJogBackward;
float32 fAimPos;
float32 fLastAimPos = 0;
float32 fLastForceFbk = 0;
bool bAppJogHomeCmd = false;
float32 fSlowDownDistan;
ErrorInfo MC_JogOrder(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback, uint8 eManualOrder, bool bExplorerControl)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    if (!psExeFeedback->bExeDataHadNotReset)        //参数自复位
    {
        psExeFeedback->bExeDataHadNotReset = true;
        pAxis->Context.iCmdExeCountor = 0;
        psExeFeedback->ExeErrInfo = errorInfo;
        psExeFeedback->bExecuteEnd = false;
    }

    if (gSysSimulation)                      //仿真模式
    {
        psExeFeedback->bExecuteEnd = true;
        psExeFeedback->bExeDataHadNotReset = false;
    }
    else
    {
        if (!pSysShareData->gEthercatDeviceInOp || !MC_IsPowerUp(pAxis))
        {
            psExeFeedback->bExecuteEnd = true;
            psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
            errorInfo.ErrCode = 22034;
            errorInfo.eErrLever = Warm;
            psExeFeedback->ExeErrInfo = errorInfo;
          //  rt_dbgPrint(1, 0, "JogHome defeated! Reason:Not PowerUp\n");
        }
        else
        {
            if (!bAppJogHomeCmd)
            {
                if (pSysShareData->sSysJogPara.fPositionMax > (pSysShareData->sSysFbkVar.fPosMax+0.001) ||
                    pSysShareData->sSysJogPara.fPositionMin < (pSysShareData->sSysFbkVar.fPosMin-0.001))
                {
                    errorInfo.ErrCode = 22051;
                    errorInfo.eErrLever = Warm;

                    psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    psExeFeedback->ExeErrInfo = errorInfo;
                }
                else if (pSysShareData->sSysJogPara.fForceMax > (pSysShareData->sSysFbkVar.fSenosrVarMax+0.0001) ||
                         pSysShareData->sSysJogPara.fForceMin < (pSysShareData->sSysFbkVar.fSenosrVarMin-0.0001))
                {
                    errorInfo.ErrCode = 22052;
                    errorInfo.eErrLever = Warm;
                    psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    psExeFeedback->ExeErrInfo = errorInfo;
                }

                if(errorInfo.ErrCode)
                    rt_dbgPrint(1, 0, "ErrCode:%d  bProfileHomeJogCmd:%s\n", errorInfo.ErrCode, bAppJogHomeCmd ? "true " : "false");
            }

            if(!errorInfo.ErrCode)
            {
                if (pSysShareData->sSysJogPara.eJogMoveMode == VleocityMode)
                {
                    if (eManualOrder == Motion_JogHome && bAppJogHomeCmd)     //JogHome:5
                        fJogVleocity = Min(pSysShareData->sSysJogPara.fProfileHomeVelocity, 40.0);
                    else
                        fJogVleocity = pSysShareData->sSysJogPara.fVelocity;

                    if (bExplorerControl)
                    {
                        fJogDistance = fJogVleocity * (100.0 * (1000.0 / SYS_BASE_TIME_uS) * SYS_BASE_TIME_uS / 1000000.0) +
                            abs((pow(fJogVleocity, 2) - pow(pSysShareData->sSysFbkVar.fVelFbk_NoFilter, 2)) / (2 * pSysShareData->sSysJogPara.fJogAcc)) +
                            pow(fJogVleocity, 2) / (2 * pSysShareData->sSysJogPara.fJogAcc);
                    }
                    else
                    {
                        fJogDistance = fJogVleocity * (50.0 * (1000.0 / SYS_BASE_TIME_uS) * SYS_BASE_TIME_uS / 1000000.0) +
                            abs((pow(fJogVleocity, 2) - pow(pSysShareData->sSysFbkVar.fVelFbk_NoFilter, 2)) / (2 * pSysShareData->sSysJogPara.fJogAcc)) +
                            pow(fJogVleocity, 2) / (2 * pSysShareData->sSysJogPara.fJogAcc);
                    }
                }
                else if (pSysShareData->sSysJogPara.eJogMoveMode == PositonMode)
                {
                    fJogDistance = pSysShareData->sSysJogPara.fStepDistance;
                    fJogVleocity = sqrt(abs(2 * pSysShareData->sSysJogPara.fStepDistance * fJogDistance));
                }

                //JogForward:3       JogBackward:4       JogHome:5       JogRefPos:6         JogTargetVar:7
                switch (eManualOrder)
                {
                case Motion_JogForward:     //JogForward:3  
                {
                    bJogForward = true;
                    bJogBackward = false;

                    fAimPos = Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax);
                }
                break;
                case Motion_JogBackward:     //JogBackward:4 
                {
                    bJogForward = false;
                    bJogBackward = true;

                    fAimPos = Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin);
                }
                break;
                case Motion_JogHome:     //JogHome:5
                {
                    if (abs(pSysShareData->sSysJogPara.fHomePos - pAxis->LogicFbk.fPos) < 0.005)
                    {
                        psExeFeedback->bExecuteEnd = true;
                        psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    }
                    else
                    {
                        bJogForward = true;
                        bJogBackward = true;
                        fAimPos = pSysShareData->sSysJogPara.fHomePos;
                    }
                }
                break;
                case Motion_JogRefPos:
                    break;
                case Motion_JogTargetVar:     //JogTargetVar:7
                {
                    errorInfo = MatchChannel(pSysShareData->sSysJogPara.sCustomChannelName);
                    if (!errorInfo.ErrCode)
                    {
                        if (strcmp(pSysShareData->sSysJogPara.sCustomChannelName, "Position") == 0)
                        {
                            if (abs(pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fPosFbk) < 0.005)
                            {
                                psExeFeedback->bExecuteEnd = true;
                                psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                            }
                            else
                            {
                                bJogForward = true;
                                bJogBackward = true;
                                fLastTargetVar = pSysShareData->sSysJogPara.fCustomVar;
                                fAimPos = pSysShareData->sSysJogPara.fCustomVar;
                            }
                        }
                        else if (strcmp(pSysShareData->sSysJogPara.sCustomChannelName, "Force") == 0)
                        {
                            if (pSysShareData->sSysFbkVar.fSenosrVarFbk < Min(pSysShareData->sSysFbkVar.fSenosrVarMax, pSysShareData->sSysJogPara.fForceMax))
                            {
                                if ( pSysShareData->sSysFbkVar.fSenosrVarFbk>= (pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fSenosrVarMax*0.01))
                                {
                                    psExeFeedback->bExecuteEnd = true;
                                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                                }
                                else
                                {
                                    bJogForward = true;
                                    bJogBackward = true;
                                    bool bAdjustVel = true;

                                    bool bTouched = false;
                                    if(abs(pSysShareData->sSysFbkVar.fSenosrVarFbk - fLastForceFbk) > pSysShareData->sSysFbkVar.fSenosrVarMax * 0.01)           //接触上了
                                    {
                                        //if (pAxis->Busy)
                                        //    MC_Stop(pAxis);
                                        float32 fDeltaForce = abs(pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fSenosrVarFbk);
                                        if (fDeltaForce < pSysShareData->sSysFbkVar.fSenosrVarMax * 0.05)
                                            fJogVleocity = Min(0.2, pSysShareData->sSysJogPara.fVelocity);
                                        else if (fDeltaForce < pSysShareData->sSysFbkVar.fSenosrVarMax * 0.1)
                                            fJogVleocity = Min(0.5, pSysShareData->sSysJogPara.fVelocity);
                                        else
                                        {
                                            bAdjustVel = false;
                                        }
                                        bTouched = true;
                                    }
                                    else 
                                    {
                                        bAdjustVel = false;
                                    }
                                    fLastForceFbk = pSysShareData->sSysFbkVar.fSenosrVarFbk;
                                    //速度变化后 重新计算fJogDistance
                                    if (bAdjustVel)
                                        fJogDistance = fJogVleocity * (2* (1000 / SYS_BASE_TIME_uS) * SYS_BASE_TIME_uS / 1000000.0) +
                                        abs((pow(fJogVleocity, 2) - pow(pSysShareData->sSysFbkVar.fVelFbk, 2)) / (2 * pSysShareData->sSysJogPara.fJogAcc)) +
                                        pow(fJogVleocity, 2) / (2 * pSysShareData->sSysJogPara.fJogAcc);
                                    else
                                        fJogDistance = fJogVleocity * (10 * (1000 / SYS_BASE_TIME_uS) * SYS_BASE_TIME_uS / 1000000.0) +
                                        abs((pow(fJogVleocity, 2) - pow(pSysShareData->sSysFbkVar.fVelFbk, 2)) / (2 * pSysShareData->sSysJogPara.fJogAcc)) +
                                        pow(fJogVleocity, 2) / (2 * pSysShareData->sSysJogPara.fJogAcc);

                                    //这里不能直接赋目标值  只有当是电机位置时可以这样      转换成电机的运动位置
                                    if (pSysShareData->sSysFbkVar.fSenosrVarFbk < pSysShareData->sSysJogPara.fCustomVar)
                                    {
                                        fAimPos = pSysShareData->sSysFbkVar.fPosFbk + fJogDistance;
                                        fLastTargetVar = pSysShareData->sSysFbkVar.fPosFbk + fJogDistance;
                                    }
                                    else
                                    {
                                        fAimPos = pSysShareData->sSysFbkVar.fPosFbk - fJogDistance;
                                        fLastTargetVar = pSysShareData->sSysFbkVar.fPosFbk + fJogDistance;
                                    }
                                    rt_dbgPrint(1, 0, "bTouched:%s bAdjustVel:%s fJogDistance:%f  Target:%f ForceFbk:%f fAimPos:%f\n",
                                        bTouched ? "true " : "false",
                                        bAdjustVel ? "true " : "false",
                                        fJogDistance,
                                        pSysShareData->sSysJogPara.fCustomVar,
                                        pSysShareData->sSysFbkVar.fSenosrVarFbk,
                                        fAimPos);
                                }
                            }
                            else
                            {
                                if (pAxis->Busy)
                                    MC_Stop(pAxis);
                            }
                        }
                        else if (strcmp(pSysShareData->sSysJogPara.sCustomChannelName, "ExtShift") == 0)
                        {
                            if (abs(pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fExtPosFbk) < 0.003)
                            {
                                psExeFeedback->bExecuteEnd = true;
                                psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                            }
                            else
                            {
                                bJogForward = true;
                                bJogBackward = true;

                                //float32 fExtVel = (pSysShareData->sSysJogPara.fJogAcc/2)*(sqrt(abs(4*abs(pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fExtPosFbk)/pSysShareData->sSysJogPara.fJogAcc)) - 0.003);
                                float32 fDeltaExtPos = abs(pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fExtPosFbk);

                                //降速处理 避免伺服噪音   原因是  差值太小的时候上位机给的连续脉冲信号 >15ms     如果15ms内走完 位置就会出现  15ms内的速度脉冲  导致噪音
                                if(fDeltaExtPos < 0.05)
                                    fJogVleocity = Min(0.2,pSysShareData->sSysJogPara.fVelocity);
                                else if (fDeltaExtPos < 0.1)
                                    fJogVleocity = Min(0.5, pSysShareData->sSysJogPara.fVelocity);
                                else if (fDeltaExtPos < 0.3)
                                    fJogVleocity = Min(1, pSysShareData->sSysJogPara.fVelocity);

                                fLastTargetVar = pSysShareData->sSysFbkVar.fPosFbk + (pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fExtPosFbk);
                                //这里不能直接赋目标值  只有当是电机位置时可以这样      转换成电机的运动位置
                                fAimPos = pSysShareData->sSysFbkVar.fPosFbk + (pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fExtPosFbk);//pSysShareData->sSysJogPara.fCustomVar;
                                if (fAimPos >= Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax))
                                    fAimPos = Min(fAimPos, Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax));
                                else if(fAimPos <= Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin))
                                    fAimPos = Max(fAimPos, Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin));


                                //printf("1  fAimPos:%f  [%f ,%f]\n", 
                                //    Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin),
                                //    Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax));
                            }
                        }
                        else
                        {
                            errorInfo.ErrCode = 22021;
                            errorInfo.eErrLever = Error;
                            psExeFeedback->bExecuteEnd = true;
                            psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                            psExeFeedback->ExeErrInfo = errorInfo;
                        }
                    }
                    else
                    {
                        psExeFeedback->bExecuteEnd = true;
                        psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                        psExeFeedback->ExeErrInfo = errorInfo;
                    }
                }
                break;
                default:
                    psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    break;
                }

                errorInfo = MC_JogErrorCheck(&fAimPos, pAxis, (JogType)eManualOrder);
                if (!psExeFeedback->bExecuteEnd)
                {
                    if (errorInfo.ErrCode == 0)
                    {
                        errorInfo = MC_MoveJog(pAxis, bJogForward, bJogBackward, &fAimPos, &fJogDistance, &fJogVleocity);
                    }
                    psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    psExeFeedback->ExeErrInfo = errorInfo;
                }
            }
        }
    }

    ErrorInfoPack(&errorInfo, "MC_JogOrder", "");
    return errorInfo;
}

/* MC_PowerUp
* @param[in]     pAxis                  = 需要控制的轴
* @param[in]     servoCtrlMode          = 轴的运动模式
* @param[in]     psExePowerFeedback     = 执行上电动作的结果  包括参数自复位、执行是否结束、执行过程中的错误
* @return        void
*/
uint8 ResetFaultCount = 0;
void MC_PowerUp(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback)
{
    //参数复位
    if (!psExeFeedback->bExeDataHadNotReset)
    {
        psExeFeedback->bExeDataHadNotReset = true;
        pAxis->Context.iCmdExeCountor = 0;
        memset(&psExeFeedback->ExeErrInfo, 0, sizeof(ErrorInfo));
        psExeFeedback->bExecuteEnd = false;
        //rt_dbgPrint(1, 0, "MC_PowerUp Cmd\n");
    }

    if (gSysSimulation)
    {
        psExeFeedback->bExecuteEnd = true;
        psExeFeedback->bExeDataHadNotReset = false;
    }
    else
    {
        if (pSysShareData->gEthercatDeviceInOp)
        {
            pAxis->Context.iCmdExeCountor++;
            if (MC_IsServoInError(pAxis))
            {
                MC_ResetFault(pAxis, &sResetFaultExeFeed);
                if (sResetFaultExeFeed.bExecuteEnd)
                {
                    if (sResetFaultExeFeed.ExeErrInfo.ErrCode != 0)
                        psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    psExeFeedback->ExeErrInfo.ErrCode = 22035;//
                    psExeFeedback->ExeErrInfo.eErrLever = Error;
                    rt_dbgPrint(1, 0, "Power on defeated! Reason:MC_ResetFault defeated\n");
                }
            }

            if ((pAxis->Motor.LinkVar.SW & 0x0040) == 0x0040)
            {
                pAxis->Motor.LinkVar.CW = 0x06;
            }
            else if ((pAxis->Motor.LinkVar.SW & 0x006f) == 0x0021)
            {
                pAxis->Motor.LinkVar.CW = 0x07;
            }
            else if ((pAxis->Motor.LinkVar.SW & 0x006f) == 0x0023)
            {
                pAxis->Motor.LinkVar.CW = 0xf;
                pAxis->Motor.LinkVar.VelRef = 0 * pAxis->Cfg.ShaftPlusePerRevolution;
                pAxis->Motor.LinkVar.ModeOfOp = 9;

                pAxis->LogicRef.fPos = pAxis->LogicFbk.fPos;//pAxis->LogicFbk.fPos+pAxis->Cfg.LogicPositionOffset;
                pAxis->Context.lastLogicRefPos = pAxis->LogicRef.fPos;
                pAxis->Motor.LinkVar.VelRef = 0;
                pAxis->Context.bWorkProfileAviable = false;
                memset(&pAxis->Context.trapezoidal, 0, sizeof(TrapezoidalTrajectory));

                //使能时复位指令
                memset(&pAxis->Context.sMotionPlanCmd[0], 0, sizeof(SMotionPlanCmd));
                memset(&pAxis->Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));
                pAxis->Context.trapezoidal.Done = true;

                pAxis->Context.trapezoidal.Done = 1;
                pAxis->Context.trapezoidal.step_.Y = pAxis->LogicFbk.fPos;

                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;

                curPlanMode = PosPlanMode;
                bPosIsok = true;
            }

            //Faildd
            if (pAxis->Context.iCmdExeCountor++ > 500)
            {
                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                psExeFeedback->ExeErrInfo.ErrCode = 22036;
                psExeFeedback->ExeErrInfo.eErrLever = Error;
            }
        }
        else
        {
            psExeFeedback->bExecuteEnd = true;
            psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
            psExeFeedback->ExeErrInfo.ErrCode = 22037;
            psExeFeedback->ExeErrInfo.eErrLever = HwError;
        }
    }
}

/* MC_PowerDown
* @param[in]     pAxis                  = 需要控制的轴
* @param[in]     psExePowerFeedback     = 执行下电动作的结果  包括参数自复位、执行是否结束、执行过程中的错误
* @return        void
*/
void MC_PowerDown(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback)
{
    if (!psExeFeedback->bExeDataHadNotReset)
    {
        psExeFeedback->bExeDataHadNotReset = true;
        pAxis->Context.iCmdExeCountor = 0;
        memset(&psExeFeedback->ExeErrInfo, 0, sizeof(ErrorInfo));
        psExeFeedback->bExecuteEnd = false;
    }

    if (gSysSimulation)
    {
        psExeFeedback->bExecuteEnd = true;
        psExeFeedback->bExeDataHadNotReset = false;
        rt_dbgPrint(1, 0, "gSysSimulation MC_PowerDown successfully\n");
    }
    else
    {
        if (pSysShareData->gEthercatDeviceInOp)
        {
            pAxis->Motor.LinkVar.CW = 0x06;
            pAxis->Context.iCmdExeCountor++;
            if ((pAxis->Motor.LinkVar.SW & 0x0031) == 0x0031)
            {
                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;
                //rt_dbgPrint(1, 0, "Power down successfully\n");
            }
            else
            {
                if (pAxis->Context.iCmdExeCountor++ > 1000)     //Fail
                {
                    psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    psExeFeedback->ExeErrInfo.ErrCode = 22038;// 0x4102;
                    psExeFeedback->ExeErrInfo.eErrLever = Error;
                    //rt_dbgPrint(1, 0, "Power down defeated! Reason:time out\n");
                }
            }
        }
        else
        {
            psExeFeedback->bExecuteEnd = true;
            psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
            psExeFeedback->ExeErrInfo.ErrCode = 22039;
            psExeFeedback->ExeErrInfo.eErrLever = HwError;
            rt_dbgPrint(1, 0, "Power down defeated! Reason:pSysShareData->gEthercatDeviceInOp false\n");
        }
    }

}

/* MC_ResetFault                        清除当前电机的错误
* @param[in]     pAxis                  = 需要控制的轴
* @param[Out]     psExePowerFeedback     = 执行下电动作的结果  包括参数自复位、执行是否结束、执行过程中的错误
* @return        void
*/
void MC_ResetFault(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback)
{
    if (!psExeFeedback->bExeDataHadNotReset)
    {
        //rt_dbgPrint(1, 0, "Init MC_ResetFault\n");
        pAxis->Context.iCmdExeCountor = 0;
        memset(&psExeFeedback->ExeErrInfo, 0, sizeof(ErrorInfo));
        psExeFeedback->bExecuteEnd = false;
        psExeFeedback->bExeDataHadNotReset = true;
    }

    if (gSysSimulation)
    {
        psExeFeedback->bExecuteEnd = true;
        psExeFeedback->bExeDataHadNotReset = false;
        // rt_dbgPrint(1, 0, "gSysSimulation MC_ResetFault  successfully\n");
    }
    else
    {
        if (pSysShareData->gEthercatDeviceInOp)
        {
            pAxis->Context.iCmdExeCountor++;
            if ((pAxis->Motor.LinkVar.SW & 0x08) == 0x08)
            {
                if (pAxis->Context.iCmdExeCountor < 100)
                {
                    pAxis->Motor.LinkVar.CW &= 0xFF7F;
                    //rt_dbgPrint(1, 0, "MC_ResetFault  CW &= 0xFF7F\n");
                }
                else
                {
                    pAxis->Motor.LinkVar.CW |= 0x80;
                    //rt_dbgPrint(1, 0, "MC_ResetFault  CW |= 0x80\n");
                }
            }
            else if ((pAxis->Motor.LinkVar.SW & 0x08) == 0x00)
            {
                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;
                //rt_dbgPrint(1, 0, "MC_ResetFault Finish ErrCode:%d\n", pAxis->Motor.LinkVar.ErrCode);
            }

            if (pAxis->Context.iCmdExeCountor > 1000)
            {//Fail
                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;
                psExeFeedback->ExeErrInfo.ErrCode = 22040;// 0x4103;
                psExeFeedback->ExeErrInfo.eErrLever = Error;
                //rt_dbgPrint(1, 0, "MC_ResetFault defeated time out\n");
            }
        }
        else
        {
            psExeFeedback->bExecuteEnd = true;
            psExeFeedback->bExeDataHadNotReset = false;
            psExeFeedback->ExeErrInfo.ErrCode = 22041;
            psExeFeedback->ExeErrInfo.eErrLever = HwError;
            //rt_dbgPrint(1, 0, "MC_ResetFault defeated pSysShareData->gEthercatDeviceInOp:false\n");
        }
    }
}

/* MC_SwitchCtrlMode                    切换电机的控制模式
* @param[in]     pAxis                  = 需要控制的轴
* @param[in]     servoCtrlMode          = 电机的控制模式
* @param[in]     psExePowerFeedback     = 执行下电动作的结果  包括参数自复位、执行是否结束、执行过程中的错误
* @return        void
*/
void MC_SwitchCtrlMode(Axis_Para* pAxis, EServoCtrlMode servoCtrlMode, ExecuteFeedback* psExeFeedback)
{
    if (!psExeFeedback->bExeDataHadNotReset)
    {
        psExeFeedback->bExeDataHadNotReset = true;
        pAxis->Context.iCmdExeCountor = 0;
        memset(&psExeFeedback->ExeErrInfo, 0, sizeof(ErrorInfo));
        psExeFeedback->bExecuteEnd = false;
    }

    if (gSysSimulation)
    {
        psExeFeedback->bExecuteEnd = true;
        psExeFeedback->bExeDataHadNotReset = false;
        rt_dbgPrint(1, 0, "gSysSimulation MC_SwitchCtrlMode  successfully\n");
    }
    else
    {
        if (pSysShareData->gEthercatDeviceInOp)
        {
            pAxis->Context.eRequestServoCtrlMode = servoCtrlMode;
            switch (pAxis->Context.eRequestServoCtrlMode) {
            case ServoCtrlMode_CST:
                pAxis->Motor.LinkVar.ModeOfOp = 10;
                break;
            case ServoCtrlMode_CSV:
                pAxis->Motor.LinkVar.ModeOfOp = 9;
                break;
            case ServoCtrlMode_CSP:
                pAxis->Motor.LinkVar.ModeOfOp = 8;
                pAxis->LogicRef.fPos = pAxis->LogicFbk.fPos;
                pAxis->Context.lastLogicRefPos = pAxis->LogicRef.fPos;
                pAxis->Context.bWorkProfileAviable = false;
                //pAxis->Context.iCmdReponse = 0;

                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;
                rt_dbgPrint(1, 0, "gSysSimulation MC_SwitchCtrlMode  successfully\n");
                break;
            }
            if (pAxis->Context.iCmdExeCountor++ > 60000) {//Fail
                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;
                psExeFeedback->ExeErrInfo.ErrCode = 22042;
                psExeFeedback->ExeErrInfo.eErrLever = HwError;
                rt_dbgPrint(1, 0, "gSysSimulation MC_SwitchCtrlMode  defeated timeout\n");
            }
        }
        else
        {
            psExeFeedback->bExecuteEnd = true;
            psExeFeedback->bExeDataHadNotReset = false;
            rt_dbgPrint(1, 0, "gSysSimulation MC_SwitchCtrlMode  defeated pSysShareData->gEthercatDeviceInOp:false\n");
        }
    }
}


int16 MC_CmdExeResult(Axis_Para* pAxis) {
    return  pAxis->Context.iCmdReponse;
}


bool MC_CmdExeErr(Axis_Para* pAxis) {
    return  pAxis->Context.iCmdReponse == 1;
}


bool MC_MoveCmdFinished(Axis_Para* pAxis) {
    return  !pAxis->Context.sMotionPlanCmd[0].bNeedExe;
}

/* MC_IsPowerUp                         根据电机状态字判断电机是否上电
* @param[in]     pAxis                  = 需要控制的轴
* @return        void
*/
bool MC_IsPowerUp(Axis_Para* pAxis) {
    if (pSysShareData->gEthercatDeviceInOp)
        return (pAxis->Motor.LinkVar.SW & 0x237) == 0x237;
    else
        return false;
}

/* MC_IsServoInError                     根据电机状态字判断电机是否报错
* @param[in]     pAxis                  = 需要控制的轴
* @return        void
*/
bool MC_IsServoInError(Axis_Para* pAxis) {
    if (pSysShareData->gEthercatDeviceInOp)
        return ((pAxis->Motor.LinkVar.SW & 0x08) == 0x08); // && (pAxis->Motor.LinkVar.ErrorCode != 0xE08)
    else
        return true;
}

/* MC_IsServoInError                     根据电机状态字判断电机是否报错
* @param[in]     pAxis                  = 需要控制的轴
* @return        void
*/
ErrorInfo MC_ServoErrorCode(Axis_Para* pAxis) {
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    switch (driveType)
    {
    case INOVANCE_DRIVE:
        //Inovance      //{0x0140, 0x01400140, 40023},   //{0x8400, 0x15000500, 40044},
        if (pAxis->Motor.LinkVar.ErrCode && errorInfo.ErrCode == 0)
        {
            errorInfo.ErrCode = Invance_findSysError(pAxis->Motor.LinkVar.ErrCode, pAxis->Motor.LinkVar.SubErrCode);
            if (errorInfo.ErrCode == 0 && (pAxis->Motor.LinkVar.ErrCode != 0 || pAxis->Motor.LinkVar.SubErrCode != 0))
            {
                errorInfo.ErrCode = 42000;
            }

            if (errorInfo.ErrCode == 40024)   //急停触发
            {
                errorInfo.eErrLever = HwError;
            }
            else if (errorInfo.ErrCode == 40025)   //光栅触发
            {
                errorInfo.eErrLever = Error;
            }
            else if (errorInfo.ErrCode)
                errorInfo.eErrLever = Error;
        }
        break;
    case RAYNEN_DRIVE:
        if (pAxis->Motor.LinkVar.ErrCode && errorInfo.ErrCode == 0)
        {
            errorInfo.ErrCode = RN_findSysError(pAxis->Motor.LinkVar.ErrCode);
            if (errorInfo.ErrCode)
            {
                errorInfo.eErrLever = HwError;
            }
        }
        break;
    default:
        break;
    }
    ErrorInfoPack(&errorInfo, "MC_MoveRelativePos", "");
    return errorInfo;
}

/* MC_Stop                              给电机下发停止运动信号
* @param[in]     pAxis                  =需要控制的轴
* @return        void
*/
void MC_Stop(Axis_Para* pAxis)
{
    printf("MC_Stop:%s\n", bStopExe?"true":"false");
    if (!bStopExe)//避免过度  触发stop    后续完善可做成 当加速度不同时  可以再次触发stop  做stop的 不同等级
    {
        float32 fSlowDownDistance = pow(pSysShareData->sSysFbkVar.fVelFbk_NoFilter, 2) / (2 * Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax));
        pAxis->Context.bStopMove = true;
        StopPlanStart_ticks = GetSysTick();

        dbgPrint(1, 1, "\n MC_Stop gSysCounter:%lu  RefV:%f FbkPos:%f fPosNoFilter:%f FbkV:%f  fVelNoFilter:%f   fSlowDownDistance:%f\n\n",
            pSysShareData->gSysCounter,
            pAxis->LogicRef.fVel,
            pAxis->LogicFbk.fPos,
            pAxis->LogicFbk.fPosNoFilter,
            pAxis->LogicFbk.fVel,
            pAxis->LogicFbk.fVelNoFilter,
            fSlowDownDistance);
    }
}

/* MC_MoveRelativePos                   给电机下发停止运动信号
* @param[in]    pAxis                  =需要控制的轴
* @param[in]    TargetTorque           规划中运行的扭矩值
* @param[in]    TorqueRate             电机的额定扭矩
* @param[in]    Distance               运动距离
* @param[in]    Vel                    运动的最大速度
* @param[in]    Acc                    最大加速度
* @param[in]    eCmdBuffMode           运动模式
* @return       ErrorInfo
*/
float32 tmpTargetPos = 0;
float64 time_us, time_us_last, fDelataTime;
ErrorInfo MC_MoveRelativePos(Axis_Para* pAxis, float32 Distance, float32 Vel, float32 Acc, EBuffermode eCmdBuffMode)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    float Vout, Vin, Pos;
    uint8 buffIndex = 0xf;

   // dbgPrint(1, 0, " MC_MoveRelativePos  PosCmdBuff Distance:%f\n", Distance);
    if (Vel != 0 && Acc != 0 && Distance != 0)
    {
        if (!pAxis->Context.sMotionPlanCmd[0].bNeedExe)
        {
            if (((Distance + pSysShareData->sSysFbkVar.fPosFbk) <= (pSysShareData->sSysFbkVar.fPosMax + 0.0005) &&
                (Distance + pSysShareData->sSysFbkVar.fPosFbk) >= pSysShareData->sSysFbkVar.fPosMin - 0.0005) ||
                ((pSysShareData->sSysFbkVar.fPosFbk < pSysShareData->sSysFbkVar.fPosMin) && (Distance + pSysShareData->sSysFbkVar.fPosFbk) > (pSysShareData->sSysFbkVar.fPosFbk)) ||
                ((pSysShareData->sSysFbkVar.fPosFbk > pSysShareData->sSysFbkVar.fPosMax) && (Distance + pSysShareData->sSysFbkVar.fPosFbk) < (pSysShareData->sSysFbkVar.fPosFbk)))
            {
                //  dbgPrint(1, 0, " MC_MoveRelativePos  PosCmdBuff Distance:%f\n", Distance);
                pAxis->Context.sMotionPlanCmd[0].CurrP = pAxis->LogicFbk.fPos;                     //当前位置
                pAxis->Context.sMotionPlanCmd[0].TargetP = Distance + pAxis->LogicFbk.fPos;        //目标位置
                pAxis->Context.sMotionPlanCmd[0].Vmax = Vel;                                       //最大速度
                pAxis->Context.sMotionPlanCmd[0].Acc = Acc;                                        //加速度
                pAxis->Context.sMotionPlanCmd[0].Jerk = 1000;                                      //加加速度
                pAxis->Context.sMotionPlanCmd[0].Vin = pAxis->LogicFbk.fVel;                       //给入速度
                pAxis->Context.sMotionPlanCmd[0].Vout = 0;                                         //结束速度
                pAxis->Context.sMotionPlanCmd[0].Distance = Distance;  //位移
                pAxis->Context.sMotionPlanCmd[0].bNeedExe = true;                                  //需要执行当前运动buff
                pAxis->Context.sMotionPlanCmd[0].bPlaned = false;
                pAxis->Context.sMotionPlanCmd[0].Buffermode = eCmdBuffMode;

                pAxis->Context.sMotionPlanCmd[0].Buffermode = eCmdBuffMode;
                Vout = pAxis->Context.sMotionPlanCmd[0].Vout;
                Pos = pAxis->Context.sMotionPlanCmd[0].TargetP;
                curPlanMode = PosPlanMode;
                //buffIndex = 0;
                //time_us= GetTimeStamp_mS();
                //fDelataTime = (time_us - time_us_last)/1000;
                //time_us_last = time_us;
                //rt_dbgPrint(1, 0, "0 Targetpos:%.6f  Vmax:%.6f Vin:%.6f, Vout:%.6f, Distance:%.6f ,Acc:%.6f Buffermode:%d \n",
                //    Pos,
                //    Vel,
                //    pAxis->LogicRef.fVel,
                //    Vout,
                //    Distance,
                //    Acc,
                //    eCmdBuffMode);
            }
            else
            {
                //rt_dbgPrint(1, 0, " Distance:%.6f  fPosFbk:%.6f   Target:%f [%.6f,%.6f]\n",
                //    Distance,
                //    pSysShareData->sSysFbkVar.fPosFbk,
                //    Distance + pSysShareData->sSysFbkVar.fPosFbk,
                //    pSysShareData->sSysFbkVar.fPosMin - 0.0001,
                //    pSysShareData->sSysFbkVar.fPosMax + 0.0001);
                errorInfo.ErrCode = 22009;
                errorInfo.eErrLever = Error;
            }
        }
        else if (!pAxis->Context.sMotionPlanCmd[1].bNeedExe || eCmdBuffMode == MC_Aborting)
        {
            if (eCmdBuffMode == MC_Aborting)
                tmpTargetPos = Distance + pSysShareData->sSysFbkVar.fPosFbk;                     //目标位置     // pAxis->Context.sMotionPlanCmd[0].TargetP
            else
                tmpTargetPos = Distance + pAxis->Context.sMotionPlanCmd[0].TargetP; //目标位置     // pAxis->Context.sMotionPlanCmd[0].TargetP

             
            if ((abs(tmpTargetPos - pAxis->Context.sMotionPlanCmd[0].TargetP) < 0.001) && abs(Vel - pAxis->Context.sMotionPlanCmd[0].Vmax) < 0.01)
            {
                //rt_dbgPrint(1, 0, "RepeatCmd Not Need Execute\n\n");//  重复的位置和速度指令  不需要再次执行
                //rt_dbgPrint(1, 0, " Distance:%.6f fPosFbk:%.6f VelMax:%.6f  fbkV:%.6f\n",
                //    Distance,
                //    pSysShareData->sSysFbkVar.fPosFbk,
                //    Vel,
                //    pSysShareData->sSysFbkVar.fVelFbk);
            }
            else
            {
                pAxis->Context.sMotionPlanCmd[1].TargetP = tmpTargetPos;
                if (((pAxis->Context.sMotionPlanCmd[1].TargetP) <= (pSysShareData->sSysFbkVar.fPosMax + 0.0005) &&
                    (pAxis->Context.sMotionPlanCmd[1].TargetP) >= pSysShareData->sSysFbkVar.fPosMin - 0.0005) ||
                    ((pSysShareData->sSysFbkVar.fPosFbk < pSysShareData->sSysFbkVar.fPosMin) && (pAxis->Context.sMotionPlanCmd[1].TargetP) > (pSysShareData->sSysFbkVar.fPosFbk)) ||
                    ((pSysShareData->sSysFbkVar.fPosFbk > pSysShareData->sSysFbkVar.fPosMax) && (pAxis->Context.sMotionPlanCmd[1].TargetP) < (pSysShareData->sSysFbkVar.fPosFbk)))
                {
                    pAxis->Context.sMotionPlanCmd[1].CurrP = pAxis->LogicFbk.fPos;                                  //当前位置
                    pAxis->Context.sMotionPlanCmd[1].Vmax = Vel;                                                    //最大速度
                    pAxis->Context.sMotionPlanCmd[1].Acc = Acc;                                                     //加速度
                    pAxis->Context.sMotionPlanCmd[1].Jerk = 1000;                                                   //加加速度
                    pAxis->Context.sMotionPlanCmd[1].Vin = pAxis->LogicFbk.fVel;                                    //给入速度
                    pAxis->Context.sMotionPlanCmd[1].Vout = 0;                                                      //结束速度
                    pAxis->Context.sMotionPlanCmd[1].Distance = Distance;                                           //位移
                    pAxis->Context.sMotionPlanCmd[1].bNeedExe = true;                                               //需要执行当前运动buff
                    pAxis->Context.sMotionPlanCmd[1].bPlaned = false;
                    pAxis->Context.sMotionPlanCmd[1].Buffermode = eCmdBuffMode;

                    Vout = pAxis->Context.sMotionPlanCmd[1].Vout;
                    Pos = pAxis->Context.sMotionPlanCmd[1].TargetP;
                    buffIndex = 1;
                    curPlanMode = PosPlanMode;
                    //time_us = GetTimeStamp_mS();
                    //fDelataTime = (time_us - time_us_last) / 1000;
                    //time_us_last = time_us;

                    //rt_dbgPrint(1, 0, "Targetpos:%.6f  Vmax:%.6f Vin:%.6f, Vout:%.6f, Distance:%.6f ,Acc:%.6f Buffermode:%d \n",
                    //    Pos,
                    //    Vel,
                    //    pAxis->LogicRef.fVel,
                    //    Vout,
                    //    Distance,
                    //    Acc,
                    //    eCmdBuffMode);
                }
                else
                {
                    rt_dbgPrint(1, 0, " Distance:%.6f  fPosFbk:%.6f   Target:%f [%.6f,%.6f]\n",
                        Distance,
                        pSysShareData->sSysFbkVar.fPosFbk,
                        pAxis->Context.sMotionPlanCmd[1].TargetP,
                        pSysShareData->sSysFbkVar.fPosMin - 0.0005,
                        pSysShareData->sSysFbkVar.fPosMax + 0.0005);

                    dbgPrint(1, 0, " MC_MoveRelativePos  PosCmdBuff  not allow\n");
                    errorInfo.ErrCode = 22009;
                    errorInfo.eErrLever = Error;
                }
            }

        }
        else
        {
            dbgPrint(1, 0, " MC_MoveRelativePos  PosCmdBuff  not allow\n");
        }
    }
    else
    {
        if (Distance == 0)
        {
            errorInfo.ErrCode = 22011;
        }
        else if (Vel == 0)
        {
            errorInfo.ErrCode = 22012;
        }
        else if (Acc == 0)
        {
            errorInfo.ErrCode = 22013;
        }
        else
        {
            errorInfo.ErrCode = 22010;
        }
        errorInfo.eErrLever = Error;
    }

    ErrorInfoPack(&errorInfo, "MC_MoveRelativePos", "");
    return errorInfo;
}

/* MC_MoveAbsPos                   给电机下发停止运动信号
* @param[in]    pAxis                  =需要控制的轴
* @param[in]    TargetTorque           规划中运行的扭矩值
* @param[in]    TorqueRate             电机的额定扭矩
* @param[in]    Pos                    目标位置
* @param[in]    Vel                    运动的最大速度
* @param[in]    Acc                    最大加速度
* @param[in]    eCmdBuffMode           运动模式
* @return       ErrorInfo
*/
ErrorInfo MC_MoveAbsPos(Axis_Para* pAxis, float32 Pos, float32 VelMax, float32 Acc, EBuffermode eCmdBuffMode)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    float Vout, Distance, Vin;
    uint8 buffIndex = 0;
    if (VelMax != 0 && Acc != 0)
    {
        if (!pAxis->Context.sMotionPlanCmd[0].bNeedExe)
        {
            pAxis->Context.sMotionPlanCmd[0].CurrP = pAxis->LogicFbk.fPos;                  //当前位置
            pAxis->Context.sMotionPlanCmd[0].TargetP = Pos;                                 //目标位置
            pAxis->Context.sMotionPlanCmd[0].Vmax = VelMax;                                    //最大速度
            pAxis->Context.sMotionPlanCmd[0].Acc = Acc;                                      //加速度
            pAxis->Context.sMotionPlanCmd[0].Jerk = 1000;                                    //加加速度
            pAxis->Context.sMotionPlanCmd[0].Vin = pAxis->LogicFbk.fVel;                     //给入速度
            pAxis->Context.sMotionPlanCmd[0].Vout = 0;// VelOut;                                  //结束速度
            pAxis->Context.sMotionPlanCmd[0].Distance = (Pos - pAxis->LogicFbk.fPos);        //位移       
            pAxis->Context.sMotionPlanCmd[0].bNeedExe = true;                               //需要执行当前运动buff
            pAxis->Context.sMotionPlanCmd[0].bPlaned = false;

            pAxis->Context.sMotionPlanCmd[0].Buffermode = eCmdBuffMode;

            Vout = pAxis->Context.sMotionPlanCmd[0].Vout;
            Distance = pAxis->Context.sMotionPlanCmd[0].Distance;
            buffIndex = 0;
            curPlanMode = PosPlanMode;
        }
        else if (!pAxis->Context.sMotionPlanCmd[1].bNeedExe || eCmdBuffMode == MC_Aborting)
        {
            if (pAxis->Context.sMotionPlanCmd[0].TargetP == Pos &&
                pAxis->Context.sMotionPlanCmd[0].Vmax == VelMax &&
                pAxis->Context.sMotionPlanCmd[0].Acc == Acc &&
                pAxis->Context.sMotionPlanCmd[0].Buffermode == eCmdBuffMode)
            {
                ;// rt_dbgPrint(1, 0, "MC_MoveAbsPos Cmd[1] Repeat Cmd  TargetP:%f  Vmax:%f \n", Pos, VelMax);
            }
            else
            {
                pAxis->Context.sMotionPlanCmd[1].CurrP = pAxis->LogicFbk.fPos;                   //当前位置
                pAxis->Context.sMotionPlanCmd[1].TargetP = Pos;                                  //目标位置
                pAxis->Context.sMotionPlanCmd[1].Vmax = VelMax;                                  //最大速度
                pAxis->Context.sMotionPlanCmd[1].Acc = Acc;                                      //加速度
                pAxis->Context.sMotionPlanCmd[1].Jerk = 1000;                                    //加加速度
                pAxis->Context.sMotionPlanCmd[1].Vin = pAxis->LogicFbk.fVel;                     //给入速度
                pAxis->Context.sMotionPlanCmd[1].Vout = 0;// VelOut;                                       //结束速度
                pAxis->Context.sMotionPlanCmd[1].Distance = (Pos - pAxis->LogicFbk.fPos);        //位移
                pAxis->Context.sMotionPlanCmd[1].bNeedExe = true;                                //需要执行当前运动buff
                pAxis->Context.sMotionPlanCmd[1].bPlaned = false;

                pAxis->Context.sMotionPlanCmd[1].Buffermode = eCmdBuffMode;

                Vout = pAxis->Context.sMotionPlanCmd[1].Vout;
                Distance = pAxis->Context.sMotionPlanCmd[1].Distance;
                curPlanMode = PosPlanMode;
                buffIndex = 1;

                //rt_dbgPrint(1, 0, "MC_MoveAbsPos Cmd[1] Repeat Cmd  TargetP:%f  Vmax:%f \n", Pos, VelMax);
            }
        }
        else
        {
            rt_dbgPrint(1, 0, " MC_MoveAbsPos  PosCmdBuff  not allow\n");
            errorInfo.ErrCode = 22015;
            errorInfo.eErrLever = Error;
        }
    }
    else
    {
        if (VelMax == 0)
        {
            errorInfo.ErrCode = 22016;
        }
        else if (Acc == 0)
        {
            errorInfo.ErrCode = 22017;
        }
        else
        {
            errorInfo.ErrCode = 22015;
        }
        errorInfo.eErrLever = Error;
        rt_dbgPrint(1, 0, "Errrrrrror    nActualInputData filled\n");
    }


    ErrorInfoPack(&errorInfo, "MC_MoveAbsPos", "");
    return errorInfo;
}

uint64 gMaxMcMoveLelativeCalTime_nS = 0;

bool MC_IsStandStill(Axis_Para* pAxis)
{
    if (abs(pAxis->LogicFbk.fVel) < 0.01)	//压机处在静止状态
        return true;
    else
        return false;
}


int iPlanIndex = 0;
/* MC_ParaInit                          给电机下发停止运动信号
* @param[in]     pAxis                  =需要控制的轴
* @return        void
*/
void MC_ParaInit(Axis_Para* pAxis)
{
    //这一块可以放到系统初始化  或者自检中进行

    InitMoveAvgFilter(&pAxis->Context.PosFbkCalcFlt, 10);
    InitMoveAvgFilter(&pAxis->Context.velFbkCalcFlt, 20);
    InitMoveAvgFilter(&pAxis->Context.AccFbkCalcFlt, 20);

    pAxis->LogicRef.fPos = 0;
    pAxis->LogicRef.fVel = 0;
    pAxis->LogicRef.fAcc = 0;
    pAxis->LogicRef.fJerk = 0;
    pAxis->Context.trapezoidal.Done = 1;
    pAxis->Motor.LinkVar.TorqueRef = 3000;
    pAxis->Motor.LinkVar.VelRef = 0;
}

/* MC_Tare                              电机去皮
* @param[in]     pAxis                  =需要控制的轴
* @return        void
*/

ErrorInfo MC_Tare(sAxisMcPara* pAxisMcPara, TrigData* pMotorTareTrig)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    pMotorTareTrig->bTrigSignal = pAxisMcPara->sMcSetPara.bLogicPosTare;
    R_Trig(pMotorTareTrig);
    if (pMotorTareTrig->bTrig)
    {
        if (!MC_IsPowerUp(&pAxisMcPara->Axis))
        {
            float32 fTmpLogicPositionOffset = 0;
            fTmpLogicPositionOffset = pAxisMcPara->Axis.Cfg.LogicPositionOffset - pAxisMcPara->Axis.LogicFbk.fPos;
            pAxisMcPara->Axis.Cfg.LogicPositionOffset = fTmpLogicPositionOffset;
        }
        else
        {
            errorInfo.eErrLever = Warm;
            errorInfo.ErrCode = 22019;
        }
    }

    ErrorInfoPack(&errorInfo, "MC_Tare", "");
    return errorInfo;
}


///* SoftStop                   依据当前的速度  软停止
//* @param[in]     pAxis        需要控制的轴
//* @return        void
//*/
//void SoftStop(sAxisMcPara* pAxisMc)
//{
//    //rt_dbgPrint(1, 0, "*******************  TrapezoidalPlan 1 SoftStopMove *************************** \n");
//    float Distance = 0;
//    //当前速度如果小于零 则应该做一次distance为负的加减速运动
//    if (pAxisMc->Axis.LogicFbk.fVel > 0 || pAxisMc->Axis.LogicFbk.fVel < 0)
//    {
//        if (pAxisMc->Axis.LogicFbk.fVel > 0)
//        {
//            Distance = pow2(pAxisMc->Axis.LogicFbk.fVel) / (2 * pAxisMc->sMcSetPara.sAccDecLimRange.Maxinum) + 0.001;//pAxis->Context.sMotionPlanCmd[1].Acc);
//        }
//        else
//        {
//            Distance = -pow2(pAxisMc->Axis.LogicFbk.fVel) / (2 * pAxisMc->sMcSetPara.sAccDecLimRange.Maxinum) - 0.001;//一定要做计算精度补偿  不然会出现先规划异常  
//        }
//
//        rt_dbgPrint(1, 0, "\n Stop Distance:%f  CurPos:%f  TargetP:%f CurForce:%f  fVelFbk:%f  fVelRef:%f\n\n", 
//            Distance,
//            pSysShareData->sSysFbkVar.fPosFbk, 
//            pAxisMc->Axis.LogicFbk.fPos + Distance,
//            pSysShareData->sSysFbkVar.fSenosrVarFbk,
//            pAxisMc->Axis.LogicFbk.fVel,
//            pAxisMc->Axis.LogicRef.fVel);
//
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].CurrP = pAxisMc->Axis.LogicFbk.fPos;                   //当前位置
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].TargetP = pAxisMc->Axis.LogicFbk.fPos + Distance;      //目标位置
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Vmax = pAxisMc->Axis.LogicFbk.fVel;                    //最大速度
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Acc = pAxisMc->sMcSetPara.sAccDecLimRange.Maxinum;                //加速度
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Jerk = 1000;                                                //加加速度
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Vin = pAxisMc->Axis.LogicFbk.fVel;                     //给入速度
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Vout = 0;                                               //结束速度
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Distance = Distance;                                    //位移
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].bNeedExe = true;                                        //需要执行当前运动buff
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].bPlaned = false;
//
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode = MC_Aborting;
//        pAxisMc->Axis.Context.bStopMove = false;
//
//        memcpy(&pAxisMc->Axis.Context.sMotionPlanCmd[0], &pAxisMc->Axis.Context.sMotionPlanCmd[1], sizeof(SMotionPlanCmd));
//        memset(&pAxisMc->Axis.Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));
//        pAxisMc->Axis.DecPhase = false;
//        //pAxisMc->Axis.CurrSet.idx = 1;
//    }
//}

/* MC_ServoCaculation                   根据规划好的运动参数，执行运动规划，实时下发速度指令。
* @param[in]     pAxis                  = 需要控制的轴
* @return        void
*/
TrigData    ClearFTrig;
TrigData    sExecuteFinishTrig;
int  iretLast = 0;
uint64					gTestCounter;
TrigData TestTrig;


bool bPidMove = false;

uint64 MovePlan_ticks,MovePlanStart_ticks;
float32 fRefPosStart = 0;
void MC_ServoCaculation(sAxisMcPara* pAxisMc,bool *pbJogMode)
{
    MotorPara* pMotor = &pAxisMc->Axis.Motor;// &pAxis->Motor;
    float TargetTorque = 0.2;

    int iret = 0;
    //刷新轴的数据
    if (MC_IsPowerUp(&pAxisMc->Axis))//(pAxisMc->Axis.Motor.LinkVar.SW & 0x237) == 0x237)  //has power on
    {
        switch (pAxisMc->Axis.Motor.LinkVar.ModeOfOp)
        {
        case 9:     //CSV
        {
            //Buff0 规划
            // buff[1] 接收到 Abort  指令，中止buff[0]的规划，重新进行规划
            if (pAxisMc->Axis.Context.bStopMove
                || ((pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode == MC_Aborting
                    && pAxisMc->Axis.Context.sMotionPlanCmd[1].bNeedExe == true
                    && !pAxisMc->Axis.Context.sMotionPlanCmd[1].bPlaned))          //Plan1
                || (((pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode == MC_BlendingLow ||
                    pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode == MC_Buffered)
                    && pAxisMc->Axis.Context.sMotionPlanCmd[1].bNeedExe == true
                    && !pAxisMc->Axis.Context.sMotionPlanCmd[1].bPlaned))           // plan2
                || ((pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode == MC_BlendingHigh
                    && pAxisMc->Axis.Context.sMotionPlanCmd[1].bNeedExe == true
                    && !pAxisMc->Axis.Context.sMotionPlanCmd[1].bPlaned))      // plan3
                || (pAxisMc->Axis.Context.trapezoidal.Done && pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned
                    && pAxisMc->Axis.Context.sMotionPlanCmd[1].bNeedExe && !pAxisMc->Axis.Context.sMotionPlanCmd[1].bPlaned))     //plan4
            {
                if ((pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode == MC_Aborting)
                    || (pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode == MC_BlendingLow && pAxisMc->Axis.Context.sMotionPlanCmd[0].Vmax > pAxisMc->Axis.Context.sMotionPlanCmd[1].Vmax)
                    || (pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode == MC_BlendingHigh && pAxisMc->Axis.Context.sMotionPlanCmd[0].Vmax < pAxisMc->Axis.Context.sMotionPlanCmd[1].Vmax))
                {
                    if (pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode == MC_Aborting)
                        pSysShareData->sTestData.gAbortCounter++;

                    memcpy(&pAxisMc->Axis.Context.sMotionPlanCmd[0], &pAxisMc->Axis.Context.sMotionPlanCmd[1], sizeof(SMotionPlanCmd));
                    memset(&pAxisMc->Axis.Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));
                    pAxisMc->Axis.DecPhase = false;
                }
                else if (pAxisMc->Axis.Context.bStopMove)
                {
                    if (!bStopExe)
                    {
                        curPlanMode = VelPlanMode;
                        pAxisMc->Axis.Context.trapezoidal.Done = true;//停掉位置梯形规划

                        RampPlan(&pAxisMc->Axis.Context.velRamp, pAxisMc->Axis.LogicFbk.fVelNoFilter, 0, pSysShareData->sSysFbkVar.fAccMax);

                        bStopExe = true;
                        pAxisMc->Axis.Context.bStopMove = false;
                        pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.LogicFbk.fPos;
                        fRefPosStart = pAxisMc->Axis.LogicRef.fPos;
                        rt_dbgPrint(1, 0, "*******************  RampPlan   fRefPosStart:%f  fTarget:%f*************************** \n", 
                            fRefPosStart);

                        pAxisMc->Axis.DecPhase = false;
                        memset(&pAxisMc->Axis.Context.sMotionPlanCmd[0], 0, sizeof(SMotionPlanCmd));
                        memset(&pAxisMc->Axis.Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));
                    }
                }
                else
                {
                    if (pAxisMc->Axis.Context.sMotionPlanCmd[1].bNeedExe)
                    {
                       // rt_dbgPrint(1, 0, "*******************  TrapezoidalPlan 1 *************************** \n");
                        memcpy(&pAxisMc->Axis.Context.sMotionPlanCmd[0], &pAxisMc->Axis.Context.sMotionPlanCmd[1], sizeof(SMotionPlanCmd));
                        memset(&pAxisMc->Axis.Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));
                        pAxisMc->Axis.DecPhase = false;
                    }
                }
            }

            if (curPlanMode == VelPlanMode)                 //斜坡降速规划 用来stop
            {
                if (bStopExe)
                {
                    if (!pAxisMc->Axis.Context.velRamp.Done)
                    {
                        StopPlan_ticks = GetSysTick();
                        pAxisMc->Axis.CurrSet.fTime = (float32)((float64)(StopPlan_ticks - StopPlanStart_ticks) / 1000000000.0) + SYS_BASE_TIME_S;		//纳秒转换成秒;

                        //速度斜坡规划
                        int velRet = RampEval(&pAxisMc->Axis.Context.velRamp, pAxisMc->Axis.CurrSet.fTime);

                        pAxisMc->Axis.LogicRef.fVel = pAxisMc->Axis.Context.velRamp.step_.Yd;
                        pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.Context.velRamp.step_.Y + fRefPosStart;

                        pAxisMc->Axis.DecPhase = true;

                        bStopExe = !pAxisMc->Axis.Context.velRamp.Done;
                    }
                    else
                    {
                        pAxisMc->Axis.LogicRef.fVel = 0;
                        pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.Context.velRamp.step_.Y + fRefPosStart;// pAxisMc->Axis.LogicFbk.fPos;
                    }
                }
                else
                {
                    pAxisMc->Axis.LogicRef.fVel = 0;
                    pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.Context.velRamp.step_.Y + fRefPosStart;
                }

                pAxisMc->Axis.Status.fPosError = pAxisMc->Axis.LogicRef.fPos - pAxisMc->Axis.LogicFbk.fPosNoFilter;
                //规避驱动器上电静态出现咔哒声音
                if (fabs(pAxisMc->Axis.Status.fPosError) < 0.001f) {
                    pAxisMc->Axis.Status.fPosError = 0.0f;
                }

                if (abs(pAxisMc->Axis.Context.posPidCtrl.fMinAllowPosErr) < 0.001) //避免参数误设置运动规划异常
                    pAxisMc->Axis.Context.posPidCtrl.fMinAllowPosErr = 0.002;

                if (abs(pAxisMc->Axis.Status.fPosError) < abs(pAxisMc->Axis.Context.posPidCtrl.fMinAllowPosErr) &&
                    pAxisMc->Axis.Context.velRamp.Done)    //MinAllowPosErr 0.001
                    bPosIsok = true;
                else
                    bPosIsok = false;

                pAxisMc->Axis.LogicRef.fVel = Saturation(pAxisMc->Axis.Context.posPidCtrl.feedForwardRatio * pAxisMc->Axis.Context.velRamp.step_.Yd +
                    pAxisMc->Axis.Status.fPosError * pAxisMc->Axis.Context.posPidCtrl.kp,
                    -abs(pAxisMc->Axis.Context.velRamp.Xi_), abs(pAxisMc->Axis.Context.velRamp.Xi_));

                pAxisMc->Axis.Busy = !(pAxisMc->Axis.Context.velRamp.Done && bPosIsok);

                //if (pAxisMc->Axis.Busy  && pSysShareData->SeqAux.iActiveRowIndex == 3)
                //{
                //    rt_dbgPrint(1, 0, "Stop   Counter:%llu Done:%s  RefVel:%f FbkVel:%f  fTime:%f \n",
                //        pSysShareData->gSysCounter,
                //        pAxisMc->Axis.Context.velRamp.Done ? "true " : "false",
                //        pAxisMc->Axis.LogicRef.fVel,
                //        pAxisMc->Axis.LogicFbk.fVelNoFilter,
                //        pAxisMc->Axis.CurrSet.fTime);
                //}
            }
            else if (curPlanMode == PosPlanMode)            //梯形位置规划
            {
                bStopExe = false;   //一定要清一遍不然 如果在stop中没有规划结束 就会出现问题
                //对buff 0 进行位置规划
                if (pAxisMc->Axis.Context.sMotionPlanCmd[0].bNeedExe == true &&
                    !pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned)
                {
                    if (pAxisMc->Axis.Context.sMotionPlanCmd[0].Vmax != 0)
                    {
                        if (pAxisMc->Axis.Context.sMotionPlanCmd[0].Acc != 0)
                        {
                            TrapezoidalPlan(&pAxisMc->Axis.Context.trapezoidal,
                                pAxisMc->Axis.Context.sMotionPlanCmd[0].TargetP,
                                pAxisMc->Axis.LogicFbk.fPos,
                                pAxisMc->Axis.LogicFbk.fVel,
                                pAxisMc->Axis.Context.sMotionPlanCmd[0].Vmax,
                                pAxisMc->Axis.Context.sMotionPlanCmd[0].Acc,
                                pAxisMc->Axis.Context.sMotionPlanCmd[0].Acc);

                            MovePlanStart_ticks = GetSysTick();
                            pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.LogicFbk.fPos;
                            pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned = true;
                            pAxisMc->Axis.DecPhase = false;
                           // rt_dbgPrint(1, 0, "*******************  TrapezoidalPlan MovePlanStart_ticks:%llu *************************** \n", MovePlanStart_ticks);
                        }
                        else
                        {
                            rt_dbgPrint(1, 0, "*******************  TrapezoidalPlan Error 1*************************** \n");
                            memset(&pAxisMc->Axis.Context.sMotionPlanCmd[0], 0, sizeof(SMotionPlanCmd));
                            pAxisMc->Axis.Context.trapezoidal.Done = false;
                            pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned = false;
                        }
                    }
                    else
                    {
                        rt_dbgPrint(1, 0, "*******************  TrapezoidalPlan Error 2*************************** \n");
                        memset(&pAxisMc->Axis.Context.sMotionPlanCmd[0], 0, sizeof(SMotionPlanCmd));
                        pAxisMc->Axis.Context.trapezoidal.Done = false;
                        pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned = false;
                    }
                }

                //已经被规划 但是当前没有被执行则开始执行规划
                if (!pAxisMc->Axis.Context.trapezoidal.Done && pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned)
                {
                    MovePlan_ticks = GetSysTick();
                    pAxisMc->Axis.CurrSet.fTime = (float32)((float64)(MovePlan_ticks - MovePlanStart_ticks) / 1000000000.0)+ SYS_BASE_TIME_S;		//纳秒转换成秒;
                    iret = TrapezoidalEval(&pAxisMc->Axis.Context.trapezoidal, pAxisMc->Axis.CurrSet.fTime);// pAxisMc->Axis.CurrSet.idx* SYS_BASE_TIME_S);
                    //bPreSlowDown 逻辑
                    if ((pAxisMc->Axis.Context.trapezoidal.Ta_ + pAxisMc->Axis.Context.trapezoidal.Tv_) >= 5 * SYS_BASE_TIME_S)
                    {
                        if ((pAxisMc->Axis.CurrSet.fTime >= ((pAxisMc->Axis.Context.trapezoidal.Ta_ + pAxisMc->Axis.Context.trapezoidal.Tv_) - 5 * SYS_BASE_TIME_S))
                            && pAxisMc->Axis.CurrSet.fTime < (pAxisMc->Axis.Context.trapezoidal.Ta_ + pAxisMc->Axis.Context.trapezoidal.Tv_))
                        {
                            pAxisMc->Axis.Context.bPreSlowDown = true;
                        }
                        else
                        {
                            pAxisMc->Axis.Context.bPreSlowDown = false;
                        }
                    }
                    else
                    {
                        pAxisMc->Axis.Context.bPreSlowDown = true;
                    }

                    pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.Context.trapezoidal.step_.Y;
                    pAxisMc->Axis.DecPhase = (iret >= 3);
                    pAxisMc->Axis.Context.bSlowDown = (iret >= 3);
                    pAxisMc->Axis.Context.iretEval = iret;
                    pAxisMc->Axis.Context.sMotionPlanCmd[0].Busy = !pAxisMc->Axis.Context.trapezoidal.Done;
                    if (iret == 5 || iret == -1)
                    {
                        memset(&pAxisMc->Axis.Context.sMotionPlanCmd[0], 0, sizeof(SMotionPlanCmd));
                    }
                }
                //是否因为 fPosError计算时精度丢失 导致电机会出现 异常的
                pAxisMc->Axis.Status.fPosError = pAxisMc->Axis.LogicRef.fPos - pAxisMc->Axis.LogicFbk.fPosNoFilter;
                pAxisMc->Axis.Status.iPosError = pMotor->LinkVar.PosRef - pMotor->LinkVar.PosFbk;

                //规避驱动器上电静态出现咔哒声音
                if (fabs(pAxisMc->Axis.Status.fPosError) < 0.001f) {
                    pAxisMc->Axis.Status.fPosError = 0.0f;
                }

                if (abs(pAxisMc->Axis.Context.posPidCtrl.fMinAllowPosErr) < 0.001) //避免参数误设置运动规划异常
                    pAxisMc->Axis.Context.posPidCtrl.fMinAllowPosErr = 0.002;

                if (abs(pAxisMc->Axis.Status.fPosError) < abs(pAxisMc->Axis.Context.posPidCtrl.fMinAllowPosErr) &&
                    pAxisMc->Axis.Context.trapezoidal.Done)    //MinAllowPosErr 0.001
                {
                    bPosIsok = true;
                }
                else
                {
                    bPosIsok = false;
                }

                if (*pbJogMode)     //点动模式下 前馈设置成1  避免速度无法达到 设置的点动速度
                {
                    pAxisMc->Axis.LogicRef.fVel = Saturation(1 * pAxisMc->Axis.Context.trapezoidal.step_.Yd +
                        pAxisMc->Axis.Status.fPosError * pAxisMc->Axis.Context.posPidCtrl.kp,
                        -abs(pAxisMc->Axis.Context.trapezoidal.Vr_), abs(pAxisMc->Axis.Context.trapezoidal.Vr_));
                    pAxisMc->Axis.Busy = !(pAxisMc->Axis.Context.trapezoidal.Done && bPosIsok);
                }
                else
                {
                    pAxisMc->Axis.LogicRef.fVel = Saturation(pAxisMc->Axis.Context.posPidCtrl.feedForwardRatio * pAxisMc->Axis.Context.trapezoidal.step_.Yd +
                        pAxisMc->Axis.Status.fPosError * pAxisMc->Axis.Context.posPidCtrl.kp,
                        -abs(pAxisMc->Axis.Context.trapezoidal.Vr_), abs(pAxisMc->Axis.Context.trapezoidal.Vr_));

                    pAxisMc->Axis.Busy = !(pAxisMc->Axis.Context.trapezoidal.Done && bPosIsok);
                }

                //if (pAxisMc->Axis.Busy && pSysShareData->SeqAux.iActiveRowIndex==3)
                //{
                //    rt_dbgPrint(1, 0, "Counter:%llu fTime:%f Done:%s  RefVel:%f FbkVel:%f   Yd:%f\n",
                //        pSysShareData->gSysCounter,
                //        pAxisMc->Axis.CurrSet.fTime,
                //        pAxisMc->Axis.Context.trapezoidal.Done ? "true " : "false",
                //         pAxisMc->Axis.LogicRef.fVel,
                //         pAxisMc->Axis.LogicFbk.fVelNoFilter,
                //         pAxisMc->Axis.Context.trapezoidal.step_.Yd);
                //}
            }

            pAxisMc->Axis.Context.CstLastCmd.Vel = pAxisMc->Axis.LogicRef.fVel;
            pAxisMc->Axis.LogicRef.fAcc = (pAxisMc->Axis.LogicRef.fVel - pAxisMc->Axis.Context.lastLogicRefVel) / pAxisMc->Axis.SystemPeriod;
            pAxisMc->Axis.Motor.LinkVar.VelRef = pAxisMc->Axis.LogicRef.fVel / pAxisMc->Axis.Cfg.Tranlatepara * pAxisMc->Axis.Cfg.ShaftPlusePerRevolution;
            pAxisMc->Axis.Motor.LinkVar.PosRef = (int32)((pAxisMc->Axis.LogicRef.fPos - pAxisMc->Axis.Cfg.LogicPositionOffset)* pAxisMc->Axis.Cfg.ShaftPlusePerRevolution / pAxisMc->Axis.Cfg.Tranlatepara);
        }
        break;
        default:
            rt_dbgPrint(1, 0, "pAxis->Motor.LinkVar.ModeOfOp not CST_Pos. Error \n");
            break;
        }
    }
    else
    {
        pAxisMc->Axis.Busy = false;
    }

    pAxisMc->Axis.LogicFbk.fTorque = ((float32)pMotor->LinkVar.TorqueFbk_Percent / 1000.0)* pAxisMc->Axis.Motor.fMotorRatedTorque;// pAxisMc->Axis.Cfg.CalibTrqWithNormalCurr* pMotor->LinkVar.TorqueFbk / 1000.0;/// pAxis->Cfg.Tranlatepara;
    pAxisMc->Axis.LogicFbk.fCurrent = ((float32)pSysShareData->AxisMc[0].Axis.Motor.LinkVar.CurrentFbk)/1000.0;
    pAxisMc->Axis.LogicRef.fAcc = (pAxisMc->Axis.LogicRef.fVel - pAxisMc->Axis.Context.lastLogicRefVel) / pAxisMc->Axis.SystemPeriod;
    pAxisMc->Axis.LogicRef.fJerk = (pAxisMc->Axis.LogicRef.fAcc - pAxisMc->Axis.Context.lastLogicRefAcc) / pAxisMc->Axis.SystemPeriod;

    pMotor->LastPosFbk = pMotor->LinkVar.PosFbk;
    pAxisMc->Axis.Context.lastLogicFbkVel = pAxisMc->Axis.LogicFbk.fVel;
    pAxisMc->Axis.Context.lastLogicRefPos = pAxisMc->Axis.LogicRef.fPos;
    pAxisMc->Axis.Context.lastLogicRefVel = pAxisMc->Axis.LogicRef.fVel;
    pAxisMc->Axis.Context.lastLogicRefAcc = pAxisMc->Axis.LogicRef.fAcc;

    pAxisMc->Axis.Context.lastCW = pMotor->LinkVar.CW;
    pAxisMc->Axis.Context.lastSW = pMotor->LinkVar.SW;
}
