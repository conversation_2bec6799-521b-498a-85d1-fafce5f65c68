#include "SeqItemWait.h"
#include "Sequence.h"
#include "SequenceItem.h"
#include <stdio.h>
#include "rttimer.h"
#include "SysShareMemoryDefine.h"
#include "DynaVar.h"
/* SeqItem_Wait_Start
* @param[in]     SeqItem* pSeqItem
* @return        ErrorInfo
*/
ErrorInfo SeqItem_Wait_Start(SeqItem* pSeqItem)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem_WaitCfg* pCfg = (SeqItem_WaitCfg*)(pSeqItem->pCfg);
	switch (pCfg->eWaitType)
	{
	case WaitConstTime:
		errorInfo = UpdateDynaVar(&pCfg->uWaitContext.sWaitTime.dVarWaitTime);
		pCfg->uWaitContext.sWaitTime.bHadInitTime = false;
		if (pCfg->uWaitContext.sWaitTime.dVarWaitTime.fVarSwitched < 0)		//WaitConstTime等待时间为负数时，报错
		{
			errorInfo.ErrCode = 24341;
			errorInfo.eErrLever = Error;
			pCfg->uWaitContext.sWaitTime.dVarWaitTime.fVarSwitched = 0;
		}
		break;
	case WaitForContinue:
		for (uint8 i = 0; i < 8; i++)
		{
			pCfg->uWaitContext.sWaitSingal.bContinueSingal[i] = false;
			pCfg->uWaitContext.sWaitSingal.bWaitSingal[i] = false;
		}
		pCfg->uWaitContext.sWaitSingal.bWaitSingal[pCfg->uWaitContext.sWaitSingal.WaitContinueIndex] = true;
		pSeqItem->pSeqRef->WaitSingal = 1 << pCfg->uWaitContext.sWaitSingal.WaitContinueIndex;
		rt_dbgPrint(1, 2, "WaitSingal:%d\n", pSeqItem->pSeqRef->WaitSingal);
		break;
	default:
		break;
	}
	((SeqItem*)pSeqItem)->pExeActor->bBusy = true;

	ErrorInfoPack(&errorInfo, "SeqItem_Wait_Start", "");
	return errorInfo;
}


/* SeqItem_Wait_Execute
* @param[in]     SeqItem* pSeqItem
* @return        ErrorInfo
*/
uint64 uWaitTestCounter = 0;
ErrorInfo SeqItem_Wait_Execute(SeqItem* pSeqItem)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem_WaitCfg* pCfg = (SeqItem_WaitCfg*)(pSeqItem->pCfg);
	uWaitTestCounter++;
	switch (pCfg->eWaitType)
	{
	case WaitConstTime:
		if (!pCfg->uWaitContext.sWaitTime.bHadInitTime)
		{
			pCfg->uWaitContext.sWaitTime.bHadInitTime = true;
			clock_gettime_nS(&pCfg->uWaitContext.sWaitTime.initTimestamp);
			//rt_dbgPrint(1, 2, "Init uWaitTestCounter:%ld gSysCounter:%ld\n", uWaitTestCounter, pSysShareData->gSysCounter);
		}
		else
		{
			clock_gettime_nS(&pCfg->uWaitContext.sWaitTime.currTimestamp);
			if ((pCfg->uWaitContext.sWaitTime.currTimestamp - pCfg->uWaitContext.sWaitTime.initTimestamp) >=
				((uint64)(pCfg->uWaitContext.sWaitTime.dVarWaitTime.fVarSwitched * 1000000000.0)))
			{
				//rt_dbgPrint(1, 2, "Finish uWaitTestCounter:%ld gSysCounter:%ld\n", uWaitTestCounter, pSysShareData->gSysCounter);
				SeqExeItemExit(pSeqItem->pExeActor, errorInfo);
				pCfg->uWaitContext.sWaitTime.bHadInitTime = false;  //多次进入wait项后重置init
			}
		}

		break;
	case WaitForContinue:
		//if (pCfg->uWaitContext.sWaitSingal.bContinueSingal[pCfg->uWaitContext.sWaitSingal.WaitContinueIndex])
		if (pSeqItem->pSeqRef->ContinueSingal & (1 << pCfg->uWaitContext.sWaitSingal.WaitContinueIndex))
		{
			pSeqItem->pSeqRef->WaitSingal = 0;  //同时不可能来多个wait   此次可以直接给0
			SeqExeItemExit(pSeqItem->pExeActor, errorInfo);
		}
		break;
	default:
		break;
	}

	ErrorInfoPack(&errorInfo, "SeqItem_Wait_Execute", "");
	return errorInfo;
}
