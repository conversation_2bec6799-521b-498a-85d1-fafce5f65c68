#ifndef _MOTION_H_
#define _MOTION_H_
#include <stdbool.h>
#include "base.h"
#include "Sensor.h"
#include "driver.h"

#pragma pack(1)

typedef enum _PlanMode
{
    PosPlanMode = 0,
    VelPlanMode =1,
}PlanMode;
typedef enum _JogMode
{
    VleocityMode = 0,
    PositonMode = 1
}JogMode;
#pragma pack()

#pragma pack(1)
typedef enum _JogType
{
    Motion_JogForward      = 3,
    Motion_JogBackward     = 4,
    Motion_JogHome         = 5,
    Motion_JogRefPos       = 6,
    Motion_JogTargetVar    = 7
}JogType;
#pragma pack()

#pragma pack(1)
typedef struct
{
    float32		fHomePos;
    float32		fProfileHomeVelocity;
    float32		fProfileHomeForceMax;
    float32		fProfileHomeForceMin;
    float32		fCustomVar;
    float32		fVelocityMax;	//Max 10

    bool		bJogCustomCmd = false;
    bool		bProfileHomeJogCmd = false;
    JogMode		eJogMoveMode = VleocityMode;
    float32		fVelocity = 3;
    float32		fStepDistance = 0.2;
    float32		fJogAcc;
    float32		fJogJeck;

    str64		sCustomChannelName = "Potition";
    float32		fPositionMax;
    float32		fPositionMin;
    float32		fForceMax;
    float32		fForceMin;
    float32		fExtPosMax;
    float32		fExtPosMin;
}SysJogPara;
#pragma pack()


extern bool gPrintMotionDetail;
extern bool gMcInScpMode;
extern uint64 gMaxMcMoveLelativeCalTime_nS;
extern float32 fLastForceFbk;
extern bool bPosIsok;
extern bool bPidMove;
extern float32 fAimPos;
void MC_Stop(Axis_Para* pAxis);// float Deceleration);
void MC_PowerUp(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback);
void MC_PowerDown(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback);

ErrorInfo  MC_JogErrorCheck(float32* pfAimPos, Axis_Para* pAxis, JogType eOrderType);
ErrorInfo MC_JogOrder(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback, uint8 eManualOrder,bool bExplorerControl);

void MC_ResetFault(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback);

int16 MC_CmdExeResult(Axis_Para* pAxis);
void MC_ParaInit(Axis_Para* pAxis);
 
void MC_ServoCaculation(sAxisMcPara* pAxisMc,bool* pbJogMode);
bool MC_IsPowerUp(Axis_Para* pAxis);
bool MC_IsStandStill(Axis_Para* pAxis);

bool MC_IsServoInError(Axis_Para* pAxis);
ErrorInfo MC_ServoErrorCode(Axis_Para* pAxis);

bool MC_MoveCmdFinished(Axis_Para* pAxis);
bool MC_CmdExeErr(Axis_Para* pAxis);

ErrorInfo MC_MoveAbsPos(Axis_Para* pAxis, float32 Pos, float32 VelMax, float32 Acc, EBuffermode eCmdBuffMode);
ErrorInfo MC_MoveRelativePos(Axis_Para* pAxis,  float32 Distance, float32 Vel, float32 Acc, EBuffermode eCmdBuffMode);
void MC_SwitchCtrlMode(Axis_Para* pAxis, EServoCtrlMode servoCtrlMode, ExecuteFeedback* psExeFeedback);
extern ErrorInfo MC_Tare(sAxisMcPara* pAxisMcPara, TrigData* pMotorTareTrig);

extern ExecuteFeedback			sPowerUpExefeed;
extern ExecuteFeedback			sPowerDownExefeed;
extern ExecuteFeedback			sJogMoveExefeed;
extern ExecuteFeedback          sResetFaultExeFeed;
extern bool                     bJogMode;
extern bool bAppJogHomeCmd;
#endif
