#include "SeqItemSetValue.h"
#include "Sequence.h"
#include "SequenceItem.h"
#include "DynaVar.h"
#include <stdio.h>
#include "rttimer.h"
#include "SysShareMemoryDefine.h"

/* SeqItem_SetValue_Start              
* @param[in]     SeqItem* pSeqItem
* @return        ErrorInfo
*/
bool bWaitSetVarOk = false;
ErrorInfo SeqItem_SetValue_Start(SeqItem* pSeqItem)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem_SetValueCfg* pCfg = (SeqItem_SetValueCfg*)(pSeqItem->pCfg);
	bWaitSetVarOk = false;
	if (pCfg->uSetItemCount > 0)
	{
		for (uint8 i = 0; i < pCfg->uSetItemCount; i++)
		{
			errorInfo = UpdateDynaVar(&pCfg->sSetValueContext[i].dyTargtY);
			switch (pCfg->sSetValueContext[i].eSetType)
			{
			case Y_X:
			{
				if (pCfg->sSetValueContext[i].dyX1.eType != DynaConst)
					errorInfo = UpdateDynaVar(&pCfg->sSetValueContext[i].dyX1);
			}
			break;
			case Y_X1_X2:
			{
				if (pCfg->sSetValueContext[i].dyX1.eType != DynaConst)
					errorInfo = UpdateDynaVar(&pCfg->sSetValueContext[i].dyX1);

				if (pCfg->sSetValueContext[i].dyX2.eType != DynaConst)
					errorInfo = UpdateDynaVar(&pCfg->sSetValueContext[i].dyX2);
			}
			break;
			default:
				break;
			}
			
			if (errorInfo.ErrCode)
				break;
		}
	}
	ErrorInfoPack(&errorInfo, "SeqItem_SetValue_Start", "");
	return errorInfo;
}


/* SeqItem_SetValue_Execute
* @param[in]     SeqItem* pSeqItem
* @return        ErrorInfo
*/
static const char* eOpType[] = { "+", "-", "*" ,"/","=","!=",">",">=","<","<="};

bool bPrintfSetDebugInfo = false;
ErrorInfo SetVarerrInfo;
ErrorInfo SeqItem_SetValue_Execute(SeqItem* pSeqItem)
{
	SeqItem_SetValueCfg* pCfg = (SeqItem_SetValueCfg*)(pSeqItem->pCfg);

	if (!bWaitSetVarOk)
	{
		memset(&SetVarerrInfo, 0, sizeof(ErrorInfo));
		pSeqItem->pExeActor->bBusy = true;

		for (uint8 i = 0; i < pCfg->uSetItemCount; i++)
		{
			switch (pCfg->sSetValueContext[i].eSetType)
			{
			case Y_X:
			{
				//SetDynaVar(&pCfg->sSetValueContext[i].dyTargtY, &pCfg->sSetValueContext[i].dyX1);
				if (pCfg->sSetValueContext[i].dyX1.eType == DynaConst)
				{
					SetDynaVar(&pCfg->sSetValueContext[i].dyTargtY, &pCfg->sSetValueContext[i].dyX1);
				}
				else
				{
					SetDynaVar(&pCfg->sSetValueContext[i].dyTargtY, &pCfg->sSetValueContext[i].dyX1);
				}
			}
			break;
			case Y_X1_X2:
			{
				float64 lfY;
				SetVarerrInfo = UpdateDynaVar(&pCfg->sSetValueContext[i].dyX1);

				if (!SetVarerrInfo.ErrCode)
				{
					SetVarerrInfo = UpdateDynaVar(&pCfg->sSetValueContext[i].dyX2);
				}

				if (!SetVarerrInfo.ErrCode)
				{
					if (bPrintfSetDebugInfo)
					{
						dbgPrint(1, 1, "dyX1 fVarSwitched:%f \n",
							pCfg->sSetValueContext[i].dyX1.lfVarSwitched);

						dbgPrint(1, 1, "dyX1 fVarSwitched:%f\n",
							pCfg->sSetValueContext[i].dyX2.lfVarSwitched);
					}

					if (pCfg->sSetValueContext[i].dyTargtY.eDataType > DT_None &&
						pCfg->sSetValueContext[i].dyTargtY.eDataType < DT_Str)
					{
						switch (pCfg->sSetValueContext[i].eValueOpType)
						{
						case ADD:				// +
							lfY = pCfg->sSetValueContext[i].dyX1.lfVarSwitched + pCfg->sSetValueContext[i].dyX2.lfVarSwitched;
							break;
						case SUB:				// -
							lfY = pCfg->sSetValueContext[i].dyX1.lfVarSwitched - pCfg->sSetValueContext[i].dyX2.lfVarSwitched;
							break;
						case MUL:				// *
							lfY = pCfg->sSetValueContext[i].dyX1.lfVarSwitched * pCfg->sSetValueContext[i].dyX2.lfVarSwitched;
							break;
						case DIV:				// /
							lfY = pCfg->sSetValueContext[i].dyX1.lfVarSwitched / pCfg->sSetValueContext[i].dyX2.lfVarSwitched;
							break;
						case Equal:				//  = 
							if (abs(pCfg->sSetValueContext[i].dyX1.lfVarSwitched - pCfg->sSetValueContext[i].dyX2.lfVarSwitched) <= 0.000001)
								lfY = 1.0;
							else
								lfY = 0.0;
							break;
						case Unequal:			//  !=
							if (abs(pCfg->sSetValueContext[i].dyX1.lfVarSwitched - pCfg->sSetValueContext[i].dyX2.lfVarSwitched) > 0.000001)
								lfY = 1.0;
							else
								lfY = 0.0;
							break;
						case Greater:			//  >
							if (pCfg->sSetValueContext[i].dyX1.lfVarSwitched > pCfg->sSetValueContext[i].dyX2.lfVarSwitched)
								lfY = 1.0;
							else
								lfY = 0.0;
							break;
						case Equal_Greater:		//  >=
							if (pCfg->sSetValueContext[i].dyX1.lfVarSwitched >= pCfg->sSetValueContext[i].dyX2.lfVarSwitched)
								lfY = 1.0;
							else
								lfY = 0.0;
							break;
						case Less:				//  <
							if (pCfg->sSetValueContext[i].dyX1.lfVarSwitched < pCfg->sSetValueContext[i].dyX2.lfVarSwitched)
								lfY = 1.0;
							else
								lfY = 0.0;
							break;
						case Less_Equal:		//  <=
							if (pCfg->sSetValueContext[i].dyX1.lfVarSwitched <= pCfg->sSetValueContext[i].dyX2.lfVarSwitched)
								lfY = 1.0;
							else
								lfY = 0.0;
							break;
						case NoneOpType:
						default:
							SetVarerrInfo.ErrCode = 24072;
							SetVarerrInfo.eErrLever = Error;
							break;
						}

						SetVarerrInfo = F64ToDynaVar(&pCfg->sSetValueContext[i].dyTargtY, &lfY);
					}
					else if (pCfg->sSetValueContext[i].dyTargtY.eDataType == DT_Str || pCfg->sSetValueContext[i].dyTargtY.eDataType == DT_WStr)
					{
						memset(pCfg->sSetValueContext[i].dyTargtY.VarUnion.sVar, 0, 255);
						switch (pCfg->sSetValueContext[i].eValueOpType)
						{
						case ADD:				// +
							if ((strlen(pCfg->sSetValueContext[i].dyX1.VarUnion.sVar) + strlen(pCfg->sSetValueContext[i].dyX1.VarUnion.sVar)) < 255 &&
								(strlen(pCfg->sSetValueContext[i].dyX1.VarUnion.sVar) + strlen(pCfg->sSetValueContext[i].dyX1.VarUnion.sVar)) > 0)
							{
								strcat(pCfg->sSetValueContext[i].dyTargtY.VarUnion.sVar, pCfg->sSetValueContext[i].dyX1.VarUnion.sVar);
								strcat(pCfg->sSetValueContext[i].dyTargtY.VarUnion.sVar, pCfg->sSetValueContext[i].dyX2.VarUnion.sVar);
							}
							else if ((strlen(pCfg->sSetValueContext[i].dyX1.VarUnion.sVar) + strlen(pCfg->sSetValueContext[i].dyX1.VarUnion.sVar)) > 255)
							{
								;//error
							}
							break;
						case Equal:				//  = 
							if (strcmp(pCfg->sSetValueContext[i].dyX1.VarUnion.sVar, pCfg->sSetValueContext[i].dyX1.VarUnion.sVar) == 0)
							{
								strcat(pCfg->sSetValueContext[i].dyTargtY.VarUnion.sVar, pCfg->sSetValueContext[i].dyX1.VarUnion.sVar);
							}
							else
							{
								strcat(pCfg->sSetValueContext[i].dyTargtY.VarUnion.sVar, "false");
							}
							break;
						case Unequal:			//  !=
							if (strcmp(pCfg->sSetValueContext[i].dyX1.VarUnion.sVar, pCfg->sSetValueContext[i].dyX1.VarUnion.sVar) != 0)
							{
								strcat(pCfg->sSetValueContext[i].dyTargtY.VarUnion.sVar, "true");
							}
							else
							{
								strcat(pCfg->sSetValueContext[i].dyTargtY.VarUnion.sVar, "false");
							}
							break;
						case SUB:				// -
						case MUL:				// *
						case DIV:				// /
						case NoneOpType:
						default:
							SetVarerrInfo.ErrCode = 25014;
							SetVarerrInfo.eErrLever = Error;
							break;
						}
					}
					else
					{
						;
					}
				}
				else
				{
					SeqExeItemExit(pSeqItem->pExeActor, SetVarerrInfo);
				}
			}
			break;
			default:
				break;
			}
		}
		bWaitSetVarOk = true;
	}
	else
	{
		SetVarerrInfo = UpdateDynaVar(&pCfg->sSetValueContext[0].dyTargtY);

		if (pCfg->sSetValueContext[0].eSetType == Y_X)
		{
			if (pCfg->sSetValueContext[0].dyTargtY.eDataType != DT_Str &&
				pCfg->sSetValueContext[0].dyTargtY.eDataType != DT_WStr)
			{
				dbgPrint(1, 0, "SetValue: Y[%f] = X1[%f]\n",
					pCfg->sSetValueContext[0].dyTargtY.lfVarSwitched,
					pCfg->sSetValueContext[0].dyX1.lfVarSwitched);
			}
			else
			{
				dbgPrint(1, 0, "SetValue: Y[%s] = X1[%s]\n",
					pCfg->sSetValueContext[0].dyTargtY.VarUnion.sVar,
					pCfg->sSetValueContext[0].dyX1.VarUnion.sVar);
			}
		}
		else
		{
			dbgPrint(1, 0, "SetValue: Y[%f] = X1[%f] -%s-  X2[%f]\n",
				pCfg->sSetValueContext[0].dyTargtY.lfVarSwitched,
				pCfg->sSetValueContext[0].dyX1.lfVarSwitched,
				eOpType[pCfg->sSetValueContext[0].eValueOpType - 1],
				pCfg->sSetValueContext[0].dyX2.lfVarSwitched);
		}

		SeqExeItemExit(pSeqItem->pExeActor, SetVarerrInfo);
	}

	ErrorInfoPack(&SetVarerrInfo, "SeqItem_SetValue_Execute", "");
	return SetVarerrInfo;
}
