#include "core.h"
#include "SequenceItem.h"
#include "SeqItemWait.h"
#include "SeqItemMotion.h"

sErrCodeJumpTarget* findErrJumpTarget(sErrCodeJumpTarget** vList, int ArrLength, uint16 errCode){
//	dbgPrint(1, 0, "errCode = %d.\n",errCode);
	for(int i=0;i<ArrLength;i++){
//		dbgPrint(1, 0, "find error = %d.\n",vList[i]->ErrCode);
		if(vList[i]->ErrCode ==  errCode){
			return vList[i];
		}
	}
	return 0;
}



sErrCodeJumpTarget* findErrJumpTarget_1(sErrCodeJumpTarget *vList, int ArrLength, uint16 errCode){
//	dbgPrint(1, 0, "errCode = %d.\n",errCode);
	for(int i=0;i<ArrLength;i++){
		if((vList+i)->ErrCode == errCode){
			return vList+i;
		}
//		dbgPrint(1, 0, "find error = %d.\n",vList[i]->ErrCode);
//		if(vList[i]->ErrCode ==  errCode){
//			return vList[i];
//		}
	}
	return 0;
}


void SeqExeItemExit(ExeActor* pExeActor,ErrorInfo errInfo)
{
    pExeActor->bBusy = false;
    pExeActor->bExecuted = true;
    pExeActor->errorCode = errInfo.ErrCode;
    bool bFinished = false;
   
    //清空动态数据
    if (pExeActor->pSeqItem->eItemType == Motion)
    {
        SeqItem_MotionCfg* pMotion = (SeqItem_MotionCfg*)pExeActor->pSeqItem->pCfg;
        McRunContext* mcRunContext = &pMotion->context;
        //pExeActor->pSeqItem->pSeqRef->pAxisMc->Axis.Context.dLastCmdTargetPos = mcRunContext->fTargetPos;
        //rt_dbgPrint(1, 1, "SeqExeItemExit dLastCmdTargetPos:%f addr:%ld\n",
        //    pExeActor->pSeqItem->pSeqRef->pAxisMc->Axis.Context.dLastCmdTargetPos,
        //    &pExeActor->pSeqItem->pSeqRef->pAxisMc->Axis.Context.dLastCmdTargetPos);
    }
    else if (pExeActor->pSeqItem->eItemType == Condition)
    {
        if (strcasecmp(pExeActor->pSeqItem->szType, "WaitNotify") == 0)              //0x14:Wait
        {
            pExeActor->pSeqItem->pSeqRef->WaitSingal = 0;  //同时不可能来多个wait   此次可以直接给0
            //dbgPrint(1, 0, "SeqExeItemExit WaitSingal\n");
        }

        // 检查是否为MeasureStop项，如果是则设置bStopRequest标志
        if (strcmp(pExeActor->pSeqItem->szType, "MeasureStop") == 0)     //手动跳工艺，允许跳过MeasureStop项
        {
            pExeActor->pSeqItem->pSeqRef->bStopRequest = true;
        }
    }

    if(errInfo.ErrCode > 0)
    {
    	sErrCodeJumpTarget* pJumpTarget;
//    	dbgPrint(1, 0, " nErrCodeJumpTargetNum = %d\n",pExeActor->nErrCodeJumpTargetNum);
    	if(pExeActor->nErrCodeJumpTargetNum > 0){
    		pJumpTarget = findErrJumpTarget(pExeActor->vErrCodeJumpTarget, pExeActor->nErrCodeJumpTargetNum, errInfo.ErrCode);
    		if(pJumpTarget)
            {
    			bFinished = true;
    			pExeActor->iJumpTarget = pJumpTarget->JumpTargetIdx;
    			return;
    		}
    	}
    	if(!bFinished)
        {
    		SeqRef* pSeqRef = pExeActor->pSeqItem->pSeqRef;
//    		dbgPrint(1, 0, " nDefaultErrCodeJumpTargetNum = %d\n",pSeqRef->nDefaultErrCodeJumpTargetNum);
    		pJumpTarget = findErrJumpTarget_1((sErrCodeJumpTarget*)&pSeqRef->vDefaultErrCodeJumpTarget , pSeqRef->nDefaultErrCodeJumpTargetNum, errInfo.ErrCode);
    		if(pJumpTarget){
    			pExeActor->iJumpTarget = pJumpTarget->JumpTargetIdx;
    		}else{
    			pJumpTarget = findErrJumpTarget_1((sErrCodeJumpTarget*)&pSeqRef->vDefaultErrCodeJumpTarget , pSeqRef->nDefaultErrCodeJumpTargetNum, -1);
    			if(pJumpTarget){
    				pExeActor->iJumpTarget = pJumpTarget->JumpTargetIdx;
    			}else{
    				pExeActor->iJumpTarget = -1;
    			}
    		}
    	}
    }else{
    	pExeActor->iJumpTarget = -1;
    }
}
