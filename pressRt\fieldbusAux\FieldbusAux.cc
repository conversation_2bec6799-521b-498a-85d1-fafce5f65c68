#include "base.h"
#include "FieldbusAux.h"
#include "SysShareMemoryDefine.h"
#include "SysVarDefine.h"
#include "Global&LocalVar.h"


/* ChangeEndian     大小端转换
* @param[in]        byte* pData
* @param[in]        uint8 uDataLen
* @return           void
*/
uint16 changeCount = 0;
uint16 changeIndex = 0;
byte* pUdint0; byte* pUdint1; byte* pUdint2; byte* pUdint3;
byte TempByte;
void ChangeEndian32(byte* pData, uint16* pUDataLen)
{
	changeCount = 0;
	if (*pUDataLen > 1)
	{
		changeCount = *pUDataLen / 4;
		if (changeCount > 0)
		{
			for (changeIndex = 0; changeIndex < changeCount; changeIndex++)
			{
				pUdint0 = pData + changeIndex * 4;
				pUdint1 = pUdint0 + 1;
				pUdint2 = pUdint0 + 2;
				pUdint3 = pUdint0 + 3;

				TempByte = *pUdint0;
				*pUdint0 = *pUdint3;
				*pUdint3 = TempByte;

				TempByte = *pUdint1;
				*pUdint1 = *pUdint2;
				*pUdint2 = TempByte;
			}
		}
	}
}

/* ChangeEndian     大小端转换
* @param[in]        byte* pData
* @param[in]        uint8 uDataLen
* @return           void
*/
void ChangeEndian64(byte* pData, uint16* pUDataLen)
{
	changeCount = 0;
	if (*pUDataLen == 8)    //0 7、1 6、2 5、3 4
	{
		TempByte = *pData;
		*pData = *(pData + 7);
		*(pData + 7) = TempByte;

		TempByte = *(pData + 1);
		*(pData + 1) = *(pData + 6);
		*(pData + 6) = TempByte;

		TempByte = *(pData + 2);
		*(pData + 2) = *(pData + 5);
		*(pData + 5) = TempByte;

		TempByte = *(pData + 3);
		*(pData + 3) = *(pData + 4);
		*(pData + 4) = TempByte;
	}
}


/* ResolveFbData    解析收到的总线数据和按照配置打包发出去的总线数据
* @param[in]        None
* @param[in]        None
* @return           ErrorInfo
*/
uint8 IndexFb;
bool bFbPf = true;
byte aTmpbyteBuff[200];
uint16 UnitDataLen = 0;
SFieldbusConfigUnit* pConfigUint = NULL;
SDOEntryDesc* pSdoEntry;
byte tmpByte;
SFieldbusConfigUnit sTmpFbCofigUnit;
ErrorInfo ResolveFbData()
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, (uint64_t)sizeof(ErrorInfo));
	//In  Cw
	if (pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount > 0 &&
		pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount <= FieldbusConfigMaxNumber)
	{
		for (IndexFb = 0; IndexFb < pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount; IndexFb++)
		{
			pConfigUint = &pSysShareData->sSysFieldbusInfo.sFieldbusInput.sFieldbusUnit[IndexFb];
			if (pConfigUint != NULL && pConfigUint->sName != NULL)
			{
				if (pConfigUint->bGlobalOrLocalVar)     //如果是全局变量或局部变量 需要去查 查看是否存在对应的变量
				{
					memset(&sTmpFbCofigUnit, 0, sizeof(SFieldbusConfigUnit));
					if (!FindGlobalLocalVar(pConfigUint, &sTmpFbCofigUnit))
					{
						errorInfo.eErrLever = Error;
						errorInfo.ErrCode = 170;
					}
				}

				if (!errorInfo.ErrCode)
				{
					if (sTmpFbCofigUnit.eDataType == DT_Str || sTmpFbCofigUnit.eDataType == DT_WStr)
					{
						memset(&aTmpbyteBuff[0], 0, 200);
						UnitDataLen = Min(sTmpFbCofigUnit.Size, 200 - sTmpFbCofigUnit.AddrByte);
						memcpy(&aTmpbyteBuff[0], &pSysShareData->sFbRec.aDataBuff[sTmpFbCofigUnit.AddrByte - 20], UnitDataLen);
					}
					else if (sTmpFbCofigUnit.eDataType == DT_I32 ||
						sTmpFbCofigUnit.eDataType == DT_U32 ||
						sTmpFbCofigUnit.eDataType == DT_F32)
					{
						memset(&aTmpbyteBuff[0], 0, 200);
						UnitDataLen = Min(DATATYPE_SIZE[sTmpFbCofigUnit.eDataType], 200 - sTmpFbCofigUnit.AddrByte);
						memcpy(&aTmpbyteBuff[0], &pSysShareData->sFbRec.aDataBuff[sTmpFbCofigUnit.AddrByte - 20], UnitDataLen);

						if (pSysShareData->sSysFieldbusInfo.sBaseSet.eByteOrder == ELittleOrder)
							ChangeEndian32(&aTmpbyteBuff[0], &UnitDataLen);
					}
					else if (sTmpFbCofigUnit.eDataType == DT_U16 || sTmpFbCofigUnit.eDataType == DT_I16)
					{
						memset(&aTmpbyteBuff[0], 0, 200);
						UnitDataLen = Min(sTmpFbCofigUnit.Size, 200 - sTmpFbCofigUnit.AddrByte);
						memcpy(&aTmpbyteBuff[0], &pSysShareData->sFbRec.aDataBuff[sTmpFbCofigUnit.AddrByte - 20], UnitDataLen);

						if (pSysShareData->sSysFieldbusInfo.sBaseSet.eByteOrder == ELittleOrder)
						{
							byte tmpByte = aTmpbyteBuff[0];
							aTmpbyteBuff[0] = aTmpbyteBuff[1];
							aTmpbyteBuff[1] = tmpByte;
						}
					}
					else if (sTmpFbCofigUnit.eDataType == DT_I64 ||
						sTmpFbCofigUnit.eDataType == DT_U64 ||
						sTmpFbCofigUnit.eDataType == DT_F64)
					{
						memset(&aTmpbyteBuff[0], 0, 200);
						UnitDataLen = Min(DATATYPE_SIZE[sTmpFbCofigUnit.eDataType], 200 - sTmpFbCofigUnit.AddrByte);
						memcpy(&aTmpbyteBuff[0], &pSysShareData->sFbRec.aDataBuff[sTmpFbCofigUnit.AddrByte - 20], UnitDataLen);

						if (pSysShareData->sSysFieldbusInfo.sBaseSet.eByteOrder == ELittleOrder)
							ChangeEndian64(&aTmpbyteBuff[0], &UnitDataLen);
					}
					else
					{
						memset(&aTmpbyteBuff[0], 0, 200);
						UnitDataLen = Min(DATATYPE_SIZE[sTmpFbCofigUnit.eDataType], 200 - sTmpFbCofigUnit.AddrByte);
						memcpy(&aTmpbyteBuff[0], &pSysShareData->sFbRec.aDataBuff[sTmpFbCofigUnit.AddrByte - 20], UnitDataLen);
					}

					if (sTmpFbCofigUnit.AddrByte >= 20 &&
						sTmpFbCofigUnit.AddrByte <= 220 &&
						strlen((const char*)sTmpFbCofigUnit.sSdoKey) > 1)
					{
						pSdoEntry = GetEntryByKey(sTmpFbCofigUnit.sSdoKey);
						if (pSdoEntry != NULL)          //数据类型错误会导致 崩溃
						{
							if (pSdoEntry->DataType == sTmpFbCofigUnit.eDataType)
							{
								errorInfo = SetSdoValueByKey(sTmpFbCofigUnit.sSdoKey,
									&aTmpbyteBuff[0],
									UnitDataLen);
							}

							else
							{
								errorInfo.eErrLever = Error;
								errorInfo.ErrCode = 171;
							}
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 170;
						}
					}
					else
					{
						errorInfo.eErrLever = Error;
						errorInfo.ErrCode = 140;
					}
				}
			}
			else
			{
				errorInfo.eErrLever = Error;
				errorInfo.ErrCode = 170;
			}


			if (errorInfo.ErrCode)
			{
				if (bFbPf)
				{
					rt_dbgPrint(1, 0, "FbIn ErrCode:%d uUnitCount:%d IndexFb:%d sSdoKey:%s  AddrByte:%d eDataType:%s Len:%d\n",
						errorInfo.ErrCode,
						pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount,
						IndexFb,
						sTmpFbCofigUnit.sSdoKey != NULL ? sTmpFbCofigUnit.sSdoKey : "sSdoKey == NULL",
						sTmpFbCofigUnit.AddrByte,
						&EDataTypeName[sTmpFbCofigUnit.eDataType],
						DATATYPE_SIZE[sTmpFbCofigUnit.eDataType]);
					bFbPf = false;
				}
				break;
			}
		}
	}

	//Out Sw
	ErrorInfoPack(&errorInfo, (char*)"ResolveFbData", "");
	return errorInfo;
}

/* PackFbData    解析收到的总线数据和按照配置打包发出去的总线数据
* @param[in]     None
* @param[in]     None
* @return        ErrorInfo
*/
int iGetDataLength = 0;
int iGetCapacity = 0;
byte tmpBuff[256];


ErrorInfo PackFbData()
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));
	//In  Cw
	memset(&pSysShareData->sFbSend.aDataBuff[0], 0, 200);
	if (pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount > 0 &&
		pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount <= FieldbusConfigMaxNumber)
	{
		for (IndexFb = 0; IndexFb < pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount; IndexFb++)
		{
			pConfigUint = &pSysShareData->sSysFieldbusInfo.sFieldbusOutput.sFieldbusUnit[IndexFb];
			if (pConfigUint != NULL && pConfigUint->sName != NULL)
			{
				if (pConfigUint->bGlobalOrLocalVar)     //如果是全局变量或局部变量 需要去查 查看是否存在对应的变量
				{
					memset(&sTmpFbCofigUnit, 0, sizeof(SFieldbusConfigUnit));
					if (!FindGlobalLocalVar(pConfigUint, &sTmpFbCofigUnit))
					{
						errorInfo.eErrLever = Error;
						errorInfo.ErrCode = 170;
					}
				}
				else
				{
					memset(&sTmpFbCofigUnit, 0, sizeof(SFieldbusConfigUnit));
					memcpy(&sTmpFbCofigUnit, pConfigUint, sizeof(SFieldbusConfigUnit));
				}

				if (sTmpFbCofigUnit.AddrByte >= 20 &&
					sTmpFbCofigUnit.AddrByte <= 220)
				{
					memset(&tmpBuff[0], 0, 256);
					iGetCapacity = (220 - sTmpFbCofigUnit.AddrByte + 1);

					pSdoEntry = GetEntryByKey(sTmpFbCofigUnit.sSdoKey);
					if (pSdoEntry != NULL && pSdoEntry->Name != NULL && sTmpFbCofigUnit.sName != NULL)
					{
						if (sTmpFbCofigUnit.bGlobalOrLocalVar &&
							strcmp(pSdoEntry->Name, sTmpFbCofigUnit.sName) != 0)          //只有全局变量和局部变量需要比较sdo和name，避免全局变量挪位置后sdo没根据位置更新
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 161;
							if (pSysShareData->gSysCounter % 5000 == 0)
								dbgPrint(1, 0, "FbOut ErrCode:%d bGlobalOrLocalVar:%s sSdoKey:%s pConfigUintName:%s SdoName:%s\n",
									errorInfo.ErrCode,
									sTmpFbCofigUnit.bGlobalOrLocalVar ? "true " : "false",
									sTmpFbCofigUnit.sSdoKey,
									sTmpFbCofigUnit.sName,
									pSdoEntry->Name);
						}
						else
						{
							if (pSdoEntry->DataType == sTmpFbCofigUnit.eDataType)
							{
								errorInfo = GetValueByKey(sTmpFbCofigUnit.sSdoKey,
									&sTmpFbCofigUnit.eDataType,
									&tmpBuff[0],
									&iGetCapacity,
									&iGetDataLength);
							}
							else
							{
								errorInfo.eErrLever = Error;
								errorInfo.ErrCode = 163;
							}

							if (errorInfo.ErrCode == 10013)
							{
								errorInfo.eErrLever = Error;
								errorInfo.ErrCode = 160;
							}
							else
							{
								if (sTmpFbCofigUnit.eDataType < DT_Str && iGetDataLength != DATATYPE_SIZE[sTmpFbCofigUnit.eDataType])
								{
									errorInfo.eErrLever = Error;
									errorInfo.ErrCode = 142;//182;
								}

								if (!errorInfo.ErrCode)
								{
									if (sTmpFbCofigUnit.eDataType == DT_I16 || sTmpFbCofigUnit.eDataType == DT_U16)
									{
										byte tmpByte = tmpBuff[0];
										tmpBuff[0] = tmpBuff[1];
										tmpBuff[1] = tmpByte;
									}
									else if (sTmpFbCofigUnit.eDataType == DT_I32 ||
										sTmpFbCofigUnit.eDataType == DT_U32 ||
										sTmpFbCofigUnit.eDataType == DT_F32)
									{
										if (pSysShareData->sSysFieldbusInfo.sBaseSet.eByteOrder == ELittleOrder)
											ChangeEndian32(&tmpBuff[0], (uint16*)&iGetDataLength);
									}
									else if (sTmpFbCofigUnit.eDataType == DT_I64 ||
										sTmpFbCofigUnit.eDataType == DT_U64 ||
										sTmpFbCofigUnit.eDataType == DT_F64)
									{
										if (pSysShareData->sSysFieldbusInfo.sBaseSet.eByteOrder == ELittleOrder)
											ChangeEndian64(&tmpBuff[0], (uint16*)&iGetDataLength);
									}
									memcpy(&pSysShareData->sFbSend.aDataBuff[sTmpFbCofigUnit.AddrByte - 20],
										&tmpBuff[0],
										iGetDataLength);
								}
							}
						}
					}
					else
					{
						if (pSysShareData->gSysCounter % 5000 == 0)
							dbgPrint(1, 0, "pSdoEntry != NULL:%s  pSdoEntry->Name!=NULL:%s sTmpFbCofigUnit->sName!=null:%s\n",
								pSdoEntry != NULL ? "true " : "false",
								pSdoEntry->Name != NULL ? "true " : "false",
								sTmpFbCofigUnit.sName ? "true " : "false");

						errorInfo.eErrLever = Error;
						errorInfo.ErrCode = 162;
					}
				}
				else
				{
					errorInfo.eErrLever = Error;
					errorInfo.ErrCode = 141;
				}

				if (bFbPf && errorInfo.ErrCode)
				{
					rt_dbgPrint(1, 0, "FbOut ErrCode:%d uUnitCount:%d IndexFb:%d sSdoKey:%s  AddrByte:%d eDataType:%s DataLen:%d iGetDataLength:%d\n",
						errorInfo.ErrCode,
						pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount,
						IndexFb,
						&sTmpFbCofigUnit.sSdoKey,
						sTmpFbCofigUnit.AddrByte,
						EDataTypeName[sTmpFbCofigUnit.eDataType],
						DATATYPE_SIZE[sTmpFbCofigUnit.eDataType],
						iGetDataLength);
					bFbPf = false;
				}
			}
			else
			{
				if (pSysShareData->gSysCounter % 5000 == 0)
					dbgPrint(1, 0, "pConfigUint == NULL:%s\n");

				errorInfo.eErrLever = Error;
				errorInfo.ErrCode = 162;
			}

			if (errorInfo.ErrCode)
				break;
		}
	}

	//Out Sw
	ErrorInfoPack(&errorInfo, (char*)"PackFbData", "");
	return errorInfo;
}
