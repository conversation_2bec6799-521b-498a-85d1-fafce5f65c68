import socket
import struct
import time
import threading
from typing import Optional, Tu<PERSON>, Dict, Any
import logging

class BoardClient:
    def __init__(self, board_ip: str = "**************", board_port: int = 8080, channel_id: int = 1):
        self.board_ip = board_ip
        self.board_port = board_port
        self.channel_id = channel_id
        self.socket: Optional[socket.socket] = None
        self.connected = False
        self.running = False
        self.heartbeat_thread: Optional[threading.Thread] = None
        self.heartbeat_interval = 1.0  # 1秒心跳间隔
        
        # SDO相关
        self.sdo_response_queue: Dict[str, Any] = {}
        self.sdo_lock = threading.Lock()
        self.frame_counter = 0
        
        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
    def pack_frame(self, sub_frame_idx: int, channel_id: int, frame_type: int, payload: bytes) -> bytes:
        """打包帧数据（小端序）"""
        head_no_len = struct.pack("<iIH", sub_frame_idx, channel_id, frame_type)
        content = head_no_len + payload
        content_len = struct.pack("<H", len(content))
        return content_len + content

    def recv_exact(self, n: int) -> bytes:
        """精确接收n字节数据"""
        buf = b""
        while len(buf) < n:
            chunk = self.socket.recv(n - len(buf))
            if not chunk:
                raise ConnectionError("Socket closed")
            buf += chunk
        return buf

    def recv_frame(self) -> Tuple[int, int, int, bytes]:
        """接收一个完整帧"""
        content_len_bytes = self.recv_exact(2)
        (content_len,) = struct.unpack("<H", content_len_bytes)
        body = self.recv_exact(content_len)
        sub_frame_idx, channel_id, frame_type = struct.unpack("<iIH", body[:10])
        payload = body[10:]
        return sub_frame_idx, channel_id, frame_type, payload

    def connect(self) -> bool:
        """连接到板子并完成握手"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            self.socket.settimeout(5.0)
            self.socket.connect((self.board_ip, self.board_port))
            
            # 发送握手
            handshake_payload = struct.pack("<H", 1)
            frame = self.pack_frame(-1, self.channel_id, 0x20, handshake_payload)
            self.socket.sendall(frame)
            
            # 接收握手响应
            sub_idx, ch, ftype, payload = self.recv_frame()
            if sub_idx == -1 and ftype == 0x8020:
                ok_flag = struct.unpack("<H", payload[:2])[0]
                if ok_flag == 0:
                    self.logger.info(f"✓ 连接成功: {self.board_ip}:{self.board_port}")
                    self.connected = True
                    self.socket.settimeout(None)  # 切换为阻塞模式
                    return True
                else:
                    self.logger.error(f"✗ 握手失败: ok_flag={ok_flag}")
            else:
                self.logger.error(f"✗ 握手响应异常: sub_idx={sub_idx}, ftype=0x{ftype:04x}")
                
        except Exception as e:
            self.logger.error(f"✗ 连接失败: {e}")
            
        self.disconnect()
        return False

    def disconnect(self):
        """断开连接"""
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None

    def send_heartbeat(self):
        """发送心跳帧"""
        if not self.connected:
            return False
        try:
            frame = self.pack_frame(-1, self.channel_id, 0x0, b"")
            self.socket.sendall(frame)
            return True
        except Exception as e:
            self.logger.error(f"✗ 心跳发送失败: {e}")
            self.connected = False
            return False

    def heartbeat_worker(self):
        """心跳线程工作函数"""
        while self.running:
            if self.connected:
                if not self.send_heartbeat():
                    self.logger.warning("心跳失败，标记为断线")
            time.sleep(self.heartbeat_interval)

    def read_sdo(self, sdo_key: str, timeout: float = 5.0) -> Optional[Any]:
        """
        读取SDO值
        
        Args:
            sdo_key: SDO键名，例如 "Axis1.ActualPosition"
            timeout: 超时时间（秒）
            
        Returns:
            SDO值，失败返回None
        """
        if not self.connected:
            self.logger.error("未连接到板子")
            return None
            
        # 生成唯一的帧ID
        with self.sdo_lock:
            self.frame_counter += 1
            frame_id = self.frame_counter
            
        # 构造SDO查询payload
        # 根据Protocol.cc中的ResolverSDOQueryFrame，payload格式为：SDO键名（以\0结尾）
        sdo_key_bytes = sdo_key.encode('utf-8') + b'\0'
        
        try:
            # 发送SDO查询帧 (frame_type = 0x2)
            frame = self.pack_frame(frame_id, self.channel_id, 0x2, sdo_key_bytes)
            self.socket.sendall(frame)
            
            # 等待响应
            start_time = time.time()
            while time.time() - start_time < timeout:
                with self.sdo_lock:
                    if str(frame_id) in self.sdo_response_queue:
                        response = self.sdo_response_queue.pop(str(frame_id))
                        return response
                time.sleep(0.01)
                
            self.logger.error(f"SDO读取超时: {sdo_key}")
            return None
            
        except Exception as e:
            self.logger.error(f"SDO读取异常: {sdo_key}, {e}")
            return None

    def process_sdo_response(self, sub_frame_idx: int, payload: bytes):
        """处理SDO响应"""
        try:
            # 解析SDO响应数据
            # 根据Protocol.cc，响应包含错误码和数据
            if len(payload) < 2:
                return
                
            # 前2字节是错误码
            error_code = struct.unpack("<H", payload[:2])[0]
            
            if error_code == 0:  # 成功
                # 解析数据部分
                data_payload = payload[2:]
                if len(data_payload) >= 4:
                    # 尝试解析为不同类型的数据
                    # 这里简化处理，实际应根据SDO类型解析
                    try:
                        # 尝试解析为float
                        value = struct.unpack("<f", data_payload[:4])[0]
                    except:
                        try:
                            # 尝试解析为int32
                            value = struct.unpack("<i", data_payload[:4])[0]
                        except:
                            # 解析为字符串
                            value = data_payload.decode('utf-8', errors='ignore').rstrip('\0')
                else:
                    value = data_payload.decode('utf-8', errors='ignore').rstrip('\0')
            else:
                value = None
                self.logger.error(f"SDO读取错误，错误码: {error_code}")
                
            # 存储响应
            with self.sdo_lock:
                self.sdo_response_queue[str(sub_frame_idx)] = value
                
        except Exception as e:
            self.logger.error(f"处理SDO响应异常: {e}")

    def start(self):
        """启动客户端（包含自动重连）"""
        self.running = True
        
        # 启动心跳线程
        self.heartbeat_thread = threading.Thread(target=self.heartbeat_worker, daemon=True)
        self.heartbeat_thread.start()
        
        while self.running:
            if not self.connected:
                self.logger.info("尝试连接...")
                if self.connect():
                    continue
                else:
                    self.logger.warning("连接失败，5秒后重试")
                    time.sleep(5)
                    continue
            
            # 处理接收数据
            try:
                self.socket.settimeout(0.1)  # 设置短超时
                sub_idx, ch, ftype, payload = self.recv_frame()
                
                # 处理不同类型的帧
                if ftype == 0x8002:  # SDO响应帧
                    self.process_sdo_response(sub_idx, payload)
                elif ftype == 0x8000:  # 心跳响应
                    pass  # 心跳响应，无需处理
                else:
                    self.logger.debug(f"收到其他帧: sub_idx={sub_idx} ch={ch} ftype=0x{ftype:04x} len={len(payload)}")
                
            except socket.timeout:
                # 超时是正常的，继续循环
                pass
            except Exception as e:
                self.logger.error(f"✗ 接收数据异常: {e}")
                self.disconnect()
                continue

    def stop(self):
        """停止客户端"""
        self.running = False
        self.disconnect()
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=2)

# 使用示例
if __name__ == "__main__":
    client = BoardClient()
    
    # 启动客户端线程
    client_thread = threading.Thread(target=client.start, daemon=True)
    client_thread.start()
    
    try:
        # 等待连接建立
        time.sleep(2)
        
        # 测试SDO读取
        if client.connected:
            print("开始测试SDO读取...")
            
            # 示例SDO读取
            test_sdos = [
                "Axis1.ActualPosition",
                "Axis1.ActualVelocity", 
                "System.Status",
                "Axis2.ActualPosition"
            ]
            
            for sdo_key in test_sdos:
                value = client.read_sdo(sdo_key)
                if value is not None:
                    print(f"✓ {sdo_key}: {value}")
                else:
                    print(f"✗ {sdo_key}: 读取失败")
                time.sleep(0.5)
        else:
            print("未连接到板子")
            
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n用户中断")
    finally:
        client.stop()
        print("客户端已停止")
