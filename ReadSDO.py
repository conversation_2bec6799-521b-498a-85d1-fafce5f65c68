import socket
import struct
import time
import threading
from typing import Optional, Tu<PERSON>, Dict, Any
import logging

class BoardClient:
    def __init__(self, board_ip: str = "**************", board_port: int = 8080, channel_id: int = 1):
        self.board_ip = board_ip
        self.board_port = board_port
        self.channel_id = channel_id
        self.socket: Optional[socket.socket] = None
        self.connected = False
        self.running = False
        self.heartbeat_thread: Optional[threading.Thread] = None
        self.heartbeat_interval = 1.0  # 1秒心跳间隔
        
        # SDO相关
        self.sdo_response_queue: Dict[str, Any] = {}
        self.sdo_lock = threading.Lock()
        self.frame_counter = 0
        
        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
    def pack_frame(self, sub_frame_idx: int, channel_id: int, frame_type: int, payload: bytes) -> bytes:
        """打包帧数据（小端序）"""
        head_no_len = struct.pack("<iIH", sub_frame_idx, channel_id, frame_type)
        content = head_no_len + payload
        content_len = struct.pack("<H", len(content))
        return content_len + content

    def recv_exact(self, n: int) -> bytes:
        """精确接收n字节数据"""
        buf = b""
        while len(buf) < n:
            chunk = self.socket.recv(n - len(buf))
            if not chunk:
                raise ConnectionError("Socket closed")
            buf += chunk
        return buf

    def recv_frame(self) -> Tuple[int, int, int, bytes]:
        """接收一个完整帧"""
        content_len_bytes = self.recv_exact(2)
        (content_len,) = struct.unpack("<H", content_len_bytes)
        body = self.recv_exact(content_len)
        sub_frame_idx, channel_id, frame_type = struct.unpack("<iIH", body[:10])
        payload = body[10:]
        return sub_frame_idx, channel_id, frame_type, payload

    def connect(self) -> bool:
        """连接到板子并完成握手"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            self.socket.settimeout(5.0)
            self.socket.connect((self.board_ip, self.board_port))
            
            # 发送握手
            handshake_payload = struct.pack("<H", 1)
            frame = self.pack_frame(-1, self.channel_id, 0x20, handshake_payload)
            self.socket.sendall(frame)
            
            # 接收握手响应
            sub_idx, ch, ftype, payload = self.recv_frame()
            if sub_idx == -1 and ftype == 0x8020:
                ok_flag = struct.unpack("<H", payload[:2])[0]
                if ok_flag == 0:
                    self.logger.info(f"✓ 连接成功: {self.board_ip}:{self.board_port}")
                    self.connected = True
                    self.socket.settimeout(None)  # 切换为阻塞模式
                    return True
                else:
                    self.logger.error(f"✗ 握手失败: ok_flag={ok_flag}")
            else:
                self.logger.error(f"✗ 握手响应异常: sub_idx={sub_idx}, ftype=0x{ftype:04x}")
                
        except Exception as e:
            self.logger.error(f"✗ 连接失败: {e}")
            
        self.disconnect()
        return False

    def disconnect(self):
        """断开连接"""
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None

    def send_heartbeat(self):
        """发送心跳帧"""
        if not self.connected:
            return False
        try:
            frame = self.pack_frame(-1, self.channel_id, 0x0, b"")
            self.socket.sendall(frame)
            return True
        except Exception as e:
            self.logger.error(f"✗ 心跳发送失败: {e}")
            self.connected = False
            return False

    def heartbeat_worker(self):
        """心跳线程工作函数"""
        while self.running:
            if self.connected:
                if not self.send_heartbeat():
                    self.logger.warning("心跳失败，标记为断线")
            time.sleep(self.heartbeat_interval)

    def read_sdo(self, sdo_key: str, timeout: float = 5.0) -> Optional[Any]:
        """
        读取SDO值

        Args:
            sdo_key: SDO键名，必须是16进制格式，例如 "0xB00101"
            timeout: 超时时间（秒）

        Returns:
            SDO值，失败返回None
        """
        if not self.connected:
            self.logger.error("未连接到板子")
            return None

        # 验证SDO键格式
        if not self._validate_sdo_key(sdo_key):
            self.logger.error(f"无效的SDO键格式: {sdo_key}")
            return None

        # 生成唯一的帧ID
        with self.sdo_lock:
            self.frame_counter += 1
            frame_id = self.frame_counter

        # 构造SDO查询payload
        # 根据Protocol.cc，payload格式为：8字符的16进制键名（以\0结尾）
        sdo_key_bytes = sdo_key.encode('utf-8') + b'\0'

        try:
            # 发送SDO查询帧 (frame_type = 0x2)
            frame = self.pack_frame(frame_id, self.channel_id, 0x2, sdo_key_bytes)
            self.socket.sendall(frame)
            self.logger.debug(f"发送SDO查询: {sdo_key}")

            # 等待响应
            start_time = time.time()
            while time.time() - start_time < timeout:
                with self.sdo_lock:
                    if str(frame_id) in self.sdo_response_queue:
                        response = self.sdo_response_queue.pop(str(frame_id))
                        return response
                time.sleep(0.01)

            self.logger.error(f"SDO读取超时: {sdo_key}")
            return None

        except Exception as e:
            self.logger.error(f"SDO读取异常: {sdo_key}, {e}")
            return None

    def _validate_sdo_key(self, sdo_key: str) -> bool:
        """验证SDO键格式"""
        # 检查长度（必须是8个字符）
        if len(sdo_key) != 8:
            return False

        # 检查格式（0x开头的16进制）
        if not sdo_key.startswith('0x'):
            return False

        try:
            # 检查是否为有效的16进制数
            key_value = int(sdo_key, 16)
            # 检查范围（根据Protocol.cc，必须在0xB00100到0xB002FF范围内）
            if key_value < 0xB00100 or key_value > 0xB002FF:
                return False
            return True
        except ValueError:
            return False

    def process_sdo_response(self, sub_frame_idx: int, payload: bytes):
        """处理SDO响应"""
        try:
            # 解析SDO响应数据
            # 根据Protocol.cc，响应格式为：
            # SDO键名(9字节，包含\0) + 错误码(2字节) + 数据类型(1字节) + 数据长度(4字节) + 实际数据

            if len(payload) < 9:
                self.logger.error(f"SDO响应数据太短: {len(payload)}")
                return

            # 跳过SDO键名（9字节）
            offset = 9

            if len(payload) < offset + 2:
                self.logger.error("SDO响应缺少错误码")
                return

            # 读取错误码
            error_code = struct.unpack("<H", payload[offset:offset+2])[0]
            offset += 2

            if error_code != 0:
                self.logger.error(f"SDO读取错误，错误码: {error_code}")
                with self.sdo_lock:
                    self.sdo_response_queue[str(sub_frame_idx)] = None
                return

            if len(payload) < offset + 5:
                self.logger.error("SDO响应缺少数据类型和长度信息")
                return

            # 读取数据类型和长度
            data_type = struct.unpack("<B", payload[offset:offset+1])[0]
            offset += 1
            data_length = struct.unpack("<I", payload[offset:offset+4])[0]
            offset += 4

            if len(payload) < offset + data_length:
                self.logger.error(f"SDO响应数据不完整，期望{data_length}字节，实际{len(payload)-offset}字节")
                return

            # 根据数据类型解析数据
            data_bytes = payload[offset:offset+data_length]
            value = self._parse_sdo_data(data_type, data_bytes)

            self.logger.debug(f"SDO响应解析成功: 类型={data_type}, 长度={data_length}, 值={value}")

            # 存储响应
            with self.sdo_lock:
                self.sdo_response_queue[str(sub_frame_idx)] = value

        except Exception as e:
            self.logger.error(f"处理SDO响应异常: {e}")

    def _parse_sdo_data(self, data_type: int, data_bytes: bytes) -> Any:
        """根据数据类型解析SDO数据"""
        try:
            # 根据base.h中的EDataType枚举
            if data_type == 1:  # DT_Bool
                return struct.unpack("<B", data_bytes[:1])[0] != 0
            elif data_type == 2:  # DT_I8
                return struct.unpack("<b", data_bytes[:1])[0]
            elif data_type == 3:  # DT_U8
                return struct.unpack("<B", data_bytes[:1])[0]
            elif data_type == 4:  # DT_I16
                return struct.unpack("<h", data_bytes[:2])[0]
            elif data_type == 5:  # DT_U16
                return struct.unpack("<H", data_bytes[:2])[0]
            elif data_type == 6:  # DT_I32
                return struct.unpack("<i", data_bytes[:4])[0]
            elif data_type == 7:  # DT_U32
                return struct.unpack("<I", data_bytes[:4])[0]
            elif data_type == 8:  # DT_I64
                return struct.unpack("<q", data_bytes[:8])[0]
            elif data_type == 9:  # DT_U64
                return struct.unpack("<Q", data_bytes[:8])[0]
            elif data_type == 10:  # DT_F32
                return struct.unpack("<f", data_bytes[:4])[0]
            elif data_type == 11:  # DT_F64
                return struct.unpack("<d", data_bytes[:8])[0]
            elif data_type == 12:  # DT_Str
                return data_bytes.decode('utf-8', errors='ignore').rstrip('\0')
            elif data_type == 13:  # DT_WStr
                return data_bytes.decode('utf-16le', errors='ignore').rstrip('\0')
            else:
                # 未知类型，返回原始字节
                self.logger.warning(f"未知的SDO数据类型: {data_type}")
                return data_bytes
        except Exception as e:
            self.logger.error(f"解析SDO数据异常: {e}")
            return data_bytes

    def query_sdo_list(self, query_type: int = 0x80, timeout: float = 10.0) -> Optional[list]:
        """
        查询SDO列表

        Args:
            query_type: 查询类型
                0x00: 查询所有非轴相关SDO
                0x80: 查询所有轴相关SDO
                0x81: 查询轴1相关SDO
                0x82: 查询轴2相关SDO
            timeout: 超时时间（秒）

        Returns:
            SDO列表，失败返回None
        """
        if not self.connected:
            self.logger.error("未连接到板子")
            return None

        # 生成唯一的帧ID
        with self.sdo_lock:
            self.frame_counter += 1
            frame_id = self.frame_counter

        try:
            # 构造对象字典查询payload (frame_type = 0x3)
            payload = struct.pack("<B", query_type)
            frame = self.pack_frame(frame_id, self.channel_id, 0x3, payload)
            self.socket.sendall(frame)
            self.logger.debug(f"发送SDO列表查询: query_type={query_type}")

            # 等待响应
            start_time = time.time()
            sdo_list = []

            while time.time() - start_time < timeout:
                with self.sdo_lock:
                    if f"list_{frame_id}" in self.sdo_response_queue:
                        response = self.sdo_response_queue.pop(f"list_{frame_id}")
                        if response is not None:
                            sdo_list.extend(response)
                        if len(response) == 0:  # 接收完成
                            break
                time.sleep(0.01)

            if sdo_list:
                self.logger.info(f"查询到{len(sdo_list)}个SDO")
                return sdo_list
            else:
                self.logger.error("SDO列表查询超时或无数据")
                return None

        except Exception as e:
            self.logger.error(f"SDO列表查询异常: {e}")
            return None

    def process_sdo_list_response(self, sub_frame_idx: int, payload: bytes):
        """处理SDO列表响应"""
        try:
            # 解析对象字典响应 (frame_type = 0x8003)
            # 这个函数需要根据实际的响应格式来实现
            # 暂时简化处理
            sdo_list = []

            # 存储响应
            with self.sdo_lock:
                key = f"list_{abs(sub_frame_idx)}"
                if key not in self.sdo_response_queue:
                    self.sdo_response_queue[key] = []

                if sub_frame_idx < 0:  # 最后一帧
                    self.sdo_response_queue[key] = []  # 标记完成
                else:
                    self.sdo_response_queue[key].extend(sdo_list)

        except Exception as e:
            self.logger.error(f"处理SDO列表响应异常: {e}")

    def start(self):
        """启动客户端（包含自动重连）"""
        self.running = True
        
        # 启动心跳线程
        self.heartbeat_thread = threading.Thread(target=self.heartbeat_worker, daemon=True)
        self.heartbeat_thread.start()
        
        while self.running:
            if not self.connected:
                self.logger.info("尝试连接...")
                if self.connect():
                    continue
                else:
                    self.logger.warning("连接失败，5秒后重试")
                    time.sleep(5)
                    continue
            
            # 处理接收数据
            try:
                self.socket.settimeout(0.1)  # 设置短超时
                sub_idx, ch, ftype, payload = self.recv_frame()
                
                # 处理不同类型的帧
                if ftype == 0x8002:  # SDO响应帧
                    self.process_sdo_response(sub_idx, payload)
                elif ftype == 0x8003:  # SDO列表响应帧
                    self.process_sdo_list_response(sub_idx, payload)
                elif ftype == 0x8000:  # 心跳响应
                    pass  # 心跳响应，无需处理
                else:
                    self.logger.debug(f"收到其他帧: sub_idx={sub_idx} ch={ch} ftype=0x{ftype:04x} len={len(payload)}")
                
            except socket.timeout:
                # 超时是正常的，继续循环
                pass
            except Exception as e:
                self.logger.error(f"✗ 接收数据异常: {e}")
                self.disconnect()
                continue

    def stop(self):
        """停止客户端"""
        self.running = False
        self.disconnect()
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=2)

# 使用示例
if __name__ == "__main__":
    client = BoardClient()
    
    # 启动客户端线程
    client_thread = threading.Thread(target=client.start, daemon=True)
    client_thread.start()
    
    try:
        # 等待连接建立
        time.sleep(2)

        # 测试SDO功能
        if client.connected:
            print("开始测试SDO功能...")

            # 首先查询可用的SDO列表
            print("\n1. 查询SDO列表...")
            sdo_list = client.query_sdo_list(query_type=0x80)  # 查询轴相关SDO
            if sdo_list:
                print(f"找到 {len(sdo_list)} 个SDO:")
                for sdo in sdo_list[:10]:  # 只显示前10个
                    print(f"  - {sdo}")
                if len(sdo_list) > 10:
                    print(f"  ... 还有 {len(sdo_list)-10} 个")
            else:
                print("未找到SDO列表，使用预定义的测试键")

            print("\n2. 测试SDO读取...")
            # 示例SDO读取（使用正确的16进制格式）
            # 注意：这些是示例键，实际的SDO键需要根据你的系统配置确定
            test_sdos = [
                "0xB00101",  # 示例：轴1位置
                "0xB00102",  # 示例：轴1速度
                "0xB00103",  # 示例：轴1状态
                "0xB00201",  # 示例：轴2位置
                "0xB00110",  # 示例：系统状态
            ]

            for sdo_key in test_sdos:
                value = client.read_sdo(sdo_key)
                if value is not None:
                    print(f"✓ {sdo_key}: {value}")
                else:
                    print(f"✗ {sdo_key}: 读取失败")
                time.sleep(0.5)
        else:
            print("未连接到板子")
            
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n用户中断")
    finally:
        client.stop()
        print("客户端已停止")
