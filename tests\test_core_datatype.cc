#include <gtest/gtest.h>
#include "core.h"

TEST(CoreDataType, Str2EnumAndLen) {
  EXPECT_EQ(DataTypeStr2Enum((char*)"Bool"), DT_Bool);
  EXPECT_EQ(DataTypeStr2Enum((char*)"U16"), DT_U16);
  EXPECT_EQ(GetDataLength(DT_Bool), 1);
  EXPECT_EQ(GetDataLength(DT_U32), 4);
}

TEST(CoreDataType, GetMaxMin) {
  double maxv=0,minv=0;
  GetDataMaxMin(DT_I16,&maxv,&minv);
  EXPECT_LT(minv,0); EXPECT_GT(maxv,0);
  GetDataMaxMin(DT_F64,&maxv,&minv);
  EXPECT_LT(minv,0); EXPECT_GT(maxv,1.0);
}

TEST(CoreDataType, FormatSdoValueBasic) {
  char buf[256];
  SDOEntryDesc e{}; int v32=123; e.DataType=DT_I32; e.pVar=&v32;
  EXPECT_STREQ(FormatSdoValue(&e, buf, sizeof(buf)), std::string("123").c_str());
  e.DataType=DT_Bool; uint8 b=1; e.pVar=&b;
  EXPECT_STREQ(FormatSdoValue(&e, buf, sizeof(buf)), std::string("1").c_str());
}

TEST(CoreDataType, FormatSdoValueStrAndByteArray) {
  char buf[256];
  SDOEntryDesc e{};
  char s[32]="hello"; e.DataType=DT_Str; e.pVar=&s[0];
  EXPECT_STREQ(FormatSdoValue(&e, buf, sizeof(buf)), s);
  ByteArray ba; uint8_t cont[3]={0xAA,0xBB,0xCC}; ba.uContentLength=3; ba.pContent=cont;
  e.DataType=DT_ByteArray; e.pVar=&ba;
  auto out=FormatSdoValue(&e, buf, sizeof(buf));
  EXPECT_EQ(std::string(out).substr(0,8), "00000003");
}