#pragma once
#ifndef _EO_H 
#define _EO_H
#include <algorithm>   //atof
#include <sstream>   //istringstream 必须包含这个头文件
#include <cstring>
#include <iostream>
#include <fstream>
#include <string>
#include <malloc.h>
#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include <vector>

#include "core/base.h"
#ifdef LINUX
#include <stdio.h>
#include <stdlib.h>
#endif

#define EoVesion "V0.1.10"  //2025-07-09 update

#pragma pack(1)
typedef struct TagEoCurveData {
	float32 Xmin_X;
	float32 Xmin_Y;
	float32 Xmax_X;
	float32 Xmax_Y;
	float32 Ymin_X;
	float32 Ymin_Y;
	float32 Ymax_X;
	float32 Ymax_Y;
	float32 PeakPeakX;
	float32 PeakPeakY;
	float32 Tmax_T;
	float32 Tmax_X;
	float32 Tmax_Y;

	Tag3FPoint tMaxPoint;
	Tag3FPoint XminPoint;
	Tag3FPoint XmaxPoint;
	Tag3FPoint YminPoint;
	Tag3FPoint YmaxPoint;
}TagEoCurveData;

typedef struct
{
	bool bActive;
	float32 fLimitPos;
	float32 fLimitForce;
}SLimitEoPara;
#pragma pack()

extern SLimitEoPara        sLimitEo;

/******************************	extern Fuction&Variables Definition	*********************************/
extern bool bCurveTruncationVisible;
extern uint32 TurncationPointIndex;
extern bool bTunnelStop;
extern ErrorCode AddEoUnit(char* pEO);
extern ErrorCode EoInit();
extern void EoReset();
extern bool Eo_TunnelJudge(Tag3FPoint* pDataBuf, uint32 PointCount, bool* pbSeqFinish, bool* pTunnelJudgeFinished);
extern bool Eo_TunnelJudgeNew(Tag3FPoint* pCurPoint, bool* pbSeqFinish, bool* pTunnelJudgeFinished);
extern ErrorCode EoMain(Tag3FPoint* pDataBuf, uint32 PointCount, bool* pbSeqFinish, bool ReStartEoCalc, bool* EoCalcFinished, bool* pbCraftIsOK, char** pResultBuffer, uint64* pResultLength);
extern void EO_CurveResult(Tag3FPoint* pDataBuf, unsigned long int iPointCount);
extern TagEoCurveData CurResult;
#endif // !_EO_H