#ifndef __CORE_H
#define __CORE_H

#include <stdio.h>
#include <stdlib.h>
#include "base.h"
#include "syslog.h"
#include "assert.h"

#define xsprintf(buf, fmt, ...)			sprintf(buf, fmt, ##__VA_ARGS__)
#define xsnprintf(buf, len, fmt, ...)	snprintf(buf, len, fmt, ##__VA_ARGS__)
#define xfwrite(fd, buf, len)			fwrite(buf, len, 1, (FILE*)fd)
#define xfread(fd, buf, len)			fread(buf, len, 1, (FILE*)fd)
#define xmalloc(len)					malloc(len)
#define xfree(len)						free(len)

typedef struct sTon {
	uint64	initTimestamp;
	uint64	currTimestamp;
	uint32  duarationInUS;
}sTon;

bool TonReStart(sTon* ton, float duarationInSecond);
bool TonCheckTimeout(sTon* ton);
extern uint64 SdoValueChangeCounter;
extern uint64 LastSdoValueChangeCounter;
extern SDOEntryDesc* pFistEntry;

ErrorInfo GetValueByKey(char* szKey, EDataType* dataType, byte* pTargetData, int* pTargetCapcity, int* iTargetDataLength);
ErrorInfo GetValueByEntry(SDOEntryDesc* pEntry, byte* pTargetData, int* pTargetCapcity, int* iTargetDataLength);

ErrorInfo SetSdoValueByKey(char* szKey, byte* pSrcData, uint32 srcDataLength);
ErrorInfo SetSdoValueByEntry(SDOEntryDesc* pEntry, byte* pSrcData, uint32 dataLength);

char* FormatSdoValue(SDOEntryDesc* pEntry, char* pStr, int nStrBufLength);
ErrorInfo ScanfSdoValue(SDOEntryDesc* pEntry, char* pStr);
ErrorInfo ScanfReadSdoValue(SDOEntryDesc* pEntry, char* pStr);
ErrorInfo ScanSdoValueByKey(char* szKey, char* pStr);

int		  GetDataLength(EDataType dt);
void GetDataMaxMin(EDataType dt, double* pMaxVar, double* pMinVar);
ErrorInfo RegistSdoEntry(SDOEntryGroup* sdoEntryGroup, int entryGroupNum);
ErrorInfo UpdateSdoDict();
//ErrorCode InitModule_Core();
SDOEntryDesc* HashTable_Find(const char* pKey);
SDOEntryDesc* HashTable_First();
SDOEntryDesc* HashTable_Next();
const char* HashTable_CurrentKey();
SDOEntryDesc* GetEntryByKey(const char* szKey);
void PrintAllSdo();
ErrorInfo SaveFactoryPara(const char* szFactoryParaCfgFile);
ErrorInfo LoadFactoryPara(const char* szFactoryParaCfgFile);


#endif
