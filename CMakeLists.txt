﻿cmake_minimum_required (VERSION 3.8)
project ("lnx-press")

message(STATUS "########## Press System ##########")

SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY /root/PressControl/Lib)
SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY /root/PressControl)

SET(OS "PREEMPT")
add_compile_definitions(_PRESS_BY_MR_DENG)
if(OS STREQUAL "PREEMPT")
	add_compile_definitions(PREEMPT)
	add_compile_definitions(LINUX)
elseif(OS STREQUAL "XENOMAI")
	add_compile_definitions(XENOMAI)
	add_compile_definitions(LINUX)
elseif(OS STREQUAL "LINUX")
	add_compile_definitions(LINUX)
endif()

#add_compile_definitions(SHARED_LIB_MODE=1)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Werror=return-type  -Wno-unused-but-set-variable -Wno-unused-variable -O0")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Werror=return-type -Wno-unused-but-set-variable -Wno-unused-variable -O0") 

message(STATUS "########## add core ##########")
add_subdirectory ("core")

message(STATUS "########## add fieldbus ##########")
add_subdirectory ("fieldbus")

message(STATUS "########## add comm ##########")
add_subdirectory ("comm")

message(STATUS "########## add sqliteapi ##########")
add_subdirectory ("sqliteapi")

message(STATUS "########## add soem ##########")
add_subdirectory ("soem")

message(STATUS "########## add EO ##########")
add_subdirectory ("EO")

message(STATUS "########## add pressRt ##########")
add_subdirectory ("pressRt")

add_subdirectory("tests")
#[[]]