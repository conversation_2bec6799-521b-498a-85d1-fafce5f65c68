#include "core.h"
#include "FSM_SysSelfCheck.h"
#include  "FSM_SysInitEntry.h"
#include "FSM_SysSelfCheckEntry.h"
#include "sensors/Sensor.h"
#include "SysShareMemoryDefine.h"
#include "FieldbusThread.h"
#include "rttimer.h"

bool bDriveSelfCheckFinish;
bool bSpiSelfCheckFinish;

uint32 uSelfcheckCounter;
bool bHadSelfChekMcPara = false;
bool bDriveOk[AxisNum];
bool bForceSensorOk[ForceMaxNumber];
bool bExtPosSensorOk[PotentiometerMaxNumber];

void SelfChekMcPara()
{
	for (int i = 0; i < AxisNum; i++)
	{
		MC_ParaInit(&pSysShareData->AxisMc[i].Axis);
	}
}

void DriveSelfCheck()
{
	if (uSelfcheckCounter > 6000 && !bDriveSelfCheckFinish)
	{
		bDriveSelfCheckFinish = true;
		bDriveOk[0] = false;
	}
	else
	{
		if (pSysShareData->gEthercatDeviceInOp)
		{
			if (!bHadSelfChekMcPara)
			{
				SelfChekMcPara();   //驱动器参数自检后完成初始化
				bHadSelfChekMcPara = true;
			}

			if (MC_IsServoInError(&pSysShareData->AxisMc[0].Axis) && !bNeedResetMc)
			{
				bNeedResetMc = true;
			}

			if (bNeedResetMc)       //电机错误需要清除
			{
				MC_ResetFault(&pSysShareData->AxisMc[0].Axis, &sResetFaultExeFeed);
				if (sResetFaultExeFeed.bExecuteEnd)
				{
					if (sResetFaultExeFeed.ExeErrInfo.ErrCode)
					{
						bDriveOk[0] = false;
					}
					else
					{
						bDriveOk[0] = true;
					}

					bDriveSelfCheckFinish = true;
					bNeedResetMc = false;
				}
			}
			else
			{
				bDriveSelfCheckFinish = true;
				bDriveOk[0] = true;
			}
		}
	}
}

void SpiSelfCheck()
{
	if (!pSysShareData->bHadInitSensSpi)
	{
		if (WITHOUT_SENSOR_MODE)
		{
			// bSpiConnected = true;
			pSysShareData->bHadInitSensSpi = true;
		}
		else
		{
			SpiInit(&pSysShareData->bHadInitSensSpi);
			if (pSysShareData->bHadInitSensSpi)
			{
				bSpiSelfCheckFinish = true;
			}
		}
	}

	if (uSelfcheckCounter > 6000 && !bSpiSelfCheckFinish)
	{
		bSpiSelfCheckFinish = true;
		bForceSensorOk[0] = false;
		bExtPosSensorOk[0] = false;
		pSysShareData->sForceData[0].bActived = false;
		pSysShareData->sExtPosData[0].bActived = false;
	}
	else
	{
		if (pSysShareData->bHadInitSensSpi || uSelfcheckCounter > 2000)
		{
			SensorsInit();
			bSpiSelfCheckFinish = true;
			bForceSensorOk[0] = true;
			bExtPosSensorOk[0] = true;
			pSysShareData->sForceData[0].bActived = true;
			pSysShareData->sExtPosData[0].bActived = true;
		}
	}
}


void FsmSysSelfCheck()
{
	ErrorCode ErrCode = 0;
	if (gSysSimulation)
	{
		//printf("----------gSysSimulation----------.\n");
		FsmSysInitEntry();
	}
	else
	{
		uSelfcheckCounter++;
		if (SYSTEM_MOTION_MODE)
		{
			if (!bDriveSelfCheckFinish)
				DriveSelfCheck();
		}
		else
		{
			bDriveOk[0] = true;
			bDriveSelfCheckFinish = true;
		}

		if (!bSpiSelfCheckFinish)
		{
			SpiSelfCheck();

			bForceSensorOk[0] = true;
			bExtPosSensorOk[0] = true;
			pSysShareData->sForceData[0].bActived = true;
			pSysShareData->sExtPosData[0].bActived = true;

			bSpiSelfCheckFinish = true;
		}


		if (bDriveSelfCheckFinish && bSpiSelfCheckFinish)
		{
			if (bDriveOk[0] && bForceSensorOk[0] && bExtPosSensorOk[0])
			{
				FsmSysInitEntry();
			}
			else
			{
				printf("\n\n FsmSysSelfCheck Error bDriveOk:%s  bForceSensorOk:%s bExtPosSensorOk:%s\n\n",
					bDriveOk[0] ? "true" : "false",
					bForceSensorOk[0] ? "true" : "false",
					bExtPosSensorOk[0] ? "true" : "false");

				FsmSysInitEntry();
			}
		}
	}
}

