#include "CurveSet.h"
#include "SysShareMemoryDefine.h"
#include "sqliteapi/sqliteOp/SqliteOp.h"
#include "rapidjson/prettywriter.h"
#include "rapidjson/document.h"
#include "rapidjson/error/en.h"
#include "rapidjson/error/error.h"
using namespace rapidjson;

/* UpdateChartsSetInfo				
* @param[in]     None
* @param[out]    None
* @return        ErrorInfo
*/
std::string Chartsjson;
ErrorInfo UpdateChartsSetInfo()
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	//rt_dbgPrint(1, 0, "\n Update Charts Info From DataBase \n\n");
	char* test_version = "V1.0.0";
	char* spCharts = "ChartInfo";
	// 调用被测函数

	//rt_dbgPrint(1, 0, "\n UpdateChartsSetInfo\n\n");
	errorInfo = ReadDeviceConfigJsonOp(spCharts, test_version, &Chartsjson);
	if (!errorInfo.ErrCode)
	{
		//从数据库中获取 整个IO Json数据内容
		if (strcmp(Chartsjson.c_str(), "0") != 0 && strlen((char*)Chartsjson.c_str()) > 0)
		{
			psChartsData->iChartCount = 0;
			//开始解析 数据
			rapidjson::Document docChartsSet;
			docChartsSet.Parse((char*)Chartsjson.c_str());
			if (docChartsSet.IsObject())
			{
				if (docChartsSet.HasMember("Charts") && docChartsSet["Charts"].IsArray())
				{
					if (SYSTEM_MOTION_MODE)
					{
						for (auto& jChart : docChartsSet["Charts"].GetArray())
						{
							if (jChart.IsNull())
							{
								errorInfo.ErrCode = 709;
								errorInfo.eErrLever = Error;
							}
							else
							{
								if (jChart.HasMember("ChX") && jChart["ChX"].IsString() &&
									jChart.HasMember("ChY") && jChart["ChY"].IsString())
								{
									memset(psChartsData->sChart[psChartsData->iChartCount].sChartSetInfo.sXChannelName, 0, sizeof(str64));
									memset(psChartsData->sChart[psChartsData->iChartCount].sChartSetInfo.sYChannelName, 0, sizeof(str64));

									strcpy(psChartsData->sChart[psChartsData->iChartCount].sChartSetInfo.sXChannelName, jChart["ChX"].GetString());
									strcpy(psChartsData->sChart[psChartsData->iChartCount].sChartSetInfo.sYChannelName, jChart["ChY"].GetString());

									//rt_dbgPrint(1, 0, "sXChannelName:%s  sYChannelName:%s\n\n", 
									//	psChartsData->sChart[psChartsData->iChartCount].sChartSetInfo.sXChannelName,
									//	psChartsData->sChart[psChartsData->iChartCount].sChartSetInfo.sYChannelName);
									psChartsData->iChartCount++;
								}
								else
								{
									errorInfo.ErrCode = 700;
									errorInfo.eErrLever = Error;
									break;
								}
							}
						}
					}
					else
					{
						strcpy(psChartsData->sChart[0].sChartSetInfo.sXChannelName, "ExtShift");
						strcpy(psChartsData->sChart[0].sChartSetInfo.sYChannelName, "Force");
						psChartsData->iChartCount = 1;
					}
				}

				if (errorInfo.ErrCode == 0)
				{
					if (docChartsSet.HasMember("Sample") && docChartsSet["Sample"].IsObject())
					{
						Value& jSample = docChartsSet["Sample"];
						str64 sTmpSample;
						if (jSample["Mode"].IsString())
						{
							memset(sTmpSample, 0, sizeof(str64));
							strcpy(sTmpSample, jSample["Mode"].GetString());

							if (strcmp(sTmpSample, "RealTime") == 0)
							{
								if (SYS_BASE_TIME_uS == 1000)
									psChartsData->eSampleType = Interval_Time_1000;
								else if (SYS_BASE_TIME_uS == 500)
									psChartsData->eSampleType = Interval_Time_2000;
								else if (SYS_BASE_TIME_uS == 250)
									psChartsData->eSampleType = Interval_Time_4000;
								else
								{
									errorInfo.ErrCode = 704;
									errorInfo.eErrLever = Error;
									rt_dbgPrint(1, 0, "\n eSampleType SYS_BASE_TIME_uS:%d\n\n", SYS_BASE_TIME_uS);
								}
							}
							else if (strcmp(sTmpSample, "TimePeak") == 0 && jSample.HasMember("Rate"))
							{
								if (jSample["Rate"].IsString())
								{
									str64 sTmpSampleRate;
									memset(sTmpSampleRate, 0, sizeof(str64));
									strcpy(sTmpSampleRate, jSample["Rate"].GetString());
									if (strcmp(sTmpSampleRate, "10%") == 0)
										psChartsData->eSampleType = Interval_Time_0100;
									else if (strcmp(sTmpSampleRate, "20%") == 0)
										psChartsData->eSampleType = Interval_Time_0100;		//Interval_Time_0200
									else if (strcmp(sTmpSampleRate, "50%") == 0)
										psChartsData->eSampleType = Interval_Time_0500;
									else if ((strcmp(sTmpSampleRate, "4000/S") == 0))
										psChartsData->eSampleType = Interval_Time_4000;	    //4000 points/S
									else if ((strcmp(sTmpSampleRate, "1000/S") == 0))
										psChartsData->eSampleType = Interval_Time_1000;
									else if ((strcmp(sTmpSampleRate, "500/S") == 0))
										psChartsData->eSampleType = Interval_Time_0500;
									else if ((strcmp(sTmpSampleRate, "100/S") == 0))
										psChartsData->eSampleType = Interval_Time_0100;
									else if ((strcmp(sTmpSampleRate, "50/S") == 0))
										psChartsData->eSampleType = Interval_Time_0050;
									else if ((strcmp(sTmpSampleRate, "10/S") == 0))
										psChartsData->eSampleType = Interval_Time_0010;
									else
									{
										errorInfo.ErrCode = 705;
										errorInfo.eErrLever = Error;
										rt_dbgPrint(1, 0, "\n eSampleType str:%s\n\n", sTmpSampleRate);
									}
								}
							}
							else if (strcmp(sTmpSample, "EqualPos") == 0 && jSample.HasMember("Rate"))
							{
								if (jSample["Rate"].IsString())
								{
									str64 sTmpSampleRate;
									memset(sTmpSampleRate, 0, sizeof(str64));
									strcpy(sTmpSampleRate, jSample["Rate"].GetString());
									if (strcmp(sTmpSampleRate, "0.01mm") == 0)
										psChartsData->eSampleType = Interval_Pos_0010um;
									else if (strcmp(sTmpSampleRate, "0.1mm") == 0)
										psChartsData->eSampleType = Interval_Pos_0100um;
									else if (strcmp(sTmpSampleRate, "1mm") == 0)
										psChartsData->eSampleType = Interval_Pos_1000um;
									else
									{
										errorInfo.ErrCode = 706;
										errorInfo.eErrLever = Error;
										rt_dbgPrint(1, 0, "\n eSampleType str:%s:%\n\n", sTmpSampleRate);
									}
								}
							}
							else if (strcmp(sTmpSample, "EqualForce") == 0 && jSample.HasMember("Rate"))
							{
								if (jSample["Rate"].IsString())
								{
									str64 sTmpSampleRate;
									memset(sTmpSampleRate, 0, sizeof(str64));
									strcpy(sTmpSampleRate, jSample["Rate"].GetString());
									if (strcmp(sTmpSampleRate, "0.01KN") == 0)
										psChartsData->eSampleType = Interval_Force_0010N;
									else if (strcmp(sTmpSampleRate, "0.1KN") == 0)
										psChartsData->eSampleType = Interval_Force_0100N;
									else if (strcmp(sTmpSampleRate, "1KN") == 0)
										psChartsData->eSampleType = Interval_Force_1000N;
									else
									{
										errorInfo.ErrCode = 707;
										errorInfo.eErrLever = Error;
										rt_dbgPrint(1, 0, "\n eSampleType str:%s:%\n\n", sTmpSampleRate);
									}
								}
							}
							else
							{
								errorInfo.ErrCode = 703;
								errorInfo.eErrLever = Error;
							}
							//rt_dbgPrint(1, 0, "\n eSampleType:%d \n\n", psChartsData->eSampleType);
						}
						else
						{
							errorInfo.ErrCode = 702;
							errorInfo.eErrLever = Error;
						}
					}
					else
					{
						errorInfo.ErrCode = 701;
						errorInfo.eErrLever = Error;
					}
				}
			}
		}
		else
		{
			errorInfo.ErrCode = 708;
			errorInfo.eErrLever = Error;
			rt_dbgPrint(1, 0, "\n ChartInfo  Is NULL \n\n");
		}
	}

	ErrorInfoPack(&errorInfo, "UpdateChartsSetInfo", "");
	return errorInfo;
}
