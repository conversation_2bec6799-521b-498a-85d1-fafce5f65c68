/**************************************************************
 * File: anybus2spiHost.h
 * Author: Gloss Tsai
 * Created on: January 26, 2024
 * Description:
 *      ANYBUS通信库主机部分
 *      Slave stands for busboard while Host stands for 1176.
 * Note:
 *      https://alidocs.dingtalk.com/i/nodes/a9E05BDRVQvv6GQyCBqzPQ0DJ63zgkYA
 * Copyright (C) 2024 Leetx. All rights reserved.
 *************************************************************/
#ifndef _ANYBUS_2_SPI_HOST_H_
#define _ANYBUS_2_SPI_HOST_H_

#if defined(__cplusplus)
extern "C" {
#endif

#include <stddef.h>

#include "anybus2spiBaseLib.h"
    /* Private macro ------------------------------------------------------------- */
#define FSM_TIMEOUT_TICK        FSM_TIMEOUT_25ms
#define FSM_TIMEOUT_1ms         1000u
#define FSM_TIMEOUT_1ms5        1500u
#define FSM_TIMEOUT_5ms         5000u
#define FSM_TIMEOUT_25ms        25000u

#define MODULE_LINK_OFFLINE_TICK   (100u * 1000u) /* 100ms */

/**
 *	@name IMXRT1176_USE_ITCM
 *	@brief 定义该宏，且值等于1，则使用DMA方式的SPI通信,且使能ITCM
 *	@note 若在1176上使用本库，请在“右键单击工程->属性->C/C++ General->Paths and Symbols”
 *			的Symbols中加入IMXRT1176_USE_ITCM = 1
 */
    typedef enum
    {
        AB2SPIE_OK = 0u,        /* 配置成功 */
        AB2SPIE_DATALEN = 1u,   /* 通信数据长度配置错误 */
        AB2SPIE_HEAP = 2u,      /* 堆空间不够 */
        AB2SPIE_BUSTYPE = 3u,   /* 总线类型未获取 */
        AB2SPIE_PARAM = 4u,     /* 参数错误 */
    }AB2SErrorCode_e;   /* uint8_t max 255 */
    typedef enum
    {
        BEFORE_INITIALIZATION = 0u,
        CONFIG_STATE,
        WORKING_STATE,
        ERROR_STATE,
        REQUEST_RESPON_STATE,
        CRC_CHECK_STATE,
    }AnybusFSM_State_e; /* uint8_t max 255 */
    typedef Anybus_HostInstruction_e AnybusFSM_Event_e;
    typedef struct errorHandle
    {
        uint8_t BusLinkStatus;          /* Anybus_LinkStatus_e 总线连接状态 */
        uint8_t ModuleLinkStatus;       /* Anybus_LinkStatus_e 模块连接状态 */

        uint64_t Work2ConfigCnt;		/* 工作模式超时返回配置模式次数 */
        uint64_t WorkingModeCpltCnt;	/* 工作模式下通信成功次数 */
        uint64_t ConfigModeCpltCnt;     /* 配置模式下通信成功次数 */
        uint64_t DataFrameErrorCnt;	    /* 数据帧错误计数 */
        uint64_t CRCErrorCnt;           /* CRC错误计数 */
        uint64_t SPIComTxTotalCnt;      /* = SPIComRxTotalCnt + CRCErrorCnt */
        uint64_t SPIComRxTotalCnt;      /* = WorkingModeCpltCnt + ConfigModeCpltCnt */

        uint32_t timeTick;
        uint32_t ModuleOfflineTick;
    } runStateRecord_t;
    typedef struct
    {
        char AB2SLibVersion_Slave[16];
        char AB2SLibVersion_Host[16];
        char ModuleSoftWareVersion[16];
        char ModuleHardWareVersion[16];
    } AB2S_versionInfo_t;
    typedef struct
    {
        /**
         * All of busboards are specified a kind of bus type before delivering it to customer.
         * The bus type is actually defined by the busboard and read by the host after power-on.
         */
        uint8_t BusType;   /* Anybus_BusType_e */
        AB2S_versionInfo_t tVersion;

        volatile uint8_t FSMState;  /* AnybusFSM_State_e */
        volatile uint8_t FSMEvent;  /* AnybusFSM_Event_e */
        struct dataLength
        {
            uint16_t ToBusLen;
            uint16_t FromBusLen;
            uint16_t MaxDataFramePhase; /* max(ToBusLen, FromBusLen) */
            uint16_t TxRxLen;    /* Indicate how long of data should be transmitted via SPI. */
        } dataLen;
        runStateRecord_t runState;
        AB2S_CCLConfigInfo_t* pCCLConfigInfo;
        AB2S_ProfiBusConfigInfo_t tProfiBusConfigInfo;
        AB2S_netX90ConfigInfo_t tNetX90ConfigInfo;
    }Anybus_HostStatus_t;

    void Anybus2spi_Init();
    uint8_t Anybus2spi_SetCCLinkConfigInfo(AB2S_CCLConfigInfo_t* pConfigInfo);
    uint8_t Anybus2spi_SetProfibusConfigInfo(uint8_t NodeAddress, AB2S_DataSwapLen_e eDSL);
    uint8_t Anybus2spi_SetNetX90ConfigInfo(AB2S_ProductLine_e ePL, AB2S_DataSwapLen_e eDSL);

    void Anybus2spi_HandleTIMPeriodElapsedCallback();
    void Anybus2spi_HandleSPITxRxCpltCallback();
    void Anybus2spi_GetAndSetBusData(uint8_t*, uint8_t*);
    void Anybus2spi_GetBusData(uint8_t*);
    void Anybus2spi_SetBusData(const uint8_t*);
    uint8_t Anybus2spi_GetAnybusType(void);
    uint8_t Anybus2spi_GetAnybusTypeBlocking(void);
    void Anybus2spi_GetRunStateRecordParam(runStateRecord_t**);
    void Anybus2spi_CleanRunStateRecordParam();
    AB2S_versionInfo_t* Anybus2spi_GetVersionInfo();
#if defined(__cplusplus)
}
#endif

#endif /* _ANYBUS_2_SPI_HOST_H_ */
