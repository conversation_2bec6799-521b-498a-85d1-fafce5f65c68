﻿#include "confighandler.h"
#include "responsejson.h"
#include "nlohmann/json.hpp"

bool servertestflag = false;
bool FileServerControl = false;
//bool Fileconecttest = false;
ConfigHandler::ConfigHandler(DBI* db) :ApiHandler(db)
{
	tableCheck();
}

ConfigHandler::~ConfigHandler()
{

}

void ConfigHandler::tableCheck()
{
	sqlite3_stmt* stmt = nullptr;
	std::string sqlquery = "select name from sqlite_master where TYPE='table' and name = 'DeviceConfig'; ";
	auto rc = dbi->prepare(sqlquery, stmt);
	bool hasCh{ false }, hasDev{ false };
	rc = sqlite3_step(stmt);
	if (rc != SQLITE_ROW)
	{
		std::string sql = R"( BEGIN TRANSACTION;SELECT md5('YourString');CREATE TABLE DeviceConfig (Id INTEGER PRIMARY KEY  AUTOINCREMENT, Ver VARCHAR (32), Item VA<PERSON>HAR (32) UNIQUE, Content JSON, Author VARCHAR (32), Md5code VARCHAR (32),UpdateTime DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')));
					 CREATE TABLE IF NOT EXISTS ConfigChangeLog (Id INTEGER PRIMARY KEY  AUTOINCREMENT, typecode  VARCHAR (32),Ver VARCHAR (32), Item VARCHAR (32) , Content JSON, Author VARCHAR (32), UpdateTime DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')));
				     INSERT INTO DeviceConfig (Item, Content,Author ) VALUES ('GlobalVar', NULL,'OEM' ), ('FieldBus', NULL,'OEM' ),  ('IdGenerator', NULL,'OEM' ) ,('DataService', NULL,'OEM' ),('ChartInfo', NULL,'OEM' ) ,('DevBase', NULL,'OEM') , ('BaseInfo',NULL,'OEM' ) , ('Channel', NULL,'OEM'  ) , ('Calibration', NULL,'OEM'  ); 
				     Update DeviceConfig Set Ver = 'V1.0.0' ; COMMIT TRANSACTION;)";
		rc = dbi->exec(sql, NULL, NULL);
		if (rc != SQLITE_OK)
		{
			std::cerr << "创建通道配置表失败,错误码 " << rc << std::endl;
		}
		std::string sql1 = R"( CREATE TABLE IF NOT EXISTS DeviceConfigRecord (Id INTEGER PRIMARY KEY  AUTOINCREMENT, typecode  VARCHAR (32),Ver VARCHAR (32), Item VARCHAR (32) , Content JSON, Author VARCHAR (32), UpdateTime DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')));)";
		std::string sql2 = R"(CREATE TABLE IF NOT EXISTS DeviceConfigMd5 (md5code VARCHAR (64) PRIMARY KEY UNIQUE, Ver VARCHAR (32),field_name  VARCHAR (32), content JSON, update_time DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')));)";
		rc = dbi->exec(sql1, NULL, NULL);
		if (rc != SQLITE_OK)
		{
			std::cerr << "创建通道配置记录表失败,错误码 " << rc << std::endl;
		}
		rc = dbi->exec(sql2, NULL, NULL);
		if (rc != SQLITE_OK)
		{
			std::cerr << "创建通道配置MD5表失败,错误码 " << rc << std::endl;
		}
		//std::string sql3 = R"(CREATE TRIGGER trigger_deviceconfig_insert  AFTER INSERT ON DeviceConfig  BEGIN INSERT OR REPLACE INTO deviceconfigmd5( md5code, content, field_name ) VALUES(md5(new.Content),new.Content,new.Item ); INSERT INTO deviceconfigrecord (Id, typecode, Ver,Item, Content, Author, UpdateTime)VALUES(new.Id,'new',new.Ver,new.Item,md5(new.Content),new.Author,new.UpdateTime);END;)";
		std::string sql4 = R"(CREATE TRIGGER IF NOT EXISTS trigger_deviceconfig_update AFTER UPDATE ON DeviceConfig FOR EACH ROW BEGIN INSERT OR REPLACE INTO deviceconfigmd5( md5code,Ver, content, field_name ) VALUES(md5(new.Content),new.Ver,new.Content,new.Item); INSERT INTO deviceconfigrecord (typecode, Ver,Item, Content, Author, UpdateTime)VALUES('new',new.Ver,new.Item,md5(new.Content),new.Author,new.UpdateTime);INSERT OR REPLACE INTO deviceconfig ( Id, Ver, Item, Content, Author, md5code, UpdateTime ) VALUES ( new.Id, new.Ver, new.Item, new.Content, new.Author, md5(new.Content), new.UpdateTime );END;)";
		std::string sql5 = R"(CREATE TRIGGER IF NOT EXISTS limit_deviceconfigrecord_rows AFTER INSERT ON deviceconfigrecord BEGIN DELETE FROM deviceconfigrecord WHERE rowid <= new.rowid - 100000; END; )";
		std::string sql6 = R"(CREATE TRIGGER IF NOT EXISTS limit_ConfigChangeLog_rows AFTER INSERT ON ConfigChangeLog BEGIN DELETE FROM ConfigChangeLog WHERE rowid <= new.rowid - 30000; END; )";
		/*rc = dbi->exec(sql3, NULL, NULL);
		if (rc != SQLITE_OK)
		{
			std::cerr << "创建通道配置插入触发器失败,错误码 " << rc << std::endl;
		}*/
		rc = dbi->exec(sql4, NULL, NULL);
		if (rc != SQLITE_OK)
		{
			std::cerr << "创建通道配置更新触发器失败,错误码 " << rc << std::endl;
		}
		rc = dbi->exec(sql5, NULL, NULL);
		if (rc != SQLITE_OK)
		{
			std::cerr << "创建通道配置条数限制触发器失败,错误码 " << rc << std::endl;
		}
		rc = dbi->exec(sql6, NULL, NULL);
		if (rc != SQLITE_OK)
		{
			std::cerr << "创建配置更改记录条数限制触发器失败,错误码 " << rc << std::endl;
		}
	}
	sqlite3_finalize(stmt);
	std::string sql3 = R"( CREATE TABLE IF NOT EXISTS ConfigChangeLog (Id INTEGER PRIMARY KEY  AUTOINCREMENT, typecode  VARCHAR (32),Ver VARCHAR (32), Item VARCHAR (32) , Content JSON, Author VARCHAR (32), UpdateTime DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')));)";
	rc = dbi->exec(sql3, NULL, NULL);
	if (rc != SQLITE_OK)
	{
		std::cerr << "创建通道配置更改日志表失败,错误码 " << rc << std::endl;
	}
}

void ConfigHandler::updateState(const std::string& m)
{
	if (m == "GlobalVar")
	{
		dbi->state()->gvar++;
	}
	else if (m == "FieldBus")
	{
		dbi->state()->fieldbus++;
	}
	else if (m == "IdGenerator")
	{
		dbi->state()->sn++;
	}
	else if (m == "DataService")
	{
		dbi->state()->dataService++;
	}
	else if (m == "ChartInfo")
	{
		dbi->state()->curve++;
	}
	else if (m == "DevBase")
	{
		dbi->state()->ncConfig++;
	}
	//else if (m == "BaseInfo")
	//{
	//	dbi->state()->gvar++;
	//}
	else if (m == "Channel")
	{
		dbi->state()->channel++;
	}
	else if (m == "Calibration")
	{
		dbi->state()->calibration++;
	}
}

void ConfigHandler::bindServe(httplib::Server& svr)
{
	svr.Get("/cfg/base/testserver", std::bind(&ConfigHandler::TestServer, this, std::placeholders::_1, std::placeholders::_2));

	svr.Post("/cfg/base/info", (httplib::Server::Handler)std::bind(&ConfigHandler::handle, this, std::placeholders::_1, std::placeholders::_2));
	svr.Post("/cfg/base/set", (httplib::Server::Handler)std::bind(&ConfigHandler::baseInfoConfig, this, std::placeholders::_1, std::placeholders::_2));
}

bool compareFilesTmp(const std::string& originalFile, const std::string& modifiedFile) {
	std::ifstream orig(originalFile);
	std::ifstream mod(modifiedFile);

	if (!orig.is_open() || !mod.is_open()) {
		std::cerr << "无法打开文件进行比较" << std::endl;
		return false;
	}

	std::string origLine, modLine;
	int origLineCount = 0, modLineCount = 0;

	// 统计行数
	while (std::getline(orig, origLine)) {
		origLineCount++;
	}
	while (std::getline(mod, modLine)) {
		modLineCount++;
	}

	// 对比行数
	return origLineCount == modLineCount;
}

int ConfigHandler::modifyip(std::string ip,std::string SubnetMask)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	std::string NetworkPort = "eth2";
	std::string modifyip;
	if (NetworkPort == "eth2") {
		std::string modifyiptemp = "sudo nmcli connection modify eth2-static ipv4.addresses " + ip + "/"+SubnetMask;
		usleep(250000);
		int retip_temp = system(modifyiptemp.c_str());
		usleep(250000);
		if (retip_temp == 0) {
			//std::cout << "修改临时ip成功" << modifyiptemp << std::endl;
			usleep(250000);
			modifyip = "sudo nmcli connection up eth2-static";
			int retip_temp = system(modifyip.c_str());
			usleep(250000);
			if (retip_temp == 0) {
				//std::cout << "修改临时ip成功" << std::endl;
				usleep(250000);
				opipeth2 = ip;
				return 1;
			}
			else {
				std::cerr << "启动eth2的ip设置失败" << std::endl;
				errorInfoapi.eErrLever = Error;
				errorInfoapi.ErrCode = 17134;
				ErrorInfoPack(&errorInfoapi, "handle", "");
				return 0;
			}
		}
		else {
			std::cerr << "修改eth2的ip设置失败" << std::endl;
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17132;
			ErrorInfoPack(&errorInfoapi, "handle", "");
			return 0;
		}
	}
	else {
		std::cerr << "eth2网口名称错误" << std::endl;
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17133;
		ErrorInfoPack(&errorInfoapi, "handle", "");
		return 0;
	}
}

void ConfigHandler::TestServer(const httplib::Request& req, httplib::Response& res) {
	servertestflag = true; // 停止循环
	ResponseJson resJson;
	resJson.json(0, "Servertest");
	res.set_content(resJson.toJson(), "application/json");
}

void ConfigHandler::handle(const httplib::Request& req, httplib::Response& res)
{
	//Fileconecttest = true;
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson resJson;
	nlohmann::json jsonPara = nlohmann::json::parse(req.body);
	std::string  _item = jsonPara["item"];
	std::string ver = jsonPara["ver"];
	sqlite3_stmt* stmt = nullptr;
	std::string sql = "SELECT Content from DeviceConfig where item=? and ver =? "; //and" + _item + " is not null
	auto rc = dbi->prepare(sql, stmt);
	rc += sqlite3_bind_text(stmt, 1, _item.c_str(), _item.length(), NULL);
	rc += sqlite3_bind_text(stmt, 2, ver.c_str(), ver.length(), NULL);
	if (rc == SQLITE_OK)
	{
		if (sqlite3_step(stmt) == SQLITE_ROW)
		{

			auto p = sqlite3_column_blob(stmt, 0);
			if (p == nullptr)
			{
				resJson.json(17062, u8"设备" + _item + u8"配置为空");
				errorInfoapi.eErrLever = Error;
				errorInfoapi.ErrCode = 17062;
			}
			else
			{
				resJson.predata();
				std::string props{ (char*)p };
				if (props.length() > 0 && (props[0] == '{' || props[0] == '['))
					resJson << props;
				else
					resJson << '\"' << props << '\"';
				resJson.finishdata();
			}
		}
		else
		{
			resJson.json(17061, u8"获取设备" + _item + u8"配置失败");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17061;
		}
	}
	else
	{
		resJson.json(17517, u8"获取设备配置失败,SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17517;
	}
	res.set_content(resJson.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, "handle", "");
}


std::string execCmd(const std::string& cmd) {
	std::array<char, 128> buffer;
	std::string result;
	std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd.c_str(), "r"), pclose);
	if (!pipe) throw std::runtime_error("popen() failed!");
	while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
		result += buffer.data();
	}
	return result;
}

void ConfigHandler::baseInfoConfig(const httplib::Request& req, httplib::Response& res)
{
	std::lock_guard<std::mutex> lock(g_database_mutex);
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson resJson;
	nlohmann::json jsonPara = nlohmann::json::parse(req.body);
	std::string nc_ip;
	std::string  _item = jsonPara["item"];
	std::string  ver = jsonPara["ver"];
	std::string  author = jsonPara["author"];
	std::string data;
	std::string SubnetMask;
	if (jsonPara["data"].is_string() == false)
		data = jsonPara["data"].dump();
	else
		data = jsonPara["data"];
	if (_item == "DataService") {
		if (jsonPara.contains("SubnetMask") && jsonPara["SubnetMask"].is_number_integer()) {
			SubnetMask = std::to_string(jsonPara["SubnetMask"].get<int>());
		}
		else{
			resJson.json(17203, "未找到子网掩码字段");
			res.set_content(resJson.toJson(), "application/json");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17203;
			ErrorInfoPack(&errorInfoapi, "baseInfoConfig", "");
			return;

		}
		sqlite3_stmt* Servicestmt = nullptr;
		std::string Servicesql = "SELECT json_extract(Content, '$.NC.IP') as NC_IP FROM DeviceConfig WHERE Item = 'DataService';";
		auto ret = dbi->prepare(Servicesql, Servicestmt);
		if (ret == SQLITE_OK) {
			ret = sqlite3_step(Servicestmt);
			if (ret==SQLITE_ROW){
				nc_ip = dbi->dbStr(Servicestmt, 0);
				sqlite3_finalize(Servicestmt);
			}
			else {
				resJson.json(17065, "查找 Datebase失败");
				res.set_content(resJson.toJson(), "application/json");
				errorInfoapi.eErrLever = Error;
				errorInfoapi.ErrCode = 17065;
				sqlite3_finalize(Servicestmt);
				ErrorInfoPack(&errorInfoapi, "baseInfoConfig", "");
				return;
			}
		}
		else {
			resJson.json(17530, "查找 Datebase失败,SQL语法错误");
			res.set_content(resJson.toJson(), "application/json");
			sqlite3_finalize(Servicestmt);
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17530;
			ErrorInfoPack(&errorInfoapi, "baseInfoConfig", "");
			return;
		}
		std::string nc_ip_in_data = jsonPara["data"]["NC"]["IP"];
		std::string cmd = "nmcli -f IP4.ADDRESS device show eth2 | grep IP4.ADDRESS | awk '{print $2}' | cut -d/ -f2";
		std::string cidrStr = execCmd(cmd);
		cidrStr.erase(std::remove(cidrStr.begin(), cidrStr.end(), '\n'), cidrStr.end());  // 去除换行

		int cidr = std::stoi(cidrStr);
		if (nc_ip_in_data != nc_ip or cidrStr != SubnetMask) {
			int ret_ip = modifyip(nc_ip_in_data,SubnetMask);
			if (ret_ip != 1) {
				resJson.json(17067, "更改 Datebase失败");
				res.set_content(resJson.toJson(), "application/json");
				errorInfoapi.eErrLever = Error;
				errorInfoapi.ErrCode = 17067;
				ErrorInfoPack(&errorInfoapi, "baseInfoConfig", "");
				return;
			}
		}
	}
	sqlite3_stmt* stmt = nullptr;
	//sqlite3_stmt* select_stmt = nullptr;
	std::string sql = "update  DeviceConfig  set Content=?, author=?, UpdateTime=strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') where item=? and ver=?";
	auto rc = dbi->prepare(sql,stmt);
	rc += sqlite3_bind_blob(stmt, 1, data.c_str(), data.length(), NULL);
	rc += sqlite3_bind_text(stmt, 2, author.c_str(), author.length(), NULL);
	rc += sqlite3_bind_text(stmt, 3, _item.c_str(), _item.length(), NULL);
	rc += sqlite3_bind_text(stmt, 4, ver.c_str(), ver.length(), NULL);
	if (rc == SQLITE_OK)
	{
		rc = sqlite3_step(stmt);
		while (rc == SQLITE_LOCKED) { // 检查是否是数据库被锁定错误
			//printf("删除全部设备设置文件SQL语法错误: %s %d\n", dbi->errmsg(stmt), rc);
			std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 等待 100 毫秒
			rc = sqlite3_step(stmt); // 重新执行
			//printf("删除全部设备设置文件SQL语法错误: %s %d\n", dbi->errmsg(stmt), rc);
		}
		auto rc1 = dbi->affectedRows();
		if (rc == 101 or rc ==0 && rc1 == 1)
		{
			resJson.json(0, u8"修改设备" + _item + u8"配置成功");
			updateState(_item);
			if (_item == "DataService") {
				FileServerControl = true;
			}
			/*std::string record1, record2 = " ";
			getRecordsByitem(_item, record1, record2);
			std::string content;
			if (record1 != record2 and record2!=" " ) {
				std::string selectsql = "SELECT content FROM DeviceConfigMd5 WHERE md5code = ?";
				if (dbi->prepare(selectsql, select_stmt) == SQLITE_OK) {
					sqlite3_bind_text(select_stmt, 1, record2.c_str(), -1, SQLITE_STATIC);
					if (sqlite3_step(select_stmt) == SQLITE_ROW) {
						content = (const char*)sqlite3_column_text(select_stmt, 0);
					}
					else {
						resJson.json(17068, "查找设备配置信息失败");
						errorInfoapi.eErrLever = Error;
						errorInfoapi.ErrCode = 17068;
					}
				}
				else{
					resJson.json(17531, "查找设备配置信息失败,SQL语法错误");
					errorInfoapi.eErrLever = Error;
					errorInfoapi.ErrCode = 17531;
				}
				if (!content.empty()) {
					nlohmann::json source = nlohmann::json::parse(content);
					nlohmann::json target;
					target = nlohmann::json::parse(data);
					nlohmann::json patch = nlohmann::json::diff(source, target);
					if (!patch.empty()) {
						std::string patch_str = patch.dump();
						std::string insertsql = "INSERT INTO ConfigChangeLog ( Ver,Item,Content, Author) VALUES (?, ?, ?, ?)";
						sqlite3_stmt* insert_stmt = nullptr;
						auto change_rc= dbi->prepare(insertsql, insert_stmt);
						if (change_rc == SQLITE_OK) {
							sqlite3_bind_text(insert_stmt, 1, ver.c_str(), -1, SQLITE_STATIC);
							sqlite3_bind_text(insert_stmt, 2, _item.c_str(), -1, SQLITE_STATIC);
							sqlite3_bind_text(insert_stmt, 3, patch_str.c_str(), -1, SQLITE_STATIC);
							sqlite3_bind_text(insert_stmt, 4, author.c_str(), -1, SQLITE_STATIC);
							change_rc  = sqlite3_step(insert_stmt);
							sqlite3_finalize(insert_stmt);
						}
						else {
							printf("新增配置更改记录失败，SQL语法错误: %s\n", dbi->errmsg(insert_stmt));
							resJson.json(17532, "新增配置更改记录失败，SQL语法错误");
							errorInfoapi.eErrLever = Error;
							errorInfoapi.ErrCode = 17532;
						}
					}
					else {
						resJson.json(17063, u8"修改设备" + _item + u8"配置失败");
						errorInfoapi.eErrLever = Error;
						errorInfoapi.ErrCode = 17063;
					}
				}
			}
			else {
				content = "";
			}*/
		}
		else
		{
			printf("新增配置更改记录失败，SQL语法错误: %s\n", dbi->errmsg(stmt));
			resJson.json(17066, "更改设备配置信息失败");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17066;
		}
	}
	else
	{
		resJson.json(17518, u8"修改设备配置失败,SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17518;
	}
	res.set_content(resJson.toJson(), "application/json");
	sqlite3_finalize(stmt);
	//sqlite3_finalize(select_stmt);
	ErrorInfoPack(&errorInfoapi, "baseInfoConfig", "");
}

void ConfigHandler::getRecordsByitem(std::string item, std::string& record1, std::string& record2)
{
	std::string sql = "SELECT Content FROM DeviceConfigRecord WHERE Item = ? ORDER BY Id DESC LIMIT 2";
	sqlite3_stmt* stmt = nullptr;

	if (dbi->prepare(sql, stmt) == SQLITE_OK) {
		auto rc = sqlite3_bind_text(stmt, 1, item.c_str(), item.length(), SQLITE_STATIC);
		if (rc == SQLITE_OK) {
			int recordCount = 0;
			while (sqlite3_step(stmt) == SQLITE_ROW ) {
				std::string& record = (recordCount == 0) ? record1 : record2;
				record = (const char*)sqlite3_column_text(stmt, 0);
				recordCount++;
			}
		}
		sqlite3_finalize(stmt);
	}
}