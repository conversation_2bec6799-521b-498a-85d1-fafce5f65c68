﻿#include "FSM.h"
#include "FSM_SysInit.h"
#include "FSM_SysSelfCheckEntry.h"
#include "FSM_SysSelfCheck.h"
#include "FSM_SysReadyEntry.h"
#include "FSM_SysReady.h"
#include "FSM_SysSwitchProfileEntry.h"
#include "FSM_SysSwitchProfile.h"
#include "FSM_SysManualOpEntry.h"
#include "FSM_SysManualOp.h"
#include "FSM_SysFaultEntry.h"
#include "FSM_SysFault.h"
#include "FSM_SysRunSequence.h"
#include "sequence/Sequence.h"
#include "SysShareMemoryDefine.h"
#include "SysVarDefine.h"

bool bTrigTareForce = false;

TrigData sPowerRtrig;               //上电     上升沿触发
TrigData sPowerFtrig;               //下电     下降沿触发

TrigData sJogHomeRtrig;             //回原点   上升沿触发
TrigData sJogRefPosRtrig;           //回参考点 上升沿触发
TrigData sJogForwardRtrig;          //向前     上升沿触发
TrigData sJogBackwardRtrig;         //向后     上升沿触发

TrigData sRunSeqRtrig;              //运行     上升沿触发
TrigData sRunSeqFtrig;              //运行     下降沿触发
TrigData sJogTargetRtrig;           //运行到指定的位置

TrigData sTareForceRtrig;           //压力去皮

TrigData sStopSeqRtrig;              //停止     上升沿触发

TrigData sClearFault;               //错误清除 上升沿触发
TrigData sLoadProfileRtrig;         //导入工艺 上升沿触发
TrigData sIdNumberRtrig;            //切条形码 上升沿触发

ESys_State eCurrSysState;
const char* SysStateName[] = { "Sys_State_PreloadPara",    "Sys_State_SelfCheck",      "Sys_State_Init",            "Sys_State_Ready",   
													"Sys_State_Switch_Profile", "Sys_State_Manual_Op",       "Sys_State_RunSequence",    "Reserve7",
													"Reserve8",                  "Reserve9",                  "Reserve10",                "Reserve11",
													"Reserve12",                 "Reserve13",                 "Reserve14",                "Sys_State_Fault" };

//static FSM_Contlor_t FSM_Contlor;
/** RefreshTrigSingal       触发边沿监测信号  统一刷新
* @param[in]     None
* @param[in]     None         
* @return        None
*/
void RefreshTrigSingal()
{
    //刷新触发信号
    if (SYSTEM_MOTION_MODE)
    {
        sPowerRtrig.bTrigSignal = pSysShareData->sExCW.bPower;                  //上电   上升沿触发

        //下电   下降沿触发 
        sPowerFtrig.bTrigSignal = pSysShareData->sExCW.bPower;

        sJogHomeRtrig.bTrigSignal = pSysShareData->sExCW.bMoveToHomePos;          //回原点        上升沿触发
        sJogForwardRtrig.bTrigSignal = pSysShareData->sExCW.bJogForword;          //向前         上升沿触发
        sJogBackwardRtrig.bTrigSignal = pSysShareData->sExCW.bJogBackword;        //向后         上升沿触发
        sJogTargetRtrig.bTrigSignal = pSysShareData->sSysJogPara.bJogCustomCmd;   //向目标点     上升沿触发

        F_Trig(&sPowerFtrig);
        if (sPowerFtrig.bTrig || (pSysShareData->sExSW.eLastControlType != pSysShareData->sExSW.eControlType))
        {
            bPowerDownCmd = true;
            if((pSysShareData->sExSW.eLastControlType != pSysShareData->sExSW.eControlType)&& pSysShareData->SCSW.PowerState)
                bExecutePowerCmd = true;
        }
        else
            bPowerDownCmd = false;

        R_Trig(&sPowerRtrig);
        if (sPowerRtrig.bTrig && pSysShareData->sExSW.eLastControlType == pSysShareData->sExSW.eControlType)
        {
            bPowerUpCmd = true;
        }
        else
            bPowerUpCmd = false;


        R_Trig(&sJogHomeRtrig);
        if ((sJogHomeRtrig.bTrig &&pSysShareData->sExSW.eLastControlType == pSysShareData->sExSW.eControlType) ||
            ((pSysShareData->sExSW.eControlType == IO || pSysShareData->sExSW.eControlType == Fieldbus)
            && (pSysShareData->sExCW.bMoveToHomePos && !pSysShareData->sExCW.bJogBackword && !pSysShareData->sExCW.bJogForword)))
        {
            if (!bExecutePowerCmd)
            {
                bJogHomeCmd = true;
                bNewJogOrder = true;
                if (pSysShareData->sSysJogPara.bProfileHomeJogCmd)
                    bAppJogHomeCmd = true;
                else
                    bAppJogHomeCmd = false;
            }
        }
        else
        {
            bJogHomeCmd = false;
        }

        R_Trig(&sJogForwardRtrig);
        if ((sJogForwardRtrig.bTrig && pSysShareData->sExSW.eLastControlType == pSysShareData->sExSW.eControlType) ||
            ((pSysShareData->sExSW.eControlType == IO || pSysShareData->sExSW.eControlType == Fieldbus)
                && (pSysShareData->sExCW.bJogForword &&!pSysShareData->sExCW.bJogBackword &&!pSysShareData->sExCW.bMoveToHomePos)))
        {
            if (!bExecutePowerCmd)
            {
                bJogForwardCmd = true;
                bNewJogOrder = true;
            }
        }
        else
        {
            bJogForwardCmd = false;
        }

        R_Trig(&sJogBackwardRtrig);
        if ((sJogBackwardRtrig.bTrig && pSysShareData->sExSW.eLastControlType == pSysShareData->sExSW.eControlType) ||
            ((pSysShareData->sExSW.eControlType == IO || pSysShareData->sExSW.eControlType == Fieldbus)
                && (pSysShareData->sExCW.bJogBackword &&!pSysShareData->sExCW.bJogForword && !pSysShareData->sExCW.bMoveToHomePos)))
        {
            if (!bExecutePowerCmd)
            {
                bJogBackwardCmd = true;
                bNewJogOrder = true;
            }
        }
        else
        {
            bJogBackwardCmd = false;
        }

        R_Trig(&sJogTargetRtrig);
        if (sJogTargetRtrig.bTrig)
        {
            bJogTargetCmd = true;
            bNewJogOrder = true;
        }
        else
            bJogTargetCmd = false;
    }

    sStopSeqRtrig.bTrigSignal       = pSysShareData->sExCW.bStopSequence;
    sClearFault.bTrigSignal         = pSysShareData->sExCW.bFaultRest;               //错误清除上升沿触发
    sLoadProfileRtrig.bTrigSignal   = pSysShareData->sExCW.bMPSiwch;
    sIdNumberRtrig.bTrigSignal      = pSysShareData->sExCW.bUsePartId;

    sRunSeqRtrig.bTrigSignal        = pSysShareData->sExCW.bRunSequence;             //工艺运行
    sRunSeqFtrig.bTrigSignal        = pSysShareData->sExCW.bRunSequence;             //工艺暂停
    sTareForceRtrig.bTrigSignal     = pSysShareData->sExCW.bTareYChannel;            //压力去皮
    //刷新触发器
    R_Trig(&sRunSeqRtrig);
    F_Trig(&sRunSeqFtrig);
    if (sRunSeqFtrig.bTrig || (pSysShareData->sExSW.bSequenceBusy && pSysShareData->sExSW.eLastControlType != pSysShareData->sExSW.eControlType))
    {
    	Seq.bPauseRequest = true;
        dbgPrint(1, 0, "bPauseRequest cmd \n\n");
    }
    
    R_Trig(&sStopSeqRtrig);     //停止
    if (sStopSeqRtrig.bTrig && pSysShareData->sExSW.eLastControlType == pSysShareData->sExSW.eControlType)
    {
        dbgPrint(1, 0, "bStopRequest cmd \n\n");
    	Seq.bStopRequest = true;
        if (pSysShareData->sExSW.bSequencePaused)
            bPasuedStopCmd = true;
        else
            bPasuedStopCmd = false;
    }

    R_Trig(&sClearFault);
    if (sClearFault.bTrig || pSysShareData->bFaultInit)
    {
        if (bExecutePowerCmd)
            bExecutePowerCmd = false;
    	//printf("\n\nsClearFault.bTrig \n\n");
        if(pSysShareData->sExSW.SysFaultCode)
    	    pSysShareData->bExeClearAction = true;

        if (pSysShareData->bFaultInit)
        	pSysShareData->bFaultInit = false;
    }

    R_Trig(&sLoadProfileRtrig);

    R_Trig(&sIdNumberRtrig);
    if (sIdNumberRtrig.bTrig && !pSysShareData->sExSW.bSequenceBusy)
    {
        if ((gCurProfileSnData.bGlobalSource && gSysSnData.GlobalSnPara.bPartIdBindingSn) ||
            (!gCurProfileSnData.bGlobalSource && gCurProfileSnData.SnPara.bPartIdBindingSn))
        {
            pSysShareData->bNeedBindingPlcSn = true;
        }
       // rt_dbgPrint(1, 0, "bTrigSnBanding\n");
    }

    R_Trig(&sTareForceRtrig);
    if (sTareForceRtrig.bTrig && !pSysShareData->sExSW.bSequenceBusy)
    {
    	bTrigTareForce = true;
        pSysShareData->sExSW.bTareYChanneled = false;
        //rt_dbgPrint(1, 0, "sTareForceRtrig\n");
    }
}     


/** FSM   系统状态机
* @param[in]     pSCCW          = 系统当前控制源 控制字
* @param[in]     pSCSW          = 系统状态
* @param[in]     pSeqRef        = Sequence数据
* @param[in]     pRunContext    = 记录数据
* @return        None
*/
bool bErrortrig = false;
TrigData errTrig;
ESys_State lastSysState = Sys_State_PreloadPara;

void FSM(sEXCW* psExCW, sEXSW* psExSW)
{
    eCurrSysState = pSysShareData->sExSW.eSysState;
   
    //传感器去皮
    if(pSysShareData->gSysCounter %100 ==0)
        SensorTare(&bTrigTareForce);

    //判断是否进入错误态      一旦进入，下面的判断不需要再次执行了
    switch (eCurrSysState)
    {
    case Sys_State_PreloadPara:
        FsmSysSelfCheckEntry();
        break;
    case Sys_State_SelfCheck:                //硬件自检
        FsmSysSelfCheck();
        break;
    case Sys_State_Init :                    //数据加载
         FsmSysInit();
        break;
    case Sys_State_Ready:                   //系统就绪态 
        FsmSysReady();
        break;
    case Sys_State_Switch_Profile:          //工艺切换
        FsmSysSwitchProfile();
        break;
    case Sys_State_Manual_Op :              //手动 (点动、上电、下点、去皮、手动回原地、手动回参考点)
        FsmSysManualOp();
        break;
    case Sys_State_RunSequence:
        FsmSysRunSequence(psExCW,psExSW);
        break;
    case Sys_State_Fault :                  
        FsmSysFault();
        break;
    default:
        break;
    }

    if (eCurrSysState != lastSysState)
    {
        //printf( "FSM_State:[%s -> %s]\n", SysStateName[lastSysState], SysStateName[eCurrSysState]);
        if (eCurrSysState!= lastSysState )
        {
        	lastSysState = eCurrSysState;
        }
    }
}



