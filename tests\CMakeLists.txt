project(press_unit_tests C CXX)
enable_testing()
set(CMAKE_CXX_STANDARD 11)

# 头文件路径（相对工程根目录）
include_directories(
  ${CMAKE_SOURCE_DIR}/core
  ${CMAKE_SOURCE_DIR}/pressRt/dsp
)

# 寻找 Threads 跨平台线程库
find_package(Threads REQUIRED)

# 优先查找系统已安装的 GTest；找不到再用 FetchContent 拉取
include(FetchContent)
find_package(GTest QUIET CONFIG)
if(NOT GTest_FOUND)
  FetchContent_Declare(
    googletest
    URL https://github.com/google/googletest/archive/refs/tags/v1.14.0.zip
  )
  set(BUILD_GMOCK OFF CACHE BOOL "" FORCE)
  set(INSTALL_GTEST OFF CACHE BOOL "" FORCE)
  FetchContent_MakeAvailable(googletest)
endif()

# 单测目标与源文件；引入 pressRt/dsp/dsp.cc 供 MoveAvg 测试
add_executable(press_tests
  test_ringbuffer.cc
  test_dsp_moveavg.cc
  test_trig.cc
  test_core_datatype.cc
  ${CMAKE_SOURCE_DIR}/pressRt/dsp/dsp.cc
)

# 链接 Core（由 core/CMakeLists.txt 生成）+ GTest + 线程库
target_link_libraries(press_tests
  PRIVATE
    Core
    Threads::Threads
    GTest::gtest
    GTest::gtest_main
)

# 注册测试并输出 XML 报告（ctest 时会生成）
add_test(NAME press_tests
  COMMAND press_tests --gtest_output=xml:${CMAKE_BINARY_DIR}/press_tests_report.xml
)