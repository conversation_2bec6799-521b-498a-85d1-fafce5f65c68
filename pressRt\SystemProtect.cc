#include "SystemProtect.h"
#include "ethercat.h"
#include "motion.h"
#include "SysShareMemoryDefine.h"
#include <string.h>
#include "SysVarDefine.h"
#include "rttimer.h"

//电机监控
SysProtectSet       sSysProtectPara;
/* MotorMonitoring
* @param[in]     None
* @param[in]     jDynaVar
*@return        ErrorCode
*/
ErrorInfo errorInfo_Drive;
ErrorInfo MotorMonitoring()
{
	if (pSysShareData->gEthercatDeviceInOp)
	{
		errorInfo_Drive = MC_ServoErrorCode(&pSysShareData->AxisMc[0].Axis);
		if (errorInfo_Drive.ErrCode)
		{
			if (driveType == INOVANCE_DRIVE)
			{
				if (errorInfo_Drive.ErrCode == 40024)   //急停触发
				{
					pSysShareData->sExSW.bEStopTrig = true;
					pSysShareData->sExSW.bGStopTrig = true;
				}
				else if (pSysShareData->sExSW.SysFaultCode == 40025)   //光栅触发
				{
					pSysShareData->sExSW.bEStopTrig = false;
					pSysShareData->sExSW.bGStopTrig = true;
				}
			}
			else if (driveType == RAYNEN_DRIVE)
			{
				if (pSysShareData->sExSW.SysFaultCode == 42056)
				{
					pSysShareData->sExSW.bEStopTrig = true;
					pSysShareData->sExSW.bGStopTrig = true;
				}
			}
		}
		else
		{
			pSysShareData->sExSW.bEStopTrig = false;	//急停触发
			pSysShareData->sExSW.bGStopTrig = false;	//光栅触发
		}
	}

	ErrorInfoPack(&errorInfo_Drive, "MotorMonitoring", "");
	return errorInfo_Drive;
}

/*
 MotionParaProtect
* @param[in]     None
* @param[in]     None
* @return        ErrorCode
*/
float32 rtPosDiff;
ErrorInfo errorInfo_Motion;
ErrorInfo MotionParaProtect()
{
	memset(&errorInfo_Motion, 0, sizeof(ErrorInfo));
	if (!errorInfo_Motion.ErrCode && pSysShareData->sExSW.eSysState > Sys_State_Init)
	{
		if (pSysShareData->AxisMc[0].Axis.Cfg.MotorPlusePerRevolution != 8388608 &&
			pSysShareData->AxisMc[0].Axis.Cfg.ShaftPlusePerRevolution != 10000000)                      //电子齿轮比设置异常
		{
			errorInfo_Motion.ErrCode = 8600;
			errorInfo_Motion.eErrLever = HwError;
		}

		if (!errorInfo_Motion.ErrCode && pSysShareData->AxisMc[0].Axis.Cfg.Tranlatepara == 0)           //传动比设置异常
		{
			errorInfo_Motion.ErrCode = 8601;
			errorInfo_Motion.eErrLever = HwError;
		}
	}

	if (!errorInfo_Motion.ErrCode && pSysShareData->sExSW.bPowerState)                                //跟随误差过大  fAllowPosErr
	{
		if (!sSysProtectPara.sSysPosPara.bNotUsePosFllowErr)                // Sdo:0x50FF01 开启或关掉功能
		{
			rtPosDiff = abs(pSysShareData->AxisMc[0].Axis.LogicFbk.fPos - pSysShareData->AxisMc[0].Axis.LogicRef.fPos);
			if (rtPosDiff > sSysProtectPara.sSysPosPara.fMaxPosDiff)
			{
				sSysProtectPara.sSysPosPara.fMaxPosDiff = rtPosDiff;
			}

			if (rtPosDiff > sSysProtectPara.sSysPosPara.fAllowPosDiffValue)     // Sdo:0x50FF04
			{
				errorInfo_Motion.ErrCode = 5604;
				errorInfo_Motion.eErrLever = MjError;
			}
		}

		if (!sSysProtectPara.sSysForceCurrPara.bNotUseCurentForceErr)   // Sdo:0x50FF10 开启或关掉功能   力比异常   系统超过额定扭矩  但是压力过小
		{
			if (sSysProtectPara.sSysForceCurrPara.fForceRatio <= 0.01)
			{
				errorInfo_Motion.ErrCode = 5612;                        //电流保护参数异常，请检查保护参数Sdo:0x50FF11
				errorInfo_Motion.eErrLever = MjError;
			}
			else
			{
				if (abs(pSysShareData->AxisMc[0].Axis.Motor.LinkVar.TorqueFbk_Percent) > 1000 &&                              //TorqueFbk_Percent :1000 表示 额定扭矩
					abs(pSysShareData->sSysFbkVar.fSenosrVarFbk) < (pSysShareData->sSysFbkVar.fSenosrVarMax * sSysProtectPara.sSysForceCurrPara.fForceRatio))
				{
					errorInfo_Motion.ErrCode = 8501;
					errorInfo_Motion.eErrLever = MjError;
				}
			}

			//if(abs(pSysShareData->AxisMc[0].Axis.Motor.LinkVar.TorqueFbk_Percent) > 500)
			//    rt_dbgPrint(1, 0, "TorqueFbk_Percent:%d  fSenosrVarFbk:%f  fSenosrVarMax*fForceRatio:%f\n",
			//        abs(pSysShareData->AxisMc[0].Axis.Motor.LinkVar.TorqueFbk_Percent),
			//        abs(pSysShareData->sSysFbkVar.fSenosrVarFbk),
			//        pSysShareData->sSysFbkVar.fSenosrVarMax * sSysProtectPara.sSysForceCurrPara.fForceRatio);
		}
	}

	if (sSysProtectPara.sSysPosPara.bResetMaxPosDiff)
	{
		sSysProtectPara.sSysPosPara.bResetMaxPosDiff = false;
		sSysProtectPara.sSysPosPara.fMaxPosDiff = 0;
	}

	ErrorInfoPack(&errorInfo_Motion, "MotionParaProtect", "");
	return errorInfo_Motion;
}


/*
 SystemTimeCheck                //系统启动时检查系统的时间   如果时间
* @param[in]     None
* @param[in]     None
* @return        ErrorInfo
*/
ErrorInfo SystemTimeCheck(char* psTime)
{
	ErrorInfo errorInfo_SysTime;
	memset(&errorInfo_SysTime, 0, sizeof(ErrorInfo));
	uint8 itestIndex = 0;
	bool bTimeErr = false;
	if (psTime != NULL)
	{
		if (strlen(psTime) >= 10)//2025-03-12    
		{
			uint8 uScanResult = sscanf(psTime, "%04d-%02d-%02d", &sSysProtectTime.tm_year, &sSysProtectTime.tm_mon, &sSysProtectTime.tm_mday);
			if (uScanResult == 3)
			{
				GetSystemTime();
				if (sSysProtectTime.tm_year >= 2025 && sSysProtectTime.tm_mon != 0 && sSysProtectTime.tm_mday != 0)
				{
					if (pLoaclTime->tm_year < sSysProtectTime.tm_year)
					{
						bTimeErr = true;
					}
					else
					{
						if (pLoaclTime->tm_mon < sSysProtectTime.tm_mon)
						{
							bTimeErr = true;
						}
						else if (pLoaclTime->tm_mon == sSysProtectTime.tm_mon)
						{
							if (pLoaclTime->tm_mday < sSysProtectTime.tm_mday)
								bTimeErr = true;
						}
					}

					if (bTimeErr)
					{
						itestIndex = 1;
						errorInfo_SysTime.ErrCode = 906;
						errorInfo_SysTime.eErrLever = Error;
					}
				}
				else
				{
					itestIndex = 2;
					errorInfo_SysTime.ErrCode = 906;
					errorInfo_SysTime.eErrLever = Error;
				}
			}
			else
			{
				itestIndex = 3;
				errorInfo_SysTime.ErrCode = 906;
				errorInfo_SysTime.eErrLever = Error;
			}
		}
		else
		{
			itestIndex = 4;
			errorInfo_SysTime.ErrCode = 906;
			errorInfo_SysTime.eErrLever = Error;
		}
	}

	if (errorInfo_SysTime.ErrCode)
	{
		GetSystemTime();    //刷新时间，不然pLoaclTime会段错误
		rt_dbgPrint(1, 0, "errIndex:%d SystemTimeCheck Error pLocalTime:%04d-%02d-%02d   sSysProtectTime:%04d-%02d-%02d\n",
			itestIndex,
			pLoaclTime->tm_year, pLoaclTime->tm_mon, pLoaclTime->tm_mday,
			sSysProtectTime.tm_year, sSysProtectTime.tm_mon, sSysProtectTime.tm_mday);
	}

	ErrorInfoPack(&errorInfo_SysTime, "SystemTimeCheck", "");
	return errorInfo_SysTime;
}

/*
 MotorMonitoring
* @param[in]     None
* @param[in]     jDynaVar
* @return        ErrorInfo
*/
ErrorInfo errorInfo_SysProject;
ErrorInfo SystemProtect()
{
	memset(&errorInfo_SysProject, 0, (uint64)sizeof(ErrorInfo));

	if (SYSTEM_MOTION_MODE)
	{
		errorInfo_SysProject = MotorMonitoring();

		if (!errorInfo_SysProject.ErrCode)
			errorInfo_SysProject = MotionParaProtect();
	}

	if (pSysShareData->gSysCounter % 1200000 == 0)          //10分钟检查一次   10min = 1200000 = 10*60*2000 (2000是1000 000/500us)
		errorInfo_SysProject = SystemTimeCheck((char*)&sSysDeviceSetInfo.sProtectTime);

	ErrorInfoPack(&errorInfo_SysProject, "SystemProtect", "");
	return errorInfo_SysProject;
}
