#include "SnMaker.h"
#include <stdio.h>
#include "SysShareMemoryDefine.h"
#include "time.h"
#include "rttimer.h"
#include <math.h>
#include "SysVarDefine.h"
#include "MainWork.h"

SysSnPara				gSysSnData;                //包含全局的Sn数据
ProfileSnPara			gCurProfileSnData;

uint16 iSnLenth = 0;
/* SpecialCharDeal                      //从Json文本中load Motion参数
* @param[in]     char *pStrSn           //需要做字符替换的数据
* @param[in]     char *pSwitchedStr     //替换过后的数据    
* @return        NULL
*/
char* pChar = NULL;
char* pSwitchChar = NULL;
void SpecialCharDeal(char* pStrSn)
{
    // [*]10#42;[/]10#47; [:]10#58 ;[?]10#63;[\]10#92 
    if (strlen(pStrSn))
    {
        for (uint16 uIndexChar = 0; uIndexChar < strlen(pStrSn); uIndexChar++)
        {
            pChar = pStrSn + uIndexChar;
            if (*pChar == 42 || *pChar == 47 || *pChar == 58 || *pChar == 63 || *pChar == 92)
            {
                *pChar = 32;//替换成空格
            }
        }
    }
}

/* SnAddSeparator              //从Json文本中load Motion参数
* @param[in]     SysSnPara* pSysSnData
* @param[in]     ProfileSnPara* pCurProfileSnPara
* @return        ErrorInfo
*/
ErrorInfo SnAddSeparator()
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));
    if (gCurProfileSnData.bGlobalSource && strlen(gSysSnData.GlobalSnPara.sSepparator))
    {
        if (iSnLenth + strlen(gSysSnData.GlobalSnPara.sSepparator) <= 255)
        {
            strcat((char*)gSysSnData.sSn, (const char*)gSysSnData.GlobalSnPara.sSepparator);
            iSnLenth += strlen((const char*)gSysSnData.GlobalSnPara.sSepparator);
        }
        else
        {
            errorInfo.ErrCode = 5200;// 4;
            errorInfo.eErrLever = Error;
        }
    }
    else if (!gCurProfileSnData.bGlobalSource && strlen(gCurProfileSnData.SnPara.sSepparator))
    {
        if (iSnLenth + strlen(gCurProfileSnData.SnPara.sSepparator) <= 255)
        {
            strcat((char *)gSysSnData.sSn, (const char *)gCurProfileSnData.SnPara.sSepparator);
            iSnLenth += strlen((const char *)gCurProfileSnData.SnPara.sSepparator);
        }
        else
        {
            errorInfo.ErrCode = 5201;//5;
            errorInfo.eErrLever = Error;
        }
    }
    
    ErrorInfoPack(&errorInfo, "SnAddSeparator", "OverLen:%d", iSnLenth + strlen(gSysSnData.GlobalSnPara.sSepparator));
    return errorInfo;
}


/* BindingResult   //将判定结果绑定在SN前面
* @param[in]     SysSnPara* pSysSnData
* @param[in]     ProfileSnPara* pCurProfileSnPara
* @param[in]     bool *pbResultOk
* @return        ErrorCode
*/
ErrorInfo BindingResult(SysSnPara* pSysSnData, ProfileSnPara* pCurProfileSnPara,bool* pbResultOk)
{
    //rt_dbgPrint(1, 0, "Sn maker\n");
    ErrorInfo errorInfo;
    memset(&errorInfo, 0,(uint64) sizeof(ErrorInfo));
    str255  sTmpSn;
    memset(sTmpSn,0,255);
    if (*pbResultOk)
    {
        strcat(sTmpSn, "OK");
    }
    else
    {
        strcat(sTmpSn, "NOK");
    }

    if (pCurProfileSnPara->bGlobalSource && pSysSnData->GlobalSnPara.bIdBindingResult)
    {
        if(strlen(sTmpSn) + strlen(pSysSnData->GlobalSnPara.sSepparator) + strlen(pSysSnData->sSn)< 255)
        {
            strcat(sTmpSn, pSysSnData->GlobalSnPara.sSepparator);
            strcat(sTmpSn, pSysSnData->sSn);

            memcpy(pSysSnData->sSn, sTmpSn,(uint64)(strlen(sTmpSn)+1));
            //rt_dbgPrint(1, 0, "sSn Global:%s\n", pSysSnData->sSn);
        }
        else
        {
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 5240;
        }
    }
    else if(!pCurProfileSnPara->bGlobalSource && pCurProfileSnPara->SnPara.bIdBindingResult)
    {
        if (strlen(sTmpSn) + strlen(pCurProfileSnPara->SnPara.sSepparator) + strlen(pSysSnData->sSn) < 255)
        {
            strcat(sTmpSn, pCurProfileSnPara->SnPara.sSepparator);
            strcat(sTmpSn, pSysSnData->sSn);

            memcpy(pSysSnData->sSn, sTmpSn, strlen(sTmpSn) + 1);
          //  rt_dbgPrint(1, 0, "sSn Profile:%s\n", pSysSnData->sSn);
        }
        else
        {
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 5241;
        }
    }

    ErrorInfoPack(&errorInfo, "SnBindingOK", "");
    return errorInfo;
}


/* BindingPartId             绑定PLC 传过来的条码
* @param[in]     None
* @return        ErrorInfo
*/
char  sYear[4], sMon[2], sDay[2], sHour[2], sMin[2], sSec[2];
ErrorInfo BindingPartId()
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));
    memset(&gSysSnData.PlcSn, 0, (uint64)sizeof(255));

    int recLen = strlen(gSysSnData.RecPlcSn);
    rt_dbgPrint(1, 0, "sSn RecPlcSn:%s\n", gSysSnData.RecPlcSn);
    if (recLen > 0)
    {
        //检查从PLC 收到的条形码中的非法数据
        memcpy(&gSysSnData.PlcSn, gSysSnData.RecPlcSn, strlen(gSysSnData.RecPlcSn) + 1);
        SpecialCharDeal((char*)&gSysSnData.PlcSn);

        if (strcmp(gSysSnData.RecPlcSn, gSysSnData.RecPlcSn_Last) == 0)       //当前和上次不同
        {
            if (gCurProfileSnData.bGlobalSource)
            {
                if (gSysSnData.GlobalSnPara.sPlcSnSetPara.bSameSnError)
                {
                    errorInfo.eErrLever = Error;
                    errorInfo.ErrCode = 5230;// 150;
                }
                else if (gSysSnData.GlobalSnPara.sPlcSnSetPara.bSameSnBingTime)
                {
                    sprintf(sYear, "%04d",pLoaclTime->tm_year);
                    sprintf(sMon, "%02d", pLoaclTime->tm_mon);
                    sprintf(sDay, "%02d", pLoaclTime->tm_mday);
                    sprintf(sHour, "%02d",pLoaclTime->tm_hour);
                    sprintf(sMin, "%02d", pLoaclTime->tm_min);
                    sprintf(sSec, "%02d", pLoaclTime->tm_sec);
                    if ((strlen(gSysSnData.PlcSn) +
                        6 * strlen(gSysSnData.GlobalSnPara.sSepparator) +
                        (strlen(sYear)) +
                        (strlen(sMon)) +
                        (strlen(sDay)) +
                        (strlen(sHour)) +
                        (strlen(sMin)) +
                        (strlen(sSec))) < 255)
                    {
                        strcat(gSysSnData.PlcSn, gSysSnData.GlobalSnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sYear);        //年
                        strcat(gSysSnData.PlcSn, gSysSnData.GlobalSnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sMon);        //月
                        strcat(gSysSnData.PlcSn, gSysSnData.GlobalSnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sDay);        //日
                        strcat(gSysSnData.PlcSn, gSysSnData.GlobalSnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sHour);        //时
                        strcat(gSysSnData.PlcSn, gSysSnData.GlobalSnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sMin);        //分
                        strcat(gSysSnData.PlcSn, gSysSnData.GlobalSnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sSec);        //秒
                    }
                    else
                    {
                        errorInfo.eErrLever = Error;
                        errorInfo.ErrCode = 5232;
                    }
                }
            }
            else
            {
                if (gCurProfileSnData.SnPara.sPlcSnSetPara.bSameSnError)
                {
                    errorInfo.eErrLever = Error;
                    errorInfo.ErrCode = 5230;
                }
                else if (gCurProfileSnData.SnPara.sPlcSnSetPara.bSameSnBingTime)
                {
                    sprintf(sYear, "%04d",pLoaclTime->tm_year);
                    sprintf(sMon, "%02d", pLoaclTime->tm_mon);
                    sprintf(sDay, "%02d", pLoaclTime->tm_mday);
                    sprintf(sHour, "%02d",pLoaclTime->tm_hour);
                    sprintf(sMin, "%02d", pLoaclTime->tm_min);
                    sprintf(sSec, "%02d", pLoaclTime->tm_sec);
                    if ((strlen(gSysSnData.PlcSn) + 
                        6*strlen(gCurProfileSnData.SnPara.sSepparator) +
                        (strlen(sYear)) +
                        (strlen(sMon))+
                        (strlen(sDay))+
                        (strlen(sHour))+
                        (strlen(sMin)) +
                        (strlen(sSec))) < 255)
                    {
                        strcat(gSysSnData.PlcSn, gCurProfileSnData.SnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sYear);        //年
                        strcat(gSysSnData.PlcSn, gCurProfileSnData.SnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sMon);        //月
                        strcat(gSysSnData.PlcSn, gCurProfileSnData.SnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sDay);        //日
                        strcat(gSysSnData.PlcSn, gCurProfileSnData.SnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sHour);        //时
                        strcat(gSysSnData.PlcSn, gCurProfileSnData.SnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sMin);        //分
                        strcat(gSysSnData.PlcSn, gCurProfileSnData.SnPara.sSepparator);
                        strcat(gSysSnData.PlcSn, sSec);        //秒
                    }
                    else
                    {
                        errorInfo.eErrLever = Error;
                        errorInfo.ErrCode = 5232;
                    }
                }
            }
        }
        pSysShareData->sExSW.bPartIdUsed = true;
    }
    else
    {
        if ((gCurProfileSnData.bGlobalSource && gSysSnData.GlobalSnPara.sPlcSnSetPara.bNullSnError) ||
            (!gCurProfileSnData.bGlobalSource && gCurProfileSnData.SnPara.sPlcSnSetPara.bNullSnError))
        {
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 5231;
        }
    }
    if (!errorInfo.ErrCode)
        pSysShareData->bHadDealPlcSn = true;
    else
        pSysShareData->bHadDealPlcSn = false;

    //rt_dbgPrint(1, 2, "sIdNumberRtrig bTrig  bGlobalSource:%s bPartIdBindingSn:%s\n",
    //    gCurProfileSnData.bGlobalSource ? "true" : "false",
    //    gCurProfileSnData.bGlobalSource ? (gSysSnData.GlobalSnPara.bPartIdBindingSn ? "true" : "false") : (gCurProfileSnData.SnPara.bPartIdBindingSn ? "true" : "false"));
   
    ErrorInfoPack(&errorInfo, "UpdateStateInfo", "");
    return errorInfo;
}


/* SnCat              //从Json文本中load Motion参数
* @param[in]     SysSnPara* pSysSnData
* @param[in]     ProfileSnPara* pCurProfileSnPara
* @return        ErrorCode
*/

ErrorInfo SnCat(SysSnPara* pSysSnData, ProfileSnPara* pCurProfileSnPara)
{
    //rt_dbgPrint(1, 0, "Sn maker\n");
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));

    ErrorInfo errorInfo_Separator;
    memset(&errorInfo_Separator, 0, (uint64)sizeof(ErrorInfo));

    uint64 lMaxCounter = 0;
    char  sTmp[255];
    memset(pSysSnData->sSn, 0, 255);
    iSnLenth = 0;
    //SnParaTest();
    if (pCurProfileSnPara->bGlobalSource)   //使用全局的
    {
        if (pSysSnData->GlobalSnPara.bUseCustomHeader && strlen(pSysSnData->GlobalSnPara.sCustomHeader))
        {
            if (iSnLenth + strlen(pSysSnData->GlobalSnPara.sCustomHeader) <= 255)
            {
                strcat(pSysSnData->sSn, pSysSnData->GlobalSnPara.sCustomHeader);
                iSnLenth += strlen(pSysSnData->GlobalSnPara.sCustomHeader);
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = 5203;//7;
                    errorInfo.eErrLever = Error;
                }
            }
        }

        if (pSysSnData->GlobalSnPara.bUseStationName && strlen(sSysDeviceSetInfo.sDeviveName))
        {
            if (iSnLenth + strlen(sSysDeviceSetInfo.sDeviveName) + strlen(gSysSnData.GlobalSnPara.sSepparator) <= 255)
            {
                if (strlen(pSysSnData->sSn) > 0)
                {
                    errorInfo_Separator = SnAddSeparator();
                    if (errorInfo_Separator.ErrCode && errorInfo.ErrCode == 0)
                    {
                        errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                        errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                    }
                }

                if (!errorInfo.ErrCode)
                {
                    strcat(pSysSnData->sSn, sSysDeviceSetInfo.sDeviveName);
                    iSnLenth = iSnLenth + strlen(sSysDeviceSetInfo.sDeviveName) + strlen(gSysSnData.GlobalSnPara.sSepparator);
                }
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = 5204;//8;
                    errorInfo.eErrLever = Error;
                }
            }
        }

        if (pSysSnData->GlobalSnPara.bUseProfileId)
        {
            if (iSnLenth + 3 < 255)
            {
                if (strlen(pSysSnData->sSn) > 0)
                    errorInfo_Separator = SnAddSeparator();
                if (!errorInfo_Separator.ErrCode)
                {
                    memset(&sTmp, 0, sizeof(sTmp));
                    sprintf(sTmp, "%03d", pSysShareData->SCSW.ActiveMPId);
                    strcat(pSysSnData->sSn, sTmp);
                    iSnLenth += 3;
                }
                else
                {
                    if (errorInfo.ErrCode == 0)
                    {
                        errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                        errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                    }
                }
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = 5202;//6;
                    errorInfo.eErrLever = Error;
                }
            }
        }
    }
    else
    {
        if (pCurProfileSnPara->SnPara.bUseCustomHeader && strlen(pCurProfileSnPara->SnPara.sCustomHeader))
        {
            if (strlen(pCurProfileSnPara->SnPara.sCustomHeader) + iSnLenth <= 255)
            {
                strcat(pSysSnData->sSn, pCurProfileSnPara->SnPara.sCustomHeader);
                iSnLenth += strlen(pCurProfileSnPara->SnPara.sCustomHeader);
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = 5205;//9;
                    errorInfo.eErrLever = Error;
                }
            }
        }

        if (pCurProfileSnPara->SnPara.bUseStationName && strlen(sSysDeviceSetInfo.sDeviveName))
        {
            if (strlen(pSysSnData->sSn) > 0)
                errorInfo_Separator = SnAddSeparator();
            if (!errorInfo_Separator.ErrCode)
            {
                if (iSnLenth + strlen(sSysDeviceSetInfo.sDeviveName) <= 255)
                {
                    strcat(pSysSnData->sSn, sSysDeviceSetInfo.sDeviveName);
                    iSnLenth = iSnLenth + strlen(sSysDeviceSetInfo.sDeviveName);
                }
                else
                {
                    if (errorInfo.ErrCode == 0)
                    {
                        errorInfo.ErrCode = 5206;//10;
                        errorInfo.eErrLever = Error;
                    }
                }
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                    errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                }
            }
        }

        if (pCurProfileSnPara->SnPara.bUseProfileId)
        {
            if (strlen(pSysSnData->sSn) > 0)
            {
                errorInfo_Separator = SnAddSeparator();
            }
            if (!errorInfo_Separator.ErrCode)
            {
                if (iSnLenth + 3 <= 255)
                {
                    memset(&sTmp, 0, sizeof(sTmp));
                    sprintf(sTmp, "%03d", pSysShareData->SCSW.ActiveMPId);
                    strcat(pSysSnData->sSn, sTmp);
                    iSnLenth += 3;
                }
                else
                {
                    if (errorInfo.ErrCode == 0)
                    {
                        errorInfo.ErrCode = 5207;//11;
                        errorInfo.eErrLever = Error;
                    }
                }
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                    errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                }
            }
        }
    }

    //时间
    for (uint8 j = 0; j <= 5; j++)
    {
        if (!errorInfo.ErrCode)
        {
            if ((pCurProfileSnPara->bGlobalSource && pSysSnData->GlobalSnPara.DatasFormat[j] == YYYY)
                || (!pCurProfileSnPara->bGlobalSource && pCurProfileSnPara->SnPara.DatasFormat[j] == YYYY))
            {
                if (strlen(pSysSnData->sSn) > 0)
                {
                    errorInfo_Separator = SnAddSeparator();
                }
                if (!errorInfo_Separator.ErrCode)
                {
                    if (iSnLenth + 4 <= 255)
                    {
                        memset(&sTmp, 0, sizeof(sTmp));
                        GetSystemTime();
                        sprintf(sTmp, "%04d", pLoaclTime->tm_year);
                        strcat(pSysSnData->sSn, sTmp);
                        iSnLenth += 4;
                    }
                    else
                    {
                        if (errorInfo.ErrCode == 0)
                        {
                            errorInfo.ErrCode = 5208;//12;
                            errorInfo.eErrLever = Error;
                        }
                    }
                }
                else
                {
                    if (errorInfo.ErrCode == 0)
                    {
                        errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                        errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                    }
                }
            }
        }

        if ((pCurProfileSnPara->bGlobalSource && pSysSnData->GlobalSnPara.DatasFormat[j] == MONTH)
            || (!pCurProfileSnPara->bGlobalSource && pCurProfileSnPara->SnPara.DatasFormat[j] == MONTH))
        {
            if (strlen(pSysSnData->sSn) > 0)
            {
                errorInfo_Separator = SnAddSeparator();
            }

            if (!errorInfo_Separator.ErrCode)
            {
                if (iSnLenth + 2 <= 255)
                {
                    memset(&sTmp, 0, sizeof(sTmp));
                    GetSystemTime();
                    sprintf(sTmp, "%02d", pLoaclTime->tm_mon);
                    strcat(pSysSnData->sSn, sTmp);
                    iSnLenth += 2;
                }
                else
                {
                    if (errorInfo.ErrCode == 0)
                    {
                        errorInfo.ErrCode = 5209;//13;
                        errorInfo.eErrLever = Error;
                    }
                }
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                    errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                }
            }
        }

        if ((pCurProfileSnPara->bGlobalSource && pSysSnData->GlobalSnPara.DatasFormat[j] == DD)
            || (!pCurProfileSnPara->bGlobalSource && pCurProfileSnPara->SnPara.DatasFormat[j] == DD))
        {
            if (strlen(pSysSnData->sSn) > 0)
            {
                errorInfo_Separator = SnAddSeparator();
            }

            if (!errorInfo_Separator.ErrCode)
            {
                if (iSnLenth + 2 <= 255)
                {
                    memset(&sTmp, 0, sizeof(sTmp));
                    GetSystemTime();
                    sprintf(sTmp, "%02d", pLoaclTime->tm_mday);
                    strcat(pSysSnData->sSn, sTmp);
                    iSnLenth += 2;
                }
                else
                {
                    if (errorInfo.ErrCode == 0)
                    {

                        errorInfo.ErrCode = 5210;//14;
                        errorInfo.eErrLever = Error;
                    }
                }
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                    errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                }
            }
        }

        if ((pCurProfileSnPara->bGlobalSource && pSysSnData->GlobalSnPara.DatasFormat[j] == HH)
            || (!pCurProfileSnPara->bGlobalSource && pCurProfileSnPara->SnPara.DatasFormat[j] == HH))
        {
            if (strlen(pSysSnData->sSn) > 0)
            {
                errorInfo_Separator = SnAddSeparator();
            }

            if (!errorInfo_Separator.ErrCode)
            {
                if (iSnLenth + 2 <= 255)
                {
                    memset(&sTmp, 0, sizeof(sTmp));
                    GetSystemTime();
                    sprintf(sTmp, "%02d", pLoaclTime->tm_hour);
                    strcat(pSysSnData->sSn, sTmp);
                    iSnLenth += 2;
                }
                else
                {
                    if (errorInfo.ErrCode == 0)
                    {
                        errorInfo.ErrCode = 5211;//15;
                        errorInfo.eErrLever = Error;
                    }
                }
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                    errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                }
            }
        }

        if ((pCurProfileSnPara->bGlobalSource && pSysSnData->GlobalSnPara.DatasFormat[j] == MN)
            || (!pCurProfileSnPara->bGlobalSource && pCurProfileSnPara->SnPara.DatasFormat[j] == MN))
        {
            if (strlen(pSysSnData->sSn) > 0)
            {
                errorInfo_Separator = SnAddSeparator();
            }

            if (!errorInfo.ErrCode)
            {
                if (iSnLenth + 2 <= 255)
                {
                    memset(&sTmp, 0, sizeof(sTmp));
                    GetSystemTime();
                    sprintf(sTmp, "%02d", pLoaclTime->tm_min);
                    strcat(pSysSnData->sSn, sTmp);
                    iSnLenth += 2;
                }
                else
                {
                    if (errorInfo.ErrCode == 0)
                    {
                        errorInfo.ErrCode = 5212;//16;
                        errorInfo.eErrLever = Error;
                    }
                }
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                    errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                }
            }
        }


        if ((pCurProfileSnPara->bGlobalSource && pSysSnData->GlobalSnPara.DatasFormat[j] == SS)
            || (!pCurProfileSnPara->bGlobalSource && pCurProfileSnPara->SnPara.DatasFormat[j] == SS))
        {
            if (strlen(pSysSnData->sSn) > 0)
            {
                errorInfo_Separator = SnAddSeparator();
            }

            if (!errorInfo_Separator.ErrCode)
            {
                if (iSnLenth + 2 <= 255)
                {
                    memset(&sTmp, 0, sizeof(sTmp));
                    GetSystemTime();
                    sprintf(sTmp, "%02d", pLoaclTime->tm_sec);
                    strcat(pSysSnData->sSn, sTmp);
                    iSnLenth += 2;
                }
                else
                {
                    if (errorInfo.ErrCode == 0)
                    {
                        errorInfo.ErrCode = 5213;//17;
                        errorInfo.eErrLever = Error;
                    }
                }
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                    errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                }
            }
        }
    }


    if (pCurProfileSnPara->bGlobalSource)
    {
        pSysSnData->GlobalSnCounter++;
        if (pSysSnData->GlobalSnCounter < pSysSnData->GlobalSnPara.StartWithCounter)        //重置计数器
        {
            pSysSnData->GlobalSnCounter = pSysSnData->GlobalSnPara.StartWithCounter;
        }

        if (pSysSnData->bUseGlobalCounter)
        {
           // rt_dbgPrint(1, 0, "\n Global1 Counter:%ld \n", pSysSnData->GlobalSnCounter);
            lMaxCounter = 0;
            if (pSysSnData->GlobalSnPara.bCounterBindingSn)
            {
                if (pSysSnData->GlobalSnPara.CounterDigits > 0 && pSysSnData->GlobalSnPara.CounterDigits <= 10)
                {
                    lMaxCounter = pow(10, (pSysSnData->GlobalSnPara.CounterDigits)) - 1;
                    if (pSysSnData->GlobalSnCounter > lMaxCounter)
                    {
                        if (errorInfo.ErrCode == 0)
                        {
                            errorInfo.ErrCode = 5214;//18;
                            errorInfo.eErrLever = Error;
                        }
                    }
                }
                else  if (pSysSnData->GlobalSnPara.CounterDigits > 10)
                {
                    if (errorInfo.ErrCode == 0)
                    {
                        errorInfo.ErrCode = 5215;
                        errorInfo.eErrLever = Error;
                    }
                }
            }
        }
        else
        {
            pSysSnData->GlobalSnCounter++;          //哪怕只使用局部计数器、全局计数器还是需要累加     工艺的计数器需要每次运行结束后都存储起来、避免出现突然关机计数器错位。
            pSysSnData->CurProfileSnCounter++;
            if (pSysSnData->CurProfileSnCounter < pCurProfileSnPara->SnPara.StartWithCounter)
                pSysSnData->CurProfileSnCounter = pCurProfileSnPara->SnPara.StartWithCounter;

            //根据位数  获取限定位数的最大值   例如6位数   最大值为 999999
            lMaxCounter = 0;
            if (pCurProfileSnPara->SnPara.CounterDigits > 0 && pCurProfileSnPara->SnPara.CounterDigits <= 10)
            {
                lMaxCounter = pow(10, (pCurProfileSnPara->SnPara.CounterDigits)) - 1;
                if (pSysSnData->CurProfileSnCounter > lMaxCounter)
                {
                    if (errorInfo.ErrCode == 0)
                    {
                      //  rt_dbgPrint(1, 0, "1 Counter:%ld \n", pSysSnData->GlobalSnCounter);
                        errorInfo.ErrCode = 5217; //20;
                        errorInfo.eErrLever = Error;
                    }
                }
                // rt_dbgPrint(1, 0, "1 ErrCode:%d  CounterDigits:%ld lMaxCounter:%ld\n", errorInfo.ErrCode, pCurProfileSnPara->SnPara.CounterDigits, lMaxCounter);
            }
            else  if (pSysSnData->GlobalSnPara.CounterDigits > 10)
            {
                errorInfo.ErrCode = 5218; //20;
                errorInfo.eErrLever = Error;
            }
        }

        if (pSysSnData->GlobalSnPara.bCounterBindingSn)
        {
            if (strlen(pSysSnData->sSn) > 0)
            {
                errorInfo_Separator = SnAddSeparator();
            }
            if (!errorInfo_Separator.ErrCode)
            {
                if (pSysSnData->bUseGlobalCounter)
                {
                    if (iSnLenth + pSysSnData->GlobalSnPara.CounterDigits <= 255)
                    {
                        memset(&sTmp, 0, (uint64)sizeof(sTmp));
                        sprintf(sTmp, "%0*ld", pSysSnData->GlobalSnPara.CounterDigits, pSysSnData->GlobalSnCounter);
                        strcat(pSysSnData->sSn, sTmp);
                        iSnLenth = iSnLenth + pSysSnData->GlobalSnPara.CounterDigits;
                    }
                    else
                    {
                        if (errorInfo.ErrCode == 0)
                        {
                            errorInfo.ErrCode = 5216; //19;
                            errorInfo.eErrLever = Error;
                        }
                    }
                }
                else
                {
                    if (iSnLenth + pCurProfileSnPara->SnPara.CounterDigits <= 255)
                    {
                        memset(&sTmp, 0, (uint64)sizeof(sTmp));
                        sprintf(sTmp, "%0*ld", pCurProfileSnPara->SnPara.CounterDigits, pSysSnData->CurProfileSnCounter);
                        strcat(pSysSnData->sSn, sTmp);
                        iSnLenth = iSnLenth + pCurProfileSnPara->SnPara.CounterDigits;
                    }
                    else
                    {
                        if (errorInfo.ErrCode == 0)
                        {
                            errorInfo.ErrCode = 5219; //21;
                            errorInfo.eErrLever = Error;
                        }
                    }
                }
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                    errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                }
            }
        }
    }
    else
    {
        pSysSnData->GlobalSnCounter++;          //哪怕只使用局部计数器、全局计数器还是需要累加     工艺的计数器需要每次运行结束后都存储起来、避免出现突然关机计数器错位。
        if (pCurProfileSnPara->bUseGlobalCounter)
        {
            if (pSysSnData->GlobalSnPara.CounterDigits > 0 && pSysSnData->GlobalSnPara.CounterDigits <= 10)
            {
                lMaxCounter = 0;
                lMaxCounter = pow(10, (pSysSnData->GlobalSnPara.CounterDigits)) - 1;
                if (pSysSnData->GlobalSnCounter > lMaxCounter)
                {
                    if (pSysSnData->GlobalSnPara.bCounterBindingSn)
                    {
                        if (errorInfo.ErrCode == 0)
                        {
                            errorInfo.ErrCode = 5214;//18;
                            errorInfo.eErrLever = Error;
                        }
                    }
                }
            }
            else  if (pSysSnData->GlobalSnPara.CounterDigits > 10)
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = 5215;
                    errorInfo.eErrLever = Error;
                }
            }
        }
        else
        {
            pSysSnData->CurProfileSnCounter++;
            if (pSysSnData->CurProfileSnCounter < pCurProfileSnPara->SnPara.StartWithCounter)
                pSysSnData->CurProfileSnCounter = pCurProfileSnPara->SnPara.StartWithCounter;

            //根据位数  获取限定位数的最大值   例如6位数   最大值为 999999
            lMaxCounter = 0;
            if (pCurProfileSnPara->SnPara.CounterDigits > 0 && pCurProfileSnPara->SnPara.CounterDigits <= 10)
            {
                lMaxCounter = pow(10, (pCurProfileSnPara->SnPara.CounterDigits)) - 1;
                if (pSysSnData->CurProfileSnCounter > lMaxCounter)
                {
                    if (errorInfo.ErrCode == 0)
                    {
                        //rt_dbgPrint(1, 0, "2 Counter:%ld \n", pSysSnData->GlobalSnCounter);
                        //printf("2 Counter:%ld \n", pSysSnData->GlobalSnCounter);
                        errorInfo.ErrCode = 5217;
                        errorInfo.eErrLever = Error;
                    }
                }

                //rt_dbgPrint(1, 0, "2 ErrCode:%d  CounterDigits:%ld lMaxCounter:%ld\n", errorInfo.ErrCode, pCurProfileSnPara->SnPara.CounterDigits, lMaxCounter);
            }
            else if (pCurProfileSnPara->SnPara.CounterDigits > 10)
            {
                errorInfo.ErrCode = 5218;
                errorInfo.eErrLever = Error;
            }
        }


        if (pCurProfileSnPara->SnPara.bCounterBindingSn)
        {
            if (strlen(pSysSnData->sSn) > 0)
            {
                errorInfo_Separator = SnAddSeparator();
            }
            if (!errorInfo_Separator.ErrCode)
            {
                if (pCurProfileSnPara->bUseGlobalCounter)
                {
                    if (iSnLenth + pSysSnData->GlobalSnPara.CounterDigits <= 255)
                    {
                        memset(&sTmp, 0, sizeof(sTmp));
                        sprintf(sTmp, "%0*ld", pSysSnData->GlobalSnPara.CounterDigits, pSysSnData->GlobalSnCounter);
                        strcat(pSysSnData->sSn, sTmp);
                        iSnLenth = iSnLenth + pSysSnData->GlobalSnPara.CounterDigits;
                    }
                    else
                    {
                        if (errorInfo.ErrCode == 0)
                        {
                            errorInfo.ErrCode = 5216;
                            errorInfo.eErrLever = Error;
                        }
                    }
                }
                else
                {
                    if (iSnLenth + pCurProfileSnPara->SnPara.CounterDigits <= 255)
                    {
                        memset(&sTmp, 0, sizeof(sTmp));
                        sprintf(sTmp, "%0*ld", pCurProfileSnPara->SnPara.CounterDigits, pSysSnData->CurProfileSnCounter);
                        strcat(pSysSnData->sSn, sTmp);
                        iSnLenth = iSnLenth + pCurProfileSnPara->SnPara.CounterDigits;
                    }
                    else
                    {
                        if (errorInfo.ErrCode == 0)
                        {
                            errorInfo.ErrCode = 5219;// 21;
                            errorInfo.eErrLever = Error;
                        }
                    }
                }
            }
            else
            {
                if (errorInfo.ErrCode == 0)
                {
                    errorInfo.ErrCode = errorInfo_Separator.ErrCode;
                    errorInfo.eErrLever = errorInfo_Separator.eErrLever;
                }
            }
        }
    }

    //拼接是收到的SN号
    if (errorInfo.ErrCode == 0 && 
        pSysShareData->sFbRec.sFbCw.FielsbusRemote&&
        pSysShareData->bHadDealPlcSn)
    {
        if ((gCurProfileSnData.bGlobalSource && gSysSnData.GlobalSnPara.bPartIdBindingSn) ||
            (!gCurProfileSnData.bGlobalSource && gCurProfileSnData.SnPara.bPartIdBindingSn))
        {
            if (strlen(gSysSnData.PlcSn) > 0)
            {
                if (gCurProfileSnData.bGlobalSource && strlen(gSysSnData.GlobalSnPara.sSepparator))
                {
                    if (strlen(pSysSnData->sSn) + strlen(gSysSnData.GlobalSnPara.sSepparator) + 1 < 255)
                        strcat(pSysSnData->sSn, gSysSnData.GlobalSnPara.sSepparator);
                    else
                    {
                        errorInfo.ErrCode = 5225;
                        errorInfo.eErrLever = Error;
                    }
                }
                else if (!gCurProfileSnData.bGlobalSource && strlen(gCurProfileSnData.SnPara.sSepparator))
                {
                    if (strlen(pSysSnData->sSn) + strlen(gCurProfileSnData.SnPara.sSepparator) + 1 < 255)
                        strcat(pSysSnData->sSn, gCurProfileSnData.SnPara.sSepparator);
                    else
                    {
                        errorInfo.ErrCode = 5226;
                        errorInfo.eErrLever = Error;
                    }
                }

                if (errorInfo.ErrCode == 0)
                {
                    if (strlen(pSysSnData->sSn) + strlen(gSysSnData.PlcSn) + 1 < 255)
                    {
                        strcat(pSysSnData->sSn, gSysSnData.PlcSn);
                    }
                    else
                    {
                        errorInfo.ErrCode = 5227;
                        errorInfo.eErrLever = Error;
                    }
                }
            }
        }
    }

    ErrorInfoPack(&errorInfo, "SnMake", "");
    return errorInfo;
}



/* SnMaker              //从Json文本中load Motion参数
* @param[in]     None
* @param[in]     None
* @return        ErrorInfo
*/
TrigData        rRemoteTrig;
ErrorInfo SnMaker(bool *bBingdingResult)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));

    rRemoteTrig.bTrigSignal = pSysShareData->SCSW.FieldBusRemoted;
    R_Trig(&rRemoteTrig);
    if (rRemoteTrig.bTrig)
    {
        if ((gCurProfileSnData.bGlobalSource && gSysSnData.GlobalSnPara.bPartIdBindingSn) ||
            (!gCurProfileSnData.bGlobalSource && gCurProfileSnData.SnPara.bPartIdBindingSn))
        {
            pSysShareData->bHadDealPlcSn = false;
        }
        else
        {
            pSysShareData->bHadDealPlcSn = true;
        }
    }

    if (pSysShareData->SCSW.FieldBusRemoted &&  pSysShareData->SFCW.FielsbusRemote)
    {
        if ((gCurProfileSnData.bGlobalSource && gSysSnData.GlobalSnPara.bPartIdBindingSn ) ||
            (!gCurProfileSnData.bGlobalSource && gCurProfileSnData.SnPara.bPartIdBindingSn))
        {
            if ((gCurProfileSnData.bGlobalSource && !gSysSnData.GlobalSnPara.sPlcSnSetPara.bNullSnError)||
                (!gCurProfileSnData.bGlobalSource&& !gCurProfileSnData.SnPara.sPlcSnSetPara.bNullSnError))
            {
                pSysShareData->bAllowNotBingSn = true;
            }
            else
            {
                pSysShareData->bAllowNotBingSn = false;
            }
           
            if (pSysShareData->bNeedBindingPlcSn)
            {
                errorInfo = BindingPartId();
                pSysShareData->bNeedBindingPlcSn = false;
            }
        }
        else
        {
            pSysShareData->bHadDealPlcSn = true;
        }
    }
    else
    {
        pSysShareData->bAllowNotBingSn = true;
        pSysShareData->bHadDealPlcSn = true;
    }

    if (pSysShareData->uNeedSnMakerFlag && !errorInfo.ErrCode)
    {
        errorInfo = SnCat(&gSysSnData, &gCurProfileSnData);
        pSysShareData->uNeedSnMakerFlag = 0;
        //rt_dbgPrint(1, 0, "Sn SnCat\n");
    }

    ErrorInfoPack(&errorInfo, "SnMake", "");
    return errorInfo;
}