#include "FieldbusAux.h"
#include "rapidjson/prettywriter.h"
#include "rapidjson/document.h"
#include "rapidjson/error/en.h"
#include "rapidjson/error/error.h"
#include "SysVarDefine.h"
#include "sqliteapi/sqliteOp/SqliteOp.h"
#include "Global&LocalVar.h"
using namespace rapidjson;

const char* SysIoInName[] = { "IoRemote",		"FaultReset",       "Power",			"StopSequence",
							  "RunSequence",	"WaitContinue",      "MoveToHomePos",    "JogForward",
							  "JogBackward",     "Reserve10",        "Reserve11",        "" };

const char* SysIoOutName[] = { "IoRemoted",		"FaultState",       "PowerState",		"ResultOK",
							   "ResultNOK",		"SequenceEnd",      "SequenceRunning",  "Wait",
							   "HomePosReached", "IsStandStill",     "Reserve11"         ,"" };


/* UpdateFieldbusSetInfo
* @param[in]     bUpdateTrig
* @param[out]    None
* @return        ErrorInfo
*/
bool bFieldbusUsedGlobalVar;
ErrorInfo UpdateFieldbusSetInfo()
{
	ErrorInfo	errorInfo;
	uint8 uIndex = 0;
	str64 sTmpStr = "";

	memset(&errorInfo, 0, sizeof(ErrorInfo));

	//rt_dbgPrint(1, 0, "\n Update Fieldbus Info From DataBase \n\n");
	char* test_version = "V1.0.0";
	char* spFieldbus = "FieldBus";
	// 调用被测函数
	std::string DeviceConfigjson;
	errorInfo = ReadDeviceConfigJsonOp(spFieldbus, test_version, &DeviceConfigjson);

	bFieldbusUsedGlobalVar = false;
	SFieldbusConfigUnit sTmpFbConfig;
	//rt_dbgPrint(1, 0, "UpdateFieldbusSetInfo\n");
	if (!errorInfo.ErrCode)
	{
		//从数据库中获取 整个IO Json数据内容
		if (strcmp(DeviceConfigjson.c_str(), "0") != 0 && strlen((char*)DeviceConfigjson.c_str()) > 0)
		{
			//开始解析 数据
				//char* psIoSet;
			rapidjson::Document docFielsbusSet;
			docFielsbusSet.Parse((char*)DeviceConfigjson.c_str());

			if (docFielsbusSet.IsObject() && docFielsbusSet.HasMember("FieldBus"))
			{
				Value& jFieldbus = docFielsbusSet["FieldBus"];

				memset(&pSysShareData->sSysFieldbusInfo, 0, sizeof(pSysShareData->sSysFieldbusInfo));
				if (jFieldbus.IsObject() && jFieldbus.HasMember("Base"))
				{
					Value& jFieldbusBase = jFieldbus["Base"];
					if (jFieldbusBase.HasMember("BytesOrder") && jFieldbusBase["BytesOrder"].IsString())
					{
						memset(sTmpStr, 0, sizeof(str64));
						strcpy(sTmpStr, jFieldbusBase.HasMember("BytesOrder") ? jFieldbusBase["BytesOrder"].GetString() : "");

						if (strcmp(sTmpStr, "LittleEndian") == 0)
							pSysShareData->sSysFieldbusInfo.sBaseSet.eByteOrder = ELittleOrder;
						else if (strcmp(sTmpStr, "BigEndian") == 0)
							pSysShareData->sSysFieldbusInfo.sBaseSet.eByteOrder = EBigOrder;
					}

					if (jFieldbusBase.HasMember("CCLinkBaudRate") && jFieldbusBase["CCLinkBaudRate"].IsString())
					{
						str64 sTmpCCLinkBaudRate;
						memset(&sTmpCCLinkBaudRate, 0, 64);
						strcpy(sTmpCCLinkBaudRate, jFieldbusBase["CCLinkBaudRate"].GetString());
						if (strcmp(sTmpCCLinkBaudRate, "156kbps") == 0)
						{
							pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.Baudrate = _156kps;
						}
						else if (strcmp(sTmpCCLinkBaudRate, "625kbps") == 0)     //625kbps
						{
							pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.Baudrate = _625kps;
						}
						else if (strcmp(sTmpCCLinkBaudRate, "2.5Mbps") == 0)
						{
							pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.Baudrate = _2d5Mbps;
						}
						else if (strcmp(sTmpCCLinkBaudRate, "5Mbps") == 0)
						{
							pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.Baudrate = _5Mbps;
						}
						else if (strcmp(sTmpCCLinkBaudRate, "10Mbps") == 0)
						{
							pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.Baudrate = _10Mbps;
						}
						else
						{
							errorInfo.ErrCode = 130;
							errorInfo.eErrLever = Error;
						}
					}

					if (jFieldbusBase.HasMember("CCLinkStationNO") && jFieldbusBase["CCLinkStationNO"].IsInt())
					{
						pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.NodeAddress = jFieldbusBase["CCLinkStationNO"].GetInt();
					}

					if (jFieldbusBase.HasMember("CCLinkExtensionCycles") && jFieldbusBase["CCLinkExtensionCycles"].IsInt())
					{
						pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.ExtensionCycles = jFieldbusBase["CCLinkExtensionCycles"].GetInt();
					}

					if (jFieldbusBase.HasMember("CCLinkOccupiedStations") && jFieldbusBase["CCLinkOccupiedStations"].IsInt())
					{
						pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.OccupiedStations = jFieldbusBase["CCLinkOccupiedStations"].GetInt();
					}

					if (jFieldbusBase.HasMember("ProfibusStationNO") && jFieldbusBase["ProfibusStationNO"].IsInt())
					{
						pSysShareData->sSysFieldbusInfo.sBaseSet.sProfibusSetConfig.NodeAddress = jFieldbusBase["ProfibusStationNO"].GetInt();
					}

					if (!errorInfo.ErrCode)
					{
						if (pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.Baudrate != pSysShareData->sCCLConfigInfo.Baudrate ||
							pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.NodeAddress != pSysShareData->sCCLConfigInfo.NodeAddress ||
							pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.OccupiedStations != pSysShareData->sCCLConfigInfo.OccupiedStations ||
							pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.ExtensionCycles != pSysShareData->sCCLConfigInfo.ExtensionCycles ||
							pSysShareData->sSysFieldbusInfo.sBaseSet.sProfibusSetConfig.NodeAddress != pSysShareData->sProfibusConfigInfo.NodeAddress)
						{
							pSysShareData->bNeedResetFieldbus = true;
							pSysShareData->sCCLConfigInfo.Baudrate = pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.Baudrate;
							pSysShareData->sCCLConfigInfo.NodeAddress = pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.NodeAddress;
							pSysShareData->sCCLConfigInfo.OccupiedStations = pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.OccupiedStations;
							pSysShareData->sCCLConfigInfo.ExtensionCycles = pSysShareData->sSysFieldbusInfo.sBaseSet.sCcLinkSetConfig.ExtensionCycles;
							pSysShareData->sProfibusConfigInfo.NodeAddress = pSysShareData->sSysFieldbusInfo.sBaseSet.sProfibusSetConfig.NodeAddress;
						}
						if (!pSysShareData->bHadLoadFieldbusPara)
							pSysShareData->bHadLoadFieldbusPara = true;

						//rt_dbgPrint(1, 0, "\n bNeedResetFieldbus:%s bHadLoadFieldbusPara:%s\n\n",
						//	pSysShareData->bNeedResetFieldbus?"true":"false",
						//	pSysShareData->bHadLoadFieldbusPara ? "true" : "false");
					}
				}
				else
				{
					errorInfo.ErrCode = 101;
					errorInfo.eErrLever = Error;
				}

				//这样处理是其他参数正常解析  但是需要将当前错误报出来
				if (errorInfo.ErrCode)
				{
					ErrorInfoPack(&errorInfo, "UpdateFieldbusSetInfo FieldBus Base", "");
					memset(&errorInfo, 0, sizeof(ErrorInfo));
				}

				if (jFieldbus.IsObject() && jFieldbus.HasMember("Input") && jFieldbus["Output"].IsArray())
				{
					pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount = 0;
					for (auto& jInput : jFieldbus["Input"].GetArray())
					{
						if (jInput.IsNull())
						{
							errorInfo.ErrCode = 114;
							errorInfo.eErrLever = Error;
						}
						else
						{
							if (pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount < FieldbusConfigMaxNumber)
							{
								if (jInput.HasMember("AddrBit") && jInput["AddrBit"].IsInt() &&
									jInput.HasMember("AddrByte") && jInput["AddrByte"].IsInt() &&
									jInput.HasMember("Size") && jInput["Size"].IsInt() &&
									jInput.HasMember("DataType") && jInput["DataType"].IsString())
								{
									pSysShareData->sSysFieldbusInfo.sFieldbusInput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount].AddrBit = jInput["AddrBit"].GetInt();
									pSysShareData->sSysFieldbusInfo.sFieldbusInput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount].AddrByte = jInput["AddrByte"].GetInt();

									pSysShareData->sSysFieldbusInfo.sFieldbusInput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount].Size = jInput["Size"].GetInt();
									strcpy(sTmpStr, jInput.HasMember("DataType") ? jInput["DataType"].GetString() : "");

									pSysShareData->sSysFieldbusInfo.sFieldbusInput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount].eDataType = DataTypeStr2Enum(sTmpStr);
								}
								else
								{
									errorInfo.ErrCode = 103;
									errorInfo.eErrLever = Error;
								}

								if (!errorInfo.ErrCode)
								{
									if (jInput.HasMember("SdoKey") && jInput["SdoKey"].IsString() &&
										jInput.HasMember("Name") && jInput["Name"].IsString())
									{
										strcpy(pSysShareData->sSysFieldbusInfo.sFieldbusInput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount].sName, jInput.HasMember("Name") ? jInput["Name"].GetString() : "");
										strcpy(pSysShareData->sSysFieldbusInfo.sFieldbusInput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount].sSdoKey, jInput.HasMember("SdoKey") ? jInput["SdoKey"].GetString() : "");
										str16 sSdoGroup;
										strncpy(sSdoGroup, pSysShareData->sSysFieldbusInfo.sFieldbusInput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount].sSdoKey, 6);
										if (strcmp(sSdoGroup, "0xB001") == 0 || strcmp(sSdoGroup, "0xB002") == 0)
										{
											bFieldbusUsedGlobalVar = true;
											pSysShareData->sSysFieldbusInfo.sFieldbusInput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount].bGlobalOrLocalVar = true;
											if (!FindGlobalLocalVar(&pSysShareData->sSysFieldbusInfo.sFieldbusInput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount], &sTmpFbConfig))
											{
												errorInfo.ErrCode = 172;
												errorInfo.eErrLever = Error;
											}
										}
										else
											pSysShareData->sSysFieldbusInfo.sFieldbusInput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount].bGlobalOrLocalVar = false;
									}
									else
									{
										errorInfo.ErrCode = 104;
										errorInfo.eErrLever = Error;
									}
								}
							}
							else
							{
								errorInfo.ErrCode = 102;
								errorInfo.eErrLever = Error;
							}
							pSysShareData->sSysFieldbusInfo.sFieldbusInput.uUnitCount++;
						}
					}
				}

				if (errorInfo.ErrCode)
				{
					ErrorInfoPack(&errorInfo, "UpdateFieldbusSetInfo Input", "");
					memset(&errorInfo, 0, sizeof(ErrorInfo));
				}

				if (jFieldbus.IsObject() && jFieldbus.HasMember("Output") && jFieldbus["Output"].IsArray())
				{
					pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount = 0;
					for (auto& jOutput : jFieldbus["Output"].GetArray())
					{
						if (jOutput.IsNull())
						{
							errorInfo.ErrCode = 115;
							errorInfo.eErrLever = Error;
						}
						else
						{
							if (pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount < FieldbusConfigMaxNumber)
							{
								if (jOutput.HasMember("AddrBit") && jOutput["AddrBit"].IsNumber() &&
									jOutput.HasMember("AddrByte") && jOutput["AddrByte"].IsNumber() &&
									jOutput.HasMember("Size") && jOutput["Size"].IsNumber() &&
									jOutput.HasMember("DataType") && jOutput["DataType"].IsString())
								{
									pSysShareData->sSysFieldbusInfo.sFieldbusOutput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount].AddrBit = jOutput["AddrBit"].GetInt();
									pSysShareData->sSysFieldbusInfo.sFieldbusOutput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount].AddrByte = jOutput["AddrByte"].GetInt();

									pSysShareData->sSysFieldbusInfo.sFieldbusOutput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount].Size = jOutput["Size"].GetInt();
									strcpy(sTmpStr, jOutput.HasMember("DataType") ? jOutput["DataType"].GetString() : "");

									pSysShareData->sSysFieldbusInfo.sFieldbusOutput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount].eDataType = DataTypeStr2Enum(sTmpStr);
								}
								else
								{
									errorInfo.ErrCode = 106;
									errorInfo.eErrLever = Error;
								}

								if (!errorInfo.ErrCode)
								{
									if (jOutput.HasMember("SdoKey") && jOutput["SdoKey"].IsString() &&
										jOutput.HasMember("Name") && jOutput["Name"].IsString())
									{
										strcpy(pSysShareData->sSysFieldbusInfo.sFieldbusOutput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount].sName, jOutput.HasMember("Name") ? jOutput["Name"].GetString() : "");
										strcpy(pSysShareData->sSysFieldbusInfo.sFieldbusOutput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount].sSdoKey, jOutput.HasMember("SdoKey") ? jOutput["SdoKey"].GetString() : "");

										str16 sSdoGroup;
										strncpy(sSdoGroup, pSysShareData->sSysFieldbusInfo.sFieldbusOutput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount].sSdoKey, 6);
										if (strcmp(sSdoGroup, "0xB001") == 0 || strcmp(sSdoGroup, "0xB002") == 0)
										{
											bFieldbusUsedGlobalVar = true;
											pSysShareData->sSysFieldbusInfo.sFieldbusOutput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount].bGlobalOrLocalVar = true;

											if (!FindGlobalLocalVar(&pSysShareData->sSysFieldbusInfo.sFieldbusOutput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount], &sTmpFbConfig))
											{
												errorInfo.ErrCode = 107;
												errorInfo.eErrLever = Error;
											}
										}
										else
											pSysShareData->sSysFieldbusInfo.sFieldbusOutput.sFieldbusUnit[pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount].bGlobalOrLocalVar = false;
									}
									else
									{
										errorInfo.ErrCode = 107;
										errorInfo.eErrLever = Error;
									}
								}
							}
							else
							{
								errorInfo.ErrCode = 105;
								errorInfo.eErrLever = Error;
							}
							pSysShareData->sSysFieldbusInfo.sFieldbusOutput.uUnitCount++;
						}
					}
				}

				if (errorInfo.ErrCode)
				{
					ErrorInfoPack(&errorInfo, "UpdateFieldbusSetInfo Output", "");
					memset(&errorInfo, 0, sizeof(ErrorInfo));
				}
			}
			else
			{
				errorInfo.ErrCode = 100;
				errorInfo.eErrLever = Error;
			}

			if (errorInfo.ErrCode)
			{
				ErrorInfoPack(&errorInfo, "UpdateFieldbusSetInfo FieldBus", "");
				memset(&errorInfo, 0, sizeof(ErrorInfo));
			}

			if (docFielsbusSet.IsObject() && docFielsbusSet.HasMember("IO"))
			{
				Value& jIO = docFielsbusSet["IO"];
				if (jIO.HasMember("IOEnabled") && jIO["IOEnabled"].IsBool() &&
					jIO.IsObject() && jIO["Input"].IsArray() && jIO["Output"].IsArray())
				{
					Value& jInputs = jIO["Input"];
					Value& jOutputs = jIO["Output"];
					memset(&pSysShareData->sSysIoSetInfo, 0, sizeof(SIoSetInfo));
					pSysShareData->sSysIoSetInfo.bEnable = jIO.HasMember("IOEnabled") ? jIO["IOEnabled"].GetBool() : false;
					for (auto& jInput : jInputs.GetArray())
					{
						if (jInput.HasMember("Addr") && jInput["Addr"].IsInt())
						{
							uIndex = jInput["Addr"].GetInt();
							if (uIndex >= 0 && uIndex < 12)
							{
								if (jInput.HasMember("Enabled") && jInput["Enabled"].IsBool() &&
									jInput.HasMember("Name") && jInput["Name"].IsString() &&
									jInput.HasMember("SdoKey") && jInput["SdoKey"].IsString())
								{
									pSysShareData->sSysIoSetInfo.SIoSetInfo_In[uIndex].uIoIndex = uIndex;
									pSysShareData->sSysIoSetInfo.SIoSetInfo_In[uIndex].bActive = jInput.HasMember("Enabled") ? jInput["Enabled"].GetBool() : false;

									strcpy(pSysShareData->sSysIoSetInfo.SIoSetInfo_In[uIndex].sName, jInput.HasMember("Name") ? jInput["Name"].GetString() : "");
									strcpy(pSysShareData->sSysIoSetInfo.SIoSetInfo_In[uIndex].sKey, jInput.HasMember("SdoKey") ? jInput["SdoKey"].GetString() : "");

									for (uint8 j = 0; j < IO_NUMBER; j++)
									{
										if (strcmp(pSysShareData->sSysIoSetInfo.SIoSetInfo_In[uIndex].sName, SysIoInName[j]) == 0)
										{
											pSysShareData->sSysIoSetInfo.SIoSetInfo_In[uIndex].uIoAddr = j;
											break;
										}
									}
								}
								else
								{
									errorInfo.ErrCode = 110;
									errorInfo.eErrLever = Error;
									break;
								}
							}
							else
							{
								errorInfo.ErrCode = 109;
								errorInfo.eErrLever = Error;
								break;
							}
						}
						else
						{
							errorInfo.ErrCode = 108;
							errorInfo.eErrLever = Error;
							break;
						}
					}

					if (errorInfo.ErrCode)
					{
						ErrorInfoPack(&errorInfo, "UpdateFieldbusSetInfo IO Input", "");
						memset(&errorInfo, 0, sizeof(ErrorInfo));
					}

					for (auto& jOutput : jOutputs.GetArray())
					{
						if (jOutput.HasMember("Addr") && jOutput["Addr"].IsInt())
						{
							uIndex = jOutput["Addr"].GetInt();
							if (uIndex >= 0 && uIndex < 12)
							{
								if (jOutput.HasMember("Enabled") && jOutput["Enabled"].IsBool() &&
									jOutput.HasMember("Name") && jOutput["Name"].IsString() &&
									jOutput.HasMember("SdoKey") && jOutput["SdoKey"].IsString())
								{
									pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[uIndex].uIoIndex = uIndex;
									pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[uIndex].bActive = jOutput.HasMember("Enabled") ? jOutput["Enabled"].GetBool() : false;

									strcpy(pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[uIndex].sName, jOutput.HasMember("Name") ? jOutput["Name"].GetString() : "");
									strcpy(pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[uIndex].sKey, jOutput.HasMember("SdoKey") ? jOutput["SdoKey"].GetString() : "");
									for (uint8 j = 0; j < 12; j++)
									{
										if (strcmp(pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[uIndex].sName, SysIoOutName[j]) == 0)
										{
											pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[uIndex].uIoAddr = j;
											//  rt_dbgPrint(1, 0, "\n uIoAddr:%d uIndex:%d SysIoOutName:%s\n\n", j, uIndex, SysIoOutName[j]);
											break;
										}
									}
								}
								else
								{
									errorInfo.ErrCode = 113;
									errorInfo.eErrLever = Error;
									break;
								}
							}
							else
							{
								errorInfo.ErrCode = 112;
								errorInfo.eErrLever = Error;
								break;
							}
						}
						else
						{
							errorInfo.ErrCode = 111;
							errorInfo.eErrLever = Error;
							break;
						}
					}

					if (errorInfo.ErrCode)
					{
						ErrorInfoPack(&errorInfo, "UpdateFieldbusSetInfo IO Outputs", "");
						memset(&errorInfo, 0, sizeof(ErrorInfo));
					}
				}
			}
		}
		else
		{
			rt_dbgPrint(1, 0, "\n Fieldbus Info Is NULL \n\n");
		}
	}

	//if (errorInfo.ErrCode == 0)
	//	pSysShareData->bNeedResetFieldbusData = false;

	ErrorInfoPack(&errorInfo, "UpdateFieldbusSetInfo", "");

	return errorInfo;
}



