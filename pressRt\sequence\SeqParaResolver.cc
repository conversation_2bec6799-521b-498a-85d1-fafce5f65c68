#include "SeqParaResolver.h"
#include <stdio.h>
#include <string.h>
#include "motion.h"
#include "SysShareMemoryDefine.h"
#include "SequenceItemInit.h"
#include "EO.h"
#include "Global&LocalVar.h"

/* TypeSwitch                  将Json中的字符串数据类型转化成 U8
* @param[in]                            char* psDataType
* @return                               EDataType
*/
EDataType DataTypeSwitch(char* psDataType)
{
	if (strcmp(psDataType, "NONE") == 0)
	{
		return DT_None;
	}
	else if (strcmp(psDataType, "Bool") == 0)
	{
		return DT_Bool;
	}
	else if (strcmp(psDataType, "I8") == 0)
	{
		return DT_I8;
	}
	else if (strcmp(psDataType, "U8") == 0)
	{
		return DT_U8;
	}
	else if (strcmp(psDataType, "I16") == 0)
	{
		return DT_I16;
	}
	else if (strcmp(psDataType, "U16") == 0)
	{
		return DT_U16;
	}
	else if (strcmp(psDataType, "I32") == 0)
	{
		return DT_I32;
	}
	else if (strcmp(psDataType, "U32") == 0)
	{
		return DT_U32;
	}
	else if (strcmp(psDataType, "I64") == 0)
	{
		return DT_I64;
	}
	else if (strcmp(psDataType, "U64") == 0)
	{
		return DT_U64;
	}
	else if (strcmp(psDataType, "F32") == 0)
	{
		return DT_F32;
	}
	else if (strcmp(psDataType, "F64") == 0)
	{
		return DT_F64;
	}
	else if (strcmp(psDataType, "Str") == 0)
	{
		return DT_Str;
	}
	else if (strcmp(psDataType, "WStr") == 0)
	{
		return DT_WStr;
	}

	return DT_None;
}


/* LoadDynaVar
* @param[in]     pDynaVar
* @param[in]     jDynaVar
* @return        ErrorCode
*/
SDOEntryDesc* pTmpEntry;
ErrorInfo LoadDynaVar(DynaVar* pDynaVar, Value& jDynaVar)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	if (jDynaVar.IsNumber())
	{
		pDynaVar->eType = DynaConst;
		pDynaVar->eDataType = DT_F32;
		if (jDynaVar.IsFloat())
			pDynaVar->VarUnion.fVar = jDynaVar.GetFloat();
		else if (jDynaVar.IsInt())
			pDynaVar->VarUnion.fVar = (float32)jDynaVar.GetInt();
		else
		{
			errorInfo.ErrCode = 24015;
			errorInfo.eErrLever = Error;
		}

		if (!errorInfo.ErrCode)
		{
			pDynaVar->fVarSwitched = pDynaVar->VarUnion.fVar;
			pDynaVar->lfVarSwitched = (float64)pDynaVar->VarUnion.fVar;
		}
	}
	else if (jDynaVar.IsObject())
	{
		str64 seType;
		str64 sDataType;
		if (jDynaVar["Name"].IsString())
			strcpy(pDynaVar->sName, jDynaVar["Name"].GetString());

		if (!errorInfo.ErrCode)
		{
			if (jDynaVar["SdoKey"].IsString())
			{
				strcpy(pDynaVar->szKey, jDynaVar["SdoKey"].GetString());
			}
		}

		if (jDynaVar["SourceType"].IsString())
		{
			memset(seType, 0, 64);
			strcpy(seType, jDynaVar["SourceType"].GetString());
			bool bSourceMatch = false;
			if (strcmp(seType, "IO") == 0)
			{
				//在IO 列表中查询是否存在

				if (pSysShareData->sSysIoSetInfo.bEnable)
				{
					for (uint8 index = 0; index < IO_NUMBER; index++)
					{
						if (pSysShareData->sSysIoSetInfo.SIoSetInfo_In[index].bActive)
						{
							if (strcmp(pDynaVar->szKey, pSysShareData->sSysIoSetInfo.SIoSetInfo_In[index].sKey) == 0 &&
								strcmp(pDynaVar->sName, pSysShareData->sSysIoSetInfo.SIoSetInfo_In[index].sName) == 0)
							{
								bSourceMatch = true;
								break;
							}
						}

						if (pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[index].bActive)
						{
							if (strcmp(pDynaVar->szKey, pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[index].sKey) == 0 &&
								strcmp(pDynaVar->sName, pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[index].sName) == 0)
							{
								bSourceMatch = true;
								break;
							}
						}
					}

					if (!bSourceMatch)
					{
						errorInfo.ErrCode = 24701;
						errorInfo.eErrLever = Error;
					}
					else
					{
						pDynaVar->eType = DynaIo;
					}
				}
				else
				{
					errorInfo.ErrCode = 24700;
					errorInfo.eErrLever = Error;
				}
			}
			else if (strcmp(seType, "Global") == 0)
			{
				if (sSysGlobalVar.uVarCount > 0)
				{
					memset(sDataType, 0, 64);
					strcpy(sDataType, jDynaVar["DataType"].GetString());
					EDataType eTmpDataType = DataTypeSwitch((char*)&sDataType);

					for (uint8 index = 0; index < sSysGlobalVar.uVarCount; index++)                     //在Global 列表中查询是否存在
					{
						if (strcmp(pDynaVar->sName, sSysGlobalVar.aSVariableVar[index].sName) == 0)
						{

							bSourceMatch = true;
							bSeqUsedGlobalVar = true;
							pDynaVar->eDataType = sSysGlobalVar.aSVariableVar[index].eDataType;
							if (strcmp(pDynaVar->szKey, sSysGlobalVar.aSVariableVar[index].sKey) != 0)
							{
								dbgPrint(1, 1, "pDynaVar->szKey:%s  sSysGlobalVar.sKey:%s\n", pDynaVar->szKey, sSysGlobalVar.aSVariableVar[index].sKey);
								strcpy(pDynaVar->szKey, sSysGlobalVar.aSVariableVar[index].sKey);
							}
							//dbgPrint(1, 1, "LoadDynaVar  bSeqUsedGlobalVar\n");
							//dbgPrint(1, 1, "pDynaVar Name:%s  pDynaVar->szKey:%s   [%d] sName:%s sKey:%s\n",
							//    pDynaVar->sName,
							//    pDynaVar->szKey,
							//    index,
							//    sSysGlobalVar.aSVariableVarInfo[index].sName,
							//    sSysGlobalVar.aSVariableVarInfo[index].sKey);
							break;
						}
					}

					if (!bSourceMatch)
					{
						dbgPrint(1, 1, "sDataType:%s  eTmpDataType:%d\n", sDataType, eTmpDataType);
						errorInfo.ErrCode = 24703;
						errorInfo.eErrLever = Error;
					}
					else
					{
						pDynaVar->eType = DynaGlobal;
					}
				}
				else
				{
					errorInfo.ErrCode = 24702;
					errorInfo.eErrLever = Error;
				}
			}
			else if (strcmp(seType, "Local") == 0)
			{
				if (sLocalVar.uVarCount > 0)
				{
					//在Local 列表中查询是否存在
					memset(sDataType, 0, 64);
					strcpy(sDataType, jDynaVar["DataType"].GetString());
					EDataType eTmpDataType = DataTypeSwitch((char*)&sDataType);

					for (uint8 index = 0; index < sLocalVar.uVarCount; index++)
					{
						if (strcmp(pDynaVar->sName, sLocalVar.aSVariableVar[index].sName) == 0)
						{
							if (strcmp(pDynaVar->szKey, sLocalVar.aSVariableVar[index].sKey) != 0)
							{
								pDynaVar->eDataType = sLocalVar.aSVariableVar[index].eDataType;
								dbgPrint(1, 1, "pDynaVar->szKey:%s  sLocalVar.sKey:%s\n", pDynaVar->szKey, sLocalVar.aSVariableVar[index].sKey);
								strcpy(pDynaVar->szKey, sLocalVar.aSVariableVar[index].sKey);
							}
							bSourceMatch = true;
							break;
						}

						//dbgPrint(1, 1, "pDynaVar Name:%s  pDynaVar->szKey:%s   [%d] sName:%s sKey:%s\n",
						//    pDynaVar->sName,
						//    pDynaVar->szKey,
						//    index,
						//    sLocalVar.aSVariableVar[index].sName,
						//    sLocalVar.aSVariableVar[index].sKey);
					}

					if (!bSourceMatch)
					{
						errorInfo.ErrCode = 24705;
						errorInfo.eErrLever = Error;
					}
					else
					{
						pDynaVar->eType = DynaLocal;
					}
				}
				else
				{
					errorInfo.ErrCode = 24704;
					errorInfo.eErrLever = Error;
				}
			}
			else if (strcmp(seType, "Meas") == 0)
			{
				pDynaVar->eType = DynaMeas;
				//在Meas 列表中查询是否存在
			}
			else
			{
				errorInfo.ErrCode = 24002;
				errorInfo.eErrLever = Error;
			}
		}


		if (!errorInfo.ErrCode)
		{
			if (pDynaVar->eType == DynaLink)
			{
				const char* szSdoKey = jDynaVar.HasMember("Key") ? jDynaVar["Key"].GetString() : "";
				pTmpEntry = GetEntryByKey((char*)szSdoKey);
				if (pTmpEntry != NULL)
				{
					pDynaVar->eType = DynaLink;
					pDynaVar->eDataType = pTmpEntry->DataType;
					memcpy(&(pDynaVar->VarUnion), pTmpEntry->pVar, GetDataLength(pTmpEntry->DataType));
				}
				else
				{
					errorInfo.eErrLever = Error;
					errorInfo.ErrCode = 24003;
				}
			}
			else
			{
				if (jDynaVar["DataType"].IsString())
				{
					memset(sDataType, 0, 64);
					strcpy(sDataType, jDynaVar["DataType"].GetString());

					//  dbgPrint(1, 1, "LoadDynaVar Name:%s SourceType:%s sDataType:%s ", pDynaVar->sName, seType, sDataType);
					if (strcmp(sDataType, "Bool") == 0)
					{
						if (jDynaVar["Var"].IsBool())
						{
							pDynaVar->eDataType = DT_Bool;
							pDynaVar->VarUnion.bVar = jDynaVar["Var"].GetBool();
							// dbgPrint(1, 1, "var:%s\n", pDynaVar->VarUnion.bVar ? "true" : "false");
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24004;
						}
					}
					else if (strcmp(sDataType, "I8") == 0) {
						if (jDynaVar["Var"].IsNumber() || jDynaVar["Var"].IsBool())
						{
							pDynaVar->eDataType = DT_I8;
							if (jDynaVar["Var"].IsInt())
								pDynaVar->VarUnion.i8Var = (int8)jDynaVar["Var"].GetInt();
							else if (jDynaVar["Var"].IsInt64())
								pDynaVar->VarUnion.i8Var = (int8)jDynaVar["Var"].GetInt64();
							else if (jDynaVar["Var"].IsFloat())
								pDynaVar->VarUnion.i8Var = (int8)jDynaVar["Var"].GetFloat();
							else if (jDynaVar["Var"].IsDouble())
								pDynaVar->VarUnion.i8Var = (int8)jDynaVar["Var"].GetDouble();
							else if (jDynaVar["Var"].IsBool())
								pDynaVar->VarUnion.i8Var = (int8)jDynaVar["Var"].GetBool();
							else if (jDynaVar["Var"].IsUint())
								pDynaVar->VarUnion.i8Var = (int8)jDynaVar["Var"].GetUint();
							else
							{
								errorInfo.eErrLever = Error;
								errorInfo.ErrCode = 24005;
							}
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24005;
						}
					}
					else if (strcmp(sDataType, "U8") == 0) {
						if (jDynaVar["Var"].IsNumber() || jDynaVar["Var"].IsBool())
						{
							pDynaVar->eDataType = DT_U8;

							if (jDynaVar["Var"].IsUint())
								pDynaVar->VarUnion.u8Var = (uint8)jDynaVar["Var"].GetUint();
							else if (jDynaVar["Var"].IsInt())
								pDynaVar->VarUnion.u8Var = (uint8)jDynaVar["Var"].GetInt();
							else if (jDynaVar["Var"].IsInt64())
								pDynaVar->VarUnion.u8Var = (uint8)jDynaVar["Var"].GetInt64();
							else if (jDynaVar["Var"].IsFloat())
								pDynaVar->VarUnion.u8Var = (uint8)jDynaVar["Var"].GetFloat();
							else if (jDynaVar["Var"].IsDouble())
								pDynaVar->VarUnion.u8Var = (uint8)jDynaVar["Var"].GetDouble();
							else if (jDynaVar["Var"].IsBool())
								pDynaVar->VarUnion.u8Var = (uint8)jDynaVar["Var"].GetBool();
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24006;
						}
					}
					else if (strcmp(sDataType, "I16") == 0) {
						if (jDynaVar["Var"].IsNumber() || jDynaVar["Var"].IsBool())
						{
							pDynaVar->eDataType = DT_I16;
							if (jDynaVar["Var"].IsInt())
								pDynaVar->VarUnion.i16Var = (int16)jDynaVar["Var"].GetInt();
							else if (jDynaVar["Var"].IsUint())
								pDynaVar->VarUnion.i16Var = (int16)jDynaVar["Var"].GetUint();
							else if (jDynaVar["Var"].IsInt64())
								pDynaVar->VarUnion.i16Var = (int16)jDynaVar["Var"].GetInt64();
							else if (jDynaVar["Var"].IsFloat())
								pDynaVar->VarUnion.i16Var = (int16)jDynaVar["Var"].GetFloat();
							else if (jDynaVar["Var"].IsDouble())
								pDynaVar->VarUnion.i16Var = (int16)jDynaVar["Var"].GetDouble();
							else if (jDynaVar["Var"].IsBool())
								pDynaVar->VarUnion.i16Var = (int16)jDynaVar["Var"].GetBool();

							//dbgPrint(1, 1, "var:%d\n", pDynaVar->VarUnion.i16Var);
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24007;
						}
					}
					else if (strcmp(sDataType, "U16") == 0) {
						if (jDynaVar["Var"].IsNumber() || jDynaVar["Var"].IsBool())
						{
							pDynaVar->eDataType = DT_U16;
							if (jDynaVar["Var"].IsUint())
								pDynaVar->VarUnion.u16Var = (uint16)jDynaVar["Var"].GetUint();
							else if (jDynaVar["Var"].IsInt())
								pDynaVar->VarUnion.u16Var = (uint16)jDynaVar["Var"].GetInt();
							else if (jDynaVar["Var"].IsInt64())
								pDynaVar->VarUnion.u16Var = (uint16)jDynaVar["Var"].GetInt64();
							else if (jDynaVar["Var"].IsFloat())
								pDynaVar->VarUnion.u16Var = (uint16)jDynaVar["Var"].GetFloat();
							else if (jDynaVar["Var"].IsDouble())
								pDynaVar->VarUnion.u16Var = (uint16)jDynaVar["Var"].GetDouble();
							else if (jDynaVar["Var"].IsBool())
								pDynaVar->VarUnion.u16Var = (uint16)jDynaVar["Var"].GetBool();
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24008;
						}
					}
					else if (strcmp(sDataType, "I32") == 0) {
						if (jDynaVar["Var"].IsNumber() || jDynaVar["Var"].IsBool())
						{
							pDynaVar->eDataType = DT_I32;
							if (jDynaVar["Var"].IsUint())
								pDynaVar->VarUnion.i32Var = (int32)jDynaVar["Var"].GetUint();
							else if (jDynaVar["Var"].IsInt())
								pDynaVar->VarUnion.i32Var = (int32)jDynaVar["Var"].GetInt();
							else if (jDynaVar["Var"].IsInt64())
								pDynaVar->VarUnion.i32Var = (int32)jDynaVar["Var"].GetInt64();
							else if (jDynaVar["Var"].IsFloat())
								pDynaVar->VarUnion.i32Var = (int32)jDynaVar["Var"].GetFloat();
							else if (jDynaVar["Var"].IsDouble())
								pDynaVar->VarUnion.i32Var = (int32)jDynaVar["Var"].GetDouble();
							else if (jDynaVar["Var"].IsBool())
								pDynaVar->VarUnion.i32Var = (int32)jDynaVar["Var"].GetBool();
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24009;
						}
					}
					else if (strcmp(sDataType, "U32") == 0) {
						if (jDynaVar["Var"].IsNumber() || jDynaVar["Var"].IsBool())
						{
							pDynaVar->eDataType = DT_U32;
							if (jDynaVar["Var"].IsUint())
								pDynaVar->VarUnion.u32Var = (uint32)jDynaVar["Var"].GetUint();
							else if (jDynaVar["Var"].IsInt())
								pDynaVar->VarUnion.u32Var = (uint32)jDynaVar["Var"].GetInt();
							else if (jDynaVar["Var"].IsInt64())
								pDynaVar->VarUnion.u32Var = (uint32)jDynaVar["Var"].GetInt64();
							else if (jDynaVar["Var"].IsFloat())
								pDynaVar->VarUnion.u32Var = (uint32)jDynaVar["Var"].GetFloat();
							else if (jDynaVar["Var"].IsDouble())
								pDynaVar->VarUnion.u32Var = (uint32)jDynaVar["Var"].GetDouble();
							else if (jDynaVar["Var"].IsBool())
								pDynaVar->VarUnion.u32Var = (uint32)jDynaVar["Var"].GetBool();
							//dbgPrint(1, 1, "var:%d\n", pDynaVar->VarUnion.u32Var);
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24010;
						}
					}
					else if (strcmp(sDataType, "I64") == 0) {
						if (jDynaVar["Var"].IsNumber() || jDynaVar["Var"].IsBool())
						{
							pDynaVar->eDataType = DT_I64;

							if (jDynaVar["Var"].IsUint())
								pDynaVar->VarUnion.i64Var = (int64)jDynaVar["Var"].GetUint();
							else if (jDynaVar["Var"].IsInt())
								pDynaVar->VarUnion.i64Var = (int64)jDynaVar["Var"].GetInt();
							else if (jDynaVar["Var"].IsInt64())
								pDynaVar->VarUnion.i64Var = (int64)jDynaVar["Var"].GetInt64();
							else if (jDynaVar["Var"].IsFloat())
								pDynaVar->VarUnion.i64Var = (int64)jDynaVar["Var"].GetFloat();
							else if (jDynaVar["Var"].IsDouble())
								pDynaVar->VarUnion.i64Var = (int64)jDynaVar["Var"].GetDouble();
							else if (jDynaVar["Var"].IsBool())
								pDynaVar->VarUnion.i64Var = (int64)jDynaVar["Var"].GetBool();
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24011;
						}
					}
					else if (strcmp(sDataType, "U64") == 0) {
						if (jDynaVar["Var"].IsNumber() || jDynaVar["Var"].IsBool())
						{
							pDynaVar->eDataType = DT_U64;
							if (jDynaVar["Var"].IsInt64())
								pDynaVar->VarUnion.u64Var = (uint64)jDynaVar["Var"].GetInt64();
							else if (jDynaVar["Var"].IsFloat())
								pDynaVar->VarUnion.u64Var = (uint64)jDynaVar["Var"].GetFloat();
							else if (jDynaVar["Var"].IsDouble())
								pDynaVar->VarUnion.u64Var = (uint64)jDynaVar["Var"].GetDouble();
							else if (jDynaVar["Var"].IsBool())
								pDynaVar->VarUnion.u64Var = (uint64)jDynaVar["Var"].GetBool();
							else if (jDynaVar["Var"].IsInt())
								pDynaVar->VarUnion.u64Var = (uint64)jDynaVar["Var"].GetInt();
							else if (jDynaVar["Var"].IsUint())
								pDynaVar->VarUnion.u64Var = (uint64)jDynaVar["Var"].GetUint();
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24012;
						}
					}
					else if (strcmp(sDataType, "F32") == 0) {
						if (jDynaVar["Var"].IsNumber() || jDynaVar["Var"].IsBool())
						{
							pDynaVar->eDataType = DT_F32;

							if (jDynaVar["Var"].IsFloat())
								pDynaVar->VarUnion.fVar = (float32)jDynaVar["Var"].GetFloat();
							else if (jDynaVar["Var"].IsDouble())
								pDynaVar->VarUnion.fVar = (float32)jDynaVar["Var"].GetDouble();
							else if (jDynaVar["Var"].IsBool())
								pDynaVar->VarUnion.fVar = (float32)jDynaVar["Var"].GetBool();
							else if (jDynaVar["Var"].IsInt())
								pDynaVar->VarUnion.fVar = (float32)jDynaVar["Var"].GetInt();
							else if (jDynaVar["Var"].IsUint())
								pDynaVar->VarUnion.fVar = (float32)jDynaVar["Var"].GetUint();
							else if (jDynaVar["Var"].IsInt64())
								pDynaVar->VarUnion.fVar = (float32)jDynaVar["Var"].GetInt64();
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24013;
						}
					}
					else if (strcmp(sDataType, "F64") == 0)
					{
						if (jDynaVar["Var"].IsNumber() || jDynaVar["Var"].IsBool())
						{
							pDynaVar->eDataType = DT_F64;
							if (jDynaVar["Var"].IsDouble())
								pDynaVar->VarUnion.dVar = (float64)jDynaVar["Var"].GetDouble();
							else if (jDynaVar["Var"].IsBool())
								pDynaVar->VarUnion.dVar = (float64)jDynaVar["Var"].GetBool();
							else if (jDynaVar["Var"].IsFloat())
								pDynaVar->VarUnion.dVar = (float64)jDynaVar["Var"].GetFloat();
							else if (jDynaVar["Var"].IsInt())
								pDynaVar->VarUnion.dVar = (float64)jDynaVar["Var"].GetInt();
							else if (jDynaVar["Var"].IsUint())
								pDynaVar->VarUnion.dVar = (float64)jDynaVar["Var"].GetUint();
							else if (jDynaVar["Var"].IsInt64())
								pDynaVar->VarUnion.dVar = (float64)jDynaVar["Var"].GetInt64();
							//dbgPrint(1, 1, "var:%f\n", pDynaVar->VarUnion.dVar);
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24014;
						}
					}
					else if (strcmp(sDataType, "Str") == 0) {
						if (jDynaVar["Var"].IsString())
						{
							pDynaVar->eDataType = DT_Str;
							strcpy(pDynaVar->VarUnion.sVar, jDynaVar["Var"].GetString());
							//dbgPrint(1, 1, "var:%s\n", pDynaVar->VarUnion.sVar);
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 25000;
						}
					}
					else
					{
						errorInfo.eErrLever = Error;
						errorInfo.ErrCode = 24015;
					}
				}
				else
				{
					errorInfo.eErrLever = Error;
					errorInfo.ErrCode = 24016;
				}

				if (!errorInfo.ErrCode && pDynaVar->eDataType >= DT_Bool && pDynaVar->eDataType <= DT_F64)
				{
					LoadDynaVarToF64(pDynaVar, &pDynaVar->lfVarSwitched);
				}
			}
		}
	}
	else
	{
		errorInfo.ErrCode = 24017;
		errorInfo.eErrLever = Error;
	}

	ErrorInfoPack(&errorInfo, (char*)"LoadDynaVar", "");
	return  errorInfo;
}

/* LoadDynaVarToF64
* @param[in]     DynaVar* pDynaVar
* @param[out]    float32* lfVar
* @return        ErrorCode
*/
ErrorInfo LoadDynaVarToF64(DynaVar* pDynaVar, float64* lfVar)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	switch (pDynaVar->eDataType)
	{
	case DT_Bool:
		if (pDynaVar->VarUnion.bVar)
			*lfVar = 1.0;
		else
			*lfVar = 0.0;
		break;
	case DT_I8:
		*lfVar = (float64)pDynaVar->VarUnion.i8Var;
		break;
	case DT_U8:
		*lfVar = (float64)pDynaVar->VarUnion.u8Var;
		break;
	case DT_I16:
		*lfVar = (float64)pDynaVar->VarUnion.i16Var;
		break;
	case DT_U16:
		*lfVar = (float64)pDynaVar->VarUnion.u16Var;
		break;
	case DT_I32:
		*lfVar = (float64)pDynaVar->VarUnion.i32Var;
		break;
	case DT_U32:
		*lfVar = (float64)pDynaVar->VarUnion.u32Var;
		break;
	case DT_I64:
		*lfVar = (float64)pDynaVar->VarUnion.i64Var;
		break;
	case DT_U64:
		*lfVar = (float64)pDynaVar->VarUnion.u64Var;
		break;
	case DT_F32:
		*lfVar = (float64)pDynaVar->VarUnion.fVar;
		break;
	case DT_F64:
		*lfVar = (float64)pDynaVar->VarUnion.dVar;
		break;
	case DT_None:
	case DT_Str:
	case DT_WStr:
	case DT_ByteArray:
	case DT_RemapArray:
	case DT_DynaValue:
	default:
		break;
	}

	ErrorInfoPack(&errorInfo, "DynaVarToF32", "");
	return  errorInfo;
}

/* findAxisMc
* @param[in]     const char*  axisName
* @param[in]     sAxisMcPara* pAxisMc
* @param[in]     int AxisMcNum
* @return        sAxisMcPara
*/
sAxisMcPara* findAxisMc(const char* axisName, sAxisMcPara* pAxisMc, int AxisMcNum) {
	if (AxisMcNum == 1) {
		return pAxisMc;
	}
	else if (AxisMcNum > 1) {
		int i;
		for (i = 0; i < AxisMcNum; i++) {
			if (strcasecmp(axisName, pAxisMc[i].sMcSetPara.SensorName) == 0) {
				return  pAxisMc + i;
			}
		}
		return  0;
	}
	return  0;
}

/* SwitchOpType     运算符操作类型从字符转换成对应枚举
* @param[in]     char *sOpStr
* @param[out]     EValueOpType *peOpType
* @return        ErrorInfo
*/
ErrorInfo SwitchOpType(char* sOpStr, EValueOpType* peOpType)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	if (strcmp(sOpStr, "+") == 0)           // +
		*peOpType = ADD;
	else if (strcmp(sOpStr, "-") == 0)      // -
		*peOpType = SUB;
	else if (strcmp(sOpStr, "*") == 0)      // *
		*peOpType = MUL;
	else if (strcmp(sOpStr, "/") == 0)      // /
		*peOpType = DIV;
	else if (strcmp(sOpStr, "=") == 0)      // =
		*peOpType = Equal;
	else if (strcmp(sOpStr, "!=") == 0)     // !=
		*peOpType = Unequal;
	else if (strcmp(sOpStr, ">") == 0)      // >
		*peOpType = Greater;
	else if (strcmp(sOpStr, ">=") == 0)     // >=
		*peOpType = Equal_Greater;
	else if (strcmp(sOpStr, "<") == 0)      // < 
		*peOpType = Less;
	else if (strcmp(sOpStr, "<=") == 0)     // <=
		*peOpType = Less_Equal;
	else
	{
		errorInfo.ErrCode = 25015;
		errorInfo.eErrLever = Error;
	}

	ErrorInfoPack(&errorInfo, "SwitchOpType", "TypeStr:%s", sOpStr);
	return  errorInfo;
}

/* SetConstX     常量X的值解析时要依据被赋值的Y来确定类型及加载数据
* @param[in]     EDataType  eVarYType
* @param[in]     Value&     jXData
* @param[in]     DynaVar    *pDynaX
* @param[in]     const char* psParaName
* @return        ErrorInfo
*/
ErrorInfo SetConstX(EDataType eVarYType, Value& jXData, DynaVar* pDynaX, const char* psParaName)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	//dbgPrint(1, 1, "SetConstX Start\n");
	switch (eVarYType)
	{
	case DT_Bool:
		pDynaX->eDataType = DT_Bool;
		if (jXData[psParaName].IsInt())
			pDynaX->VarUnion.bVar = (bool)jXData[psParaName].GetInt();
		else if (jXData[psParaName].IsUint())
			pDynaX->VarUnion.bVar = (bool)jXData[psParaName].GetUint();
		else if (jXData[psParaName].IsFloat())
			pDynaX->VarUnion.bVar = (bool)jXData[psParaName].GetFloat();
		else if (jXData[psParaName].IsDouble())
			pDynaX->VarUnion.bVar = (bool)jXData[psParaName].GetDouble();
		else if (jXData[psParaName].IsInt64())
			pDynaX->VarUnion.bVar = (bool)jXData[psParaName].GetInt64();
		else if (jXData[psParaName].IsUint64())
			pDynaX->VarUnion.bVar = (bool)jXData[psParaName].GetUint64();
		else
		{
			errorInfo.ErrCode = 25002;
			errorInfo.eErrLever = Error;
		}
		if (!errorInfo.ErrCode)
		{
			pDynaX->fVarSwitched = (float32)pDynaX->VarUnion.bVar;
			pDynaX->lfVarSwitched = (float64)pDynaX->VarUnion.bVar;
		}

		break;
	case DT_I8:
		if (jXData[psParaName].IsNumber() || jXData[psParaName].IsBool())
		{
			pDynaX->eDataType = DT_I8;
			if (jXData[psParaName].IsInt())
				pDynaX->VarUnion.i8Var = (int8)jXData[psParaName].GetInt();
			else if (jXData[psParaName].IsUint())
				pDynaX->VarUnion.i8Var = (int8)jXData[psParaName].GetUint();
			else if (jXData[psParaName].IsFloat())
				pDynaX->VarUnion.i8Var = (int8)jXData[psParaName].GetFloat();
			else if (jXData[psParaName].IsDouble())
				pDynaX->VarUnion.i8Var = (int8)jXData[psParaName].GetDouble();
			else if (jXData[psParaName].IsInt64())
				pDynaX->VarUnion.i8Var = (int8)jXData[psParaName].GetInt64();
			else if (jXData[psParaName].IsUint64())
				pDynaX->VarUnion.i8Var = (int8)jXData[psParaName].GetUint64();

			pDynaX->fVarSwitched = (float32)pDynaX->VarUnion.i8Var;
			pDynaX->lfVarSwitched = (float64)pDynaX->VarUnion.i8Var;
			//dbgPrint(1, 1, "Name:%s  sDataType:%s  Var:%d\n",
			//    psParaName,
			//    "DT_I8",
			//    pDynaX->VarUnion.i8Var);
		}
		else
		{
			errorInfo.ErrCode = 25003;
			errorInfo.eErrLever = Error;
		}
		break;
	case DT_U8:
		if (jXData[psParaName].IsNumber() || jXData[psParaName].IsBool())
		{
			pDynaX->eDataType = DT_U8;
			if (jXData[psParaName].IsInt())
				pDynaX->VarUnion.u8Var = (uint8)jXData[psParaName].GetInt();
			else if (jXData[psParaName].IsUint())
				pDynaX->VarUnion.u8Var = (uint8)jXData[psParaName].GetUint();
			else if (jXData[psParaName].IsFloat())
				pDynaX->VarUnion.u8Var = (uint8)jXData[psParaName].GetFloat();
			else if (jXData[psParaName].IsDouble())
				pDynaX->VarUnion.u8Var = (uint8)jXData[psParaName].GetDouble();
			else if (jXData[psParaName].IsInt64())
				pDynaX->VarUnion.u8Var = (uint8)jXData[psParaName].GetInt64();
			else if (jXData[psParaName].IsUint64())
				pDynaX->VarUnion.u8Var = (uint8)jXData[psParaName].GetUint64();

			pDynaX->fVarSwitched = (float32)pDynaX->VarUnion.u8Var;
			pDynaX->lfVarSwitched = (float64)pDynaX->VarUnion.u8Var;

			//dbgPrint(1, 1, "Name:%s  sDataType:%s  Var:%d\n",
			//    psParaName,
			//    "DT_U8",
			//    pDynaX->VarUnion.u8Var);
		}
		else
		{
			errorInfo.ErrCode = 25004;
			errorInfo.eErrLever = Error;
		}

		break;
	case DT_I16:
		if (jXData[psParaName].IsNumber() || jXData[psParaName].IsBool())
		{
			pDynaX->eDataType = DT_I16;
			if (jXData[psParaName].IsInt())
				pDynaX->VarUnion.i16Var = (int16)jXData[psParaName].GetInt();
			else if (jXData[psParaName].IsUint())
				pDynaX->VarUnion.i16Var = (int16)jXData[psParaName].GetUint();
			else if (jXData[psParaName].IsFloat())
				pDynaX->VarUnion.i16Var = (int16)jXData[psParaName].GetFloat();
			else if (jXData[psParaName].IsDouble())
				pDynaX->VarUnion.i16Var = (int16)jXData[psParaName].GetDouble();
			else if (jXData[psParaName].IsInt64())
				pDynaX->VarUnion.i16Var = (int16)jXData[psParaName].GetInt64();
			else if (jXData[psParaName].IsUint64())
				pDynaX->VarUnion.i16Var = (int16)jXData[psParaName].GetUint64();

			pDynaX->fVarSwitched = (float32)pDynaX->VarUnion.i16Var;
			pDynaX->lfVarSwitched = (float64)pDynaX->VarUnion.i16Var;
			//dbgPrint(1, 1, "Name:%s  sDataType:%s  Var:%d\n",
			//    psParaName,
			//    "DT_I16",
			//    pDynaX->VarUnion.i16Var);
		}
		else {
			errorInfo.ErrCode = 25005;
			errorInfo.eErrLever = Error;
		}

		break;
	case DT_U16:
		if (jXData[psParaName].IsNumber() || jXData[psParaName].IsBool())
		{
			pDynaX->eDataType = DT_U16;

			if (jXData[psParaName].IsInt())
				pDynaX->VarUnion.u16Var = (uint16)jXData[psParaName].GetInt();
			else if (jXData[psParaName].IsUint())
				pDynaX->VarUnion.u16Var = (uint16)jXData[psParaName].GetUint();
			else if (jXData[psParaName].IsFloat())
				pDynaX->VarUnion.u16Var = (uint16)jXData[psParaName].GetFloat();
			else if (jXData[psParaName].IsDouble())
				pDynaX->VarUnion.u16Var = (uint16)jXData[psParaName].GetDouble();
			else if (jXData[psParaName].IsInt64())
				pDynaX->VarUnion.u16Var = (uint16)jXData[psParaName].GetInt64();
			else if (jXData[psParaName].IsUint64())
				pDynaX->VarUnion.u16Var = (uint16)jXData[psParaName].GetUint64();

			pDynaX->fVarSwitched = (float32)pDynaX->VarUnion.u16Var;
			pDynaX->lfVarSwitched = (float64)pDynaX->VarUnion.u16Var;
		}
		else {
			errorInfo.ErrCode = 25006;
			errorInfo.eErrLever = Error;
		}
		break;
	case DT_I32:
		if (jXData[psParaName].IsNumber() || jXData[psParaName].IsBool())
		{
			pDynaX->eDataType = DT_I32;
			if (jXData[psParaName].IsInt())
				pDynaX->VarUnion.i32Var = (int32)jXData[psParaName].GetInt();
			else if (jXData[psParaName].IsUint())
				pDynaX->VarUnion.i32Var = (int32)jXData[psParaName].GetUint();
			else if (jXData[psParaName].IsFloat())
				pDynaX->VarUnion.i32Var = (int32)jXData[psParaName].GetFloat();
			else if (jXData[psParaName].IsDouble())
				pDynaX->VarUnion.i32Var = (int32)jXData[psParaName].GetDouble();
			else if (jXData[psParaName].IsInt64())
				pDynaX->VarUnion.i32Var = (int32)jXData[psParaName].GetInt64();
			else if (jXData[psParaName].IsUint64())
				pDynaX->VarUnion.i32Var = (int32)jXData[psParaName].GetUint64();

			pDynaX->fVarSwitched = (float32)pDynaX->VarUnion.i32Var;
			pDynaX->lfVarSwitched = (float64)pDynaX->VarUnion.i32Var;
			//dbgPrint(1, 1, "Name:%s  sDataType:%s  Var:%d\n",
			//    psParaName,
			//    "DT_I32",
			//    pDynaX->VarUnion.i32Var);
		}
		else {
			errorInfo.ErrCode = 25007;
			errorInfo.eErrLever = Error;
		}
		break;
	case DT_U32:
		if (jXData[psParaName].IsNumber() || jXData[psParaName].IsBool())
		{
			pDynaX->eDataType = DT_U32;

			if (jXData[psParaName].IsInt())
				pDynaX->VarUnion.u32Var = (uint32)jXData[psParaName].GetInt();
			else if (jXData[psParaName].IsUint())
				pDynaX->VarUnion.u32Var = (uint32)jXData[psParaName].GetUint();
			else if (jXData[psParaName].IsFloat())
				pDynaX->VarUnion.u32Var = (uint32)jXData[psParaName].GetFloat();
			else if (jXData[psParaName].IsDouble())
				pDynaX->VarUnion.u32Var = (uint32)jXData[psParaName].GetDouble();
			else if (jXData[psParaName].IsInt64())
				pDynaX->VarUnion.u32Var = (uint32)jXData[psParaName].GetInt64();
			else if (jXData[psParaName].IsUint64())
				pDynaX->VarUnion.u32Var = (uint32)jXData[psParaName].GetUint64();

			pDynaX->fVarSwitched = (float32)pDynaX->VarUnion.u32Var;
			pDynaX->lfVarSwitched = (float64)pDynaX->VarUnion.u32Var;
			//dbgPrint(1, 1, "Name:%s  sDataType:%s  Var:%d\n",
			//    psParaName,
			//    "DT_U32",
			//    pDynaX->VarUnion.u32Var);
		}
		else {
			errorInfo.ErrCode = 25008;
			errorInfo.eErrLever = Error;
		}
		break;
	case DT_I64:
		if (jXData[psParaName].IsNumber() || jXData[psParaName].IsBool())
		{
			pDynaX->eDataType = DT_I64;
			if (jXData[psParaName].IsInt())
				pDynaX->VarUnion.i64Var = (int64)jXData[psParaName].GetInt();
			else if (jXData[psParaName].IsUint())
				pDynaX->VarUnion.i64Var = (int64)jXData[psParaName].GetUint();
			else if (jXData[psParaName].IsFloat())
				pDynaX->VarUnion.i64Var = (int64)jXData[psParaName].GetFloat();
			else if (jXData[psParaName].IsDouble())
				pDynaX->VarUnion.i64Var = (int64)jXData[psParaName].GetDouble();
			else if (jXData[psParaName].IsInt64())
				pDynaX->VarUnion.i64Var = (int64)jXData[psParaName].GetInt64();
			else if (jXData[psParaName].IsUint64())
				pDynaX->VarUnion.i64Var = (int64)jXData[psParaName].GetUint64();


			pDynaX->fVarSwitched = (float32)pDynaX->VarUnion.i64Var;
			pDynaX->lfVarSwitched = (float64)pDynaX->VarUnion.i64Var;
			//dbgPrint(1, 1, "Name:%s  sDataType:%s  Var:%ld\n",
			//    psParaName,
			//    "DT_I64",
			//    pDynaX->VarUnion.i64Var);
		}
		else {
			errorInfo.ErrCode = 25009;
			errorInfo.eErrLever = Error;
		}
		break;
	case DT_U64:
		if (jXData[psParaName].IsNumber() || jXData[psParaName].IsBool())
		{
			pDynaX->eDataType = DT_U64;
			if (jXData[psParaName].IsInt())
				pDynaX->VarUnion.u64Var = (uint64)jXData[psParaName].GetInt();
			else if (jXData[psParaName].IsUint())
				pDynaX->VarUnion.u64Var = (uint64)jXData[psParaName].GetUint();
			else if (jXData[psParaName].IsFloat())
				pDynaX->VarUnion.u64Var = (uint64)jXData[psParaName].GetFloat();
			else if (jXData[psParaName].IsDouble())
				pDynaX->VarUnion.u64Var = (uint64)jXData[psParaName].GetDouble();
			else if (jXData[psParaName].IsInt64())
				pDynaX->VarUnion.u64Var = (uint64)jXData[psParaName].GetInt64();
			else if (jXData[psParaName].IsUint64())
				pDynaX->VarUnion.u64Var = (uint64)jXData[psParaName].GetUint64();

			pDynaX->fVarSwitched = (float32)pDynaX->VarUnion.u64Var;
			pDynaX->lfVarSwitched = (float64)pDynaX->VarUnion.u64Var;
			//dbgPrint(1, 1, "Name:%s  sDataType:%s  Var:%ld\n",
			//    psParaName,
			//    "DT_U64",
			//    pDynaX->VarUnion.u64Var);
		}
		else {
			errorInfo.ErrCode = 25010;
			errorInfo.eErrLever = Error;
		}
		break;
	case DT_F32:
		if (jXData[psParaName].IsNumber() || jXData[psParaName].IsBool()) //IsFloat())
		{
			pDynaX->eDataType = DT_F32;
			if (jXData[psParaName].IsInt())
				pDynaX->VarUnion.fVar = (float32)jXData[psParaName].GetInt();
			else if (jXData[psParaName].IsUint())
				pDynaX->VarUnion.fVar = (float32)jXData[psParaName].GetUint();
			else if (jXData[psParaName].IsFloat())
				pDynaX->VarUnion.fVar = (float32)jXData[psParaName].GetFloat();
			else if (jXData[psParaName].IsDouble())
				pDynaX->VarUnion.fVar = (float32)jXData[psParaName].GetDouble();
			else if (jXData[psParaName].IsInt64())
				pDynaX->VarUnion.fVar = (float32)jXData[psParaName].GetInt64();
			else if (jXData[psParaName].IsUint64())
				pDynaX->VarUnion.fVar = (float32)jXData[psParaName].GetUint64();

			pDynaX->fVarSwitched = (float32)pDynaX->VarUnion.fVar;
			pDynaX->lfVarSwitched = (float64)pDynaX->VarUnion.fVar;
			//dbgPrint(1, 1, "Name:%s  sDataType:%s  Var:%f\n",
			//    psParaName,
			//    "DT_F32",
			//    pDynaX->VarUnion.fVar);
		}
		else {
			errorInfo.ErrCode = 25011;
			errorInfo.eErrLever = Error;
		}
		break;
	case DT_F64:
		if (jXData[psParaName].IsNumber() || jXData[psParaName].IsBool())
		{
			pDynaX->eDataType = DT_F64;
			pDynaX->VarUnion.dVar = jXData[psParaName].GetDouble();

			if (jXData[psParaName].IsInt())
				pDynaX->VarUnion.dVar = (float64)jXData[psParaName].GetInt();
			else if (jXData[psParaName].IsUint())
				pDynaX->VarUnion.dVar = (float64)jXData[psParaName].GetUint();
			else if (jXData[psParaName].IsFloat())
				pDynaX->VarUnion.dVar = (float64)jXData[psParaName].GetFloat();
			else if (jXData[psParaName].IsDouble())
				pDynaX->VarUnion.dVar = (float64)jXData[psParaName].GetDouble();
			else if (jXData[psParaName].IsInt64())
				pDynaX->VarUnion.dVar = (float64)jXData[psParaName].GetInt64();
			else if (jXData[psParaName].IsUint64())
				pDynaX->VarUnion.dVar = (float64)jXData[psParaName].GetUint64();

			pDynaX->lfVarSwitched = pDynaX->VarUnion.dVar;
			pDynaX->fVarSwitched = (float32)pDynaX->VarUnion.dVar;
			//dbgPrint(1, 1, "Name:%s  sDataType:%s  Var:%f\n",
			//    psParaName,
			//    "DT_F64",
			//    pDynaX->VarUnion.dVar);
		}
		else {
			errorInfo.ErrCode = 25012;
			errorInfo.eErrLever = Error;
		}
		break;
	case  DT_Str:
	case  DT_WStr:
		if (jXData[psParaName].IsString())
		{
			pDynaX->eDataType = DT_Str;
			memcpy(pDynaX->VarUnion.sVar, jXData[psParaName].GetString(), strlen(jXData[psParaName].GetString()));
			//dbgPrint(1, 1, "Name:%s  sDataType:%s  Var:%s\n",
			//    psParaName,
			//    "DT_Str/DT_WStr",
			//    pDynaX->VarUnion.dVar);
		}
		else
		{
			errorInfo.ErrCode = 25013;
			errorInfo.eErrLever = Error;
		}
		break;
	case DT_ByteArray:
	case DT_RemapArray:
	case DT_DynaValue:
	case DT_None:
	default:
		errorInfo.ErrCode = 25001;
		errorInfo.eErrLever = Error;
		break;
	}

	ErrorInfoPack(&errorInfo, "SetConstX", "");
	return  errorInfo;
}

void SeqItem_Free(SeqItem* pSeqItem)
{
	if (pSeqItem != NULL)
	{
		if (pSeqItem->pCfg != NULL)
		{
			//dbgPrint(1, 3, "Free	   pSeqItemp:%u pCfg:%u\n", pSeqItem, pSeqItem->pCfg);
			free(pSeqItem->pCfg);
			pSeqItem->pCfg = NULL;
		}

		if (pSeqItem->pExeActor != NULL)
		{
			if (pSeqItem->pExeActor->nErrCodeJumpTargetNum > 0)
			{
				for (int i = 0; i < pSeqItem->pExeActor->nErrCodeJumpTargetNum; i++)
				{
					if (pSeqItem->pExeActor->vErrCodeJumpTarget[i])
					{
						//free(pSeqItem->pExeActor->vErrCodeJumpTarget[i]);
						pSeqItem->pExeActor->vErrCodeJumpTarget[i] = 0;
					}
					pSeqItem->pExeActor->nErrCodeJumpTargetNum = 0;
				}
			}
			//dbgPrint(1, 3, "Free	   pSeqItemp:%u ExeActor:%u\n",pSeqItem, pSeqItem->pExeActor);
			free(pSeqItem->pExeActor);
			pSeqItem->pExeActor = NULL;
		}
		if (pSeqItem->pFlowActor != NULL)
		{
			free(pSeqItem->pFlowActor);
			pSeqItem->pFlowActor = NULL;
			//dbgPrint(1, 3, "Free	   pSeqItemp:%u ExeActor:%u\n", pSeqItem, pSeqItem->pFlowActor);
		}

		free(pSeqItem);
		pSeqItem = NULL;
	}
}


void Seq_Free(SeqRef* pSeqRef)
{
	if (pSeqRef)
	{
		// 释放 vpNode 数组中的每个元素
		for (uint8 i = 0; i < MaxBranchNum; i++)
		{
			if (pSeqRef->vpNode[i] != NULL)
			{
				free(pSeqRef->vpNode[i]);
				//dbgPrint(1, 1, "Free Count:%d  vpNode:%u\n", i, pSeqRef->vpNode[i]);
				pSeqRef->vpNode[i] = NULL;  // 避免悬空指针
			}
		}

		// 释放 vpBranch 数组中的每个元素
		for (uint8 i = 0; i < MaxBranchNum; i++)
		{
			if (pSeqRef->vpBranch[i])
			{
				// 释放当前分支的 SeqItem 链表
				SeqItem* pSeqItem = pSeqRef->vpBranch[i]->pFirstItem;
				while (pSeqItem != NULL)
				{
					SeqItem* pNextItem = pSeqItem->pNext;
					//dbgPrint(1, 2, "Free Count:%d  pSeqItem:%u\n", pSeqItem->iRowIndex, pSeqItem);
					SeqItem_Free(pSeqItem);  // 释放单个 SeqItem
					pSeqItem = pNextItem;
				}

				// 释放当前分支的内存
				free(pSeqRef->vpBranch[i]);
				//dbgPrint(1, 1, "Free Count:%d  vpBranch:%u\n", i, pSeqRef->vpBranch[i]);
				pSeqRef->vpBranch[i] = NULL;  // 避免悬空指针
			}
		}
	}
}

void SequenceItemRelease(SeqItem* pSeqItem)
{
	if (pSeqItem == 0)
		return;
	if (pSeqItem->pChild)
	{
		SequenceItemRelease(pSeqItem->pChild);
	}
	else if (pSeqItem->pNext) {
		SequenceItemRelease(pSeqItem->pNext);
	}
	SeqItem_Free(pSeqItem);
}

ErrorInfo SeqRelease(SeqRef* pSeqRef)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	for (int i = 0; i < pSeqRef->nBranchNum; i++) {
		sSeqRef_Branch* pBranch = pSeqRef->vpBranch[i];
		SequenceItemRelease(pBranch->pFirstItem);
		pBranch->nRowCount = -1;
		pBranch->pFirstItem = NULL;
		pBranch->pCurrItem = NULL;
		pBranch->pLastItem = NULL;
		pBranch->iActiveRowIndex = -1;
		free(pBranch);
		pBranch = NULL;
	}
	for (int i = 0; i < pSeqRef->nNodeNum; i++) {
		sSeqRef_Node* pNode = pSeqRef->vpNode[i];
		pNode->nInBranchNum = 0;
		pNode->nOutBranchNum = 0;
		free(pNode);
		pNode = NULL;
	}
	pSeqRef->nBranchNum = 0;
	pSeqRef->nNodeNum = -1;
	pSeqRef->nDefaultErrCodeJumpTargetNum = 0;
	ErrorInfoPack(&errorInfo, "SeqRelease", "");

	return errorInfo;
}

/* SeqItem_MeasureStar_Load              //从Json文本中load Motion参数
* @param[in]     pSeqItem
* @param[in]     jMotion
* @return        ErrorInfo
*/
const char* sMeasureName[7] = { "Auto","ManualOp","PLC_IO" ,"xTrigger","yTrigger","TimeMode","CurveReturn" };
const char* sDirection[5] = { "None ","Left","Right","Up","Down" };
ErrorInfo SeqItem_MeasureStar_Load(SeqItem* pSeqItem, Value& jMeasureValue)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	SeqItem_MeasurementStart* pCfg = (SeqItem_MeasurementStart*)(pSeqItem->pCfg);
	str64 sMeasureType;

	bool bPrintfResolve = false;

	if (jMeasureValue.IsObject())
	{
		if (jMeasureValue.HasMember("MeasureType") && jMeasureValue["MeasureType"].IsString())
		{
			memset(sMeasureType, 0, sizeof(str64));
			strcpy(sMeasureType, jMeasureValue["MeasureType"].GetString());
			if (strcmp(sMeasureType, "ManualOp") == 0)
			{
				pCfg->eMeasureMode = ManualOp;
			}
			else  if (strcmp(sMeasureType, "PLC/IO") == 0)
			{
				pCfg->eMeasureMode = PLC_IO;
			}
			else  if (strcmp(sMeasureType, "xTrigger") == 0)
			{
				pCfg->eMeasureMode = xTrigger;
				if (jMeasureValue.HasMember("TriggerVar") &&
					jMeasureValue.HasMember("Direction") && jMeasureValue["Direction"].IsString())
				{
					errorInfo = LoadDynaVar(&(pCfg->uMeasurePara.sxTriggerPara.dTriggerVar), jMeasureValue["TriggerVar"]);
					if (errorInfo.ErrCode == 0)
					{
						str64 sDirectionType;
						memset(sDirectionType, 0, sizeof(str64));
						strcpy(sDirectionType, jMeasureValue["Direction"].GetString());
						if ((strcmp(sDirectionType, "Left") == 0) || (strcmp(sDirectionType, "left") == 0))
						{
							pCfg->uMeasurePara.sxTriggerPara.eDirection = LeftToRight;
						}
						else if ((strcmp(sDirectionType, "Right") == 0) || (strcmp(sDirectionType, "right") == 0))
						{
							pCfg->uMeasurePara.sxTriggerPara.eDirection = RightToLeft;
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24321;
						}
						if (bPrintfResolve)
							dbgPrint(1, 0, "MeassureStart xTrigger  fTriggerVar:%f sDirection:%s\n",
								pCfg->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched,
								sDirection[pCfg->uMeasurePara.syTriggerPara.eDirection]);
					}
				}
				else
				{
					errorInfo.eErrLever = Error;
					errorInfo.ErrCode = 24326;
				}
			}
			else  if (strcmp(sMeasureType, "yTrigger") == 0)
			{
				pCfg->eMeasureMode = yTrigger;
				if (jMeasureValue.HasMember("TriggerVar") &&
					jMeasureValue.HasMember("Direction") && jMeasureValue["Direction"].IsString())
				{
					errorInfo = LoadDynaVar(&(pCfg->uMeasurePara.syTriggerPara.dTriggerVar), jMeasureValue["TriggerVar"]);
					if (!errorInfo.ErrCode)
					{
						str64 sDirectionType;
						memset(sDirectionType, 0, sizeof(str64));
						strcpy(sDirectionType, jMeasureValue["Direction"].GetString());
						if ((strcmp(sDirectionType, "Above") == 0) || (strcmp(sDirectionType, "above") == 0)
							|| (strcmp(sDirectionType, "Up") == 0) || (strcmp(sDirectionType, "up") == 0))
						{
							pCfg->uMeasurePara.syTriggerPara.eDirection = UpToDown;
						}
						else if ((strcmp(sDirectionType, "Blow") == 0) || (strcmp(sDirectionType, "blow") == 0) ||
							(strcmp(sDirectionType, "down") == 0) || (strcmp(sDirectionType, "Down") == 0))
						{
							pCfg->uMeasurePara.syTriggerPara.eDirection = DownToUp;
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24322;
						}

						if (bPrintfResolve)
							dbgPrint(1, 0, "MeassureStart yTrigger  fTriggerVar:%f sDirection:%s \n",
								pCfg->uMeasurePara.syTriggerPara.dTriggerVar.VarUnion.fVar,
								sDirection[pCfg->uMeasurePara.syTriggerPara.eDirection]);
					}
				}
				else
				{
					errorInfo.eErrLever = Error;
					errorInfo.ErrCode = 24327;
				}
			}
			else
			{
				errorInfo.eErrLever = Error;
				errorInfo.ErrCode = 24323;
			}
		}
		else
		{
			errorInfo.eErrLever = Error;
			errorInfo.ErrCode = 24320;
		}
	}
	else
	{
		errorInfo.eErrLever = Error;
		errorInfo.ErrCode = 24328;
	}

	if (bPrintfResolve)
		dbgPrint(1, 0, "MeassureStart eMeasureMode:%s\n", sMeasureName[pCfg->eMeasureMode]);

	ErrorInfoPack(&errorInfo, "SeqItem MeasureStar Load", "");
	return  errorInfo;
}

/* SeqItem_MeasureStop_Load              //从Json文本中load Motion参数
* @param[in]     pSeqItem
* @param[in]     jMotion
* @return        ErrorInfo
*/
ErrorInfo SeqItem_MeasureStop_Load(SeqItem* pSeqItem, Value& jMeasureValue)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	SeqItem_MeasurementStop* pCfg = (SeqItem_MeasurementStop*)(pSeqItem->pCfg);
	str64 sMeasureType;

	bool bPrintfResolve = false;

	if (jMeasureValue.IsObject())
	{
		if (jMeasureValue.HasMember("MeasureType") && jMeasureValue["MeasureType"].IsString())
		{
			memset(sMeasureType, 0, sizeof(str64));
			strcpy(sMeasureType, jMeasureValue["MeasureType"].GetString());
			if (strcmp(sMeasureType, "ManualOp") == 0)
			{
				pCfg->eMeasureMode = ManualOp;
			}
			else  if (strcmp(sMeasureType, "PLC/IO") == 0)
			{
				pCfg->eMeasureMode = PLC_IO;
			}
			else  if (strcmp(sMeasureType, "xTrigger") == 0)
			{
				pCfg->eMeasureMode = xTrigger;
				if (jMeasureValue.HasMember("TriggerVar") &&
					jMeasureValue.HasMember("TimeOut") &&
					jMeasureValue.HasMember("Direction") && jMeasureValue["Direction"].IsString())
				{
					errorInfo = LoadDynaVar(&(pCfg->uMeasurePara.sxTriggerPara.dTriggerVar), jMeasureValue["TriggerVar"]);

					if (errorInfo.ErrCode == 0)
					{
						errorInfo = LoadDynaVar(&(pCfg->uMeasurePara.sxTriggerPara.dTimeOut), jMeasureValue["TimeOut"]);
					}

					if (errorInfo.ErrCode == 0)
					{
						str64 sDirectionType;
						memset(sDirectionType, 0, sizeof(str64));
						strcpy(sDirectionType, jMeasureValue["Direction"].GetString());
						if (strcmp(sDirectionType, "left") == 0 || strcmp(sDirectionType, "Left") == 0)
						{
							pCfg->uMeasurePara.sxTriggerPara.eDirection = LeftToRight;
						}
						else if (strcmp(sDirectionType, "right") == 0 || strcmp(sDirectionType, "Right") == 0)
						{
							pCfg->uMeasurePara.sxTriggerPara.eDirection = RightToLeft;
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24330;
						}

						if (bPrintfResolve)
							dbgPrint(1, 0, "MeassureStop xTrigger  fTriggerVar:%f sDirection:%s fTimeOut:%f\n",
								pCfg->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched,
								sDirection[pCfg->uMeasurePara.syTriggerPara.eDirection],
								pCfg->uMeasurePara.syTriggerPara.dTimeOut.fVarSwitched);
					}
				}
				else
				{
					errorInfo.eErrLever = Error;
					errorInfo.ErrCode = 24331;
				}

			}
			else  if (strcmp(sMeasureType, "yTrigger") == 0)
			{
				pCfg->eMeasureMode = yTrigger;
				if (jMeasureValue.HasMember("TriggerVar") &&
					jMeasureValue.HasMember("TimeOut") &&
					jMeasureValue.HasMember("Direction") && jMeasureValue["Direction"].IsString())
				{
					if (errorInfo.ErrCode == 0)
					{
						errorInfo = LoadDynaVar(&(pCfg->uMeasurePara.syTriggerPara.dTriggerVar), jMeasureValue["TriggerVar"]);
					}

					if (errorInfo.ErrCode == 0)
					{
						errorInfo = LoadDynaVar(&(pCfg->uMeasurePara.syTriggerPara.dTimeOut), jMeasureValue["TimeOut"]);
					}

					if (errorInfo.ErrCode == 0)
					{
						str64 sDirectionType;
						memset(sDirectionType, 0, sizeof(str64));
						strcpy(sDirectionType, jMeasureValue["Direction"].GetString());
						if ((strcmp(sDirectionType, "Above") == 0) || (strcmp(sDirectionType, "above") == 0)
							|| (strcmp(sDirectionType, "Up") == 0) || (strcmp(sDirectionType, "up") == 0))
						{
							pCfg->uMeasurePara.syTriggerPara.eDirection = UpToDown;
						}
						else if ((strcmp(sDirectionType, "Below") == 0) || (strcmp(sDirectionType, "below") == 0) ||
							(strcmp(sDirectionType, "Blow") == 0) || (strcmp(sDirectionType, "blow") == 0) ||
							(strcmp(sDirectionType, "down") == 0) || (strcmp(sDirectionType, "Down") == 0))
						{
							pCfg->uMeasurePara.syTriggerPara.eDirection = DownToUp;
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24332;
						}

						if (bPrintfResolve)
							dbgPrint(1, 0, "MeassureStop yTrigger  fTriggerVar:%f sDirection:%s fTimeOut:%f\n",
								pCfg->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched,
								sDirection[pCfg->uMeasurePara.syTriggerPara.eDirection],
								pCfg->uMeasurePara.syTriggerPara.dTimeOut.fVarSwitched);
					}
				}
				else
				{
					errorInfo.eErrLever = Error;
					errorInfo.ErrCode = 24333;
				}
			}
			else  if ((strcmp(sMeasureType, "Time") == 0) || (strcmp(sMeasureType, "time") == 0))//time
			{
				pCfg->eMeasureMode = TimeMode;
				if (jMeasureValue.HasMember("TimeOut"))
				{
					errorInfo = LoadDynaVar(&(pCfg->uMeasurePara.sfTimePara.dTime), jMeasureValue["TimeOut"]);
					if (bPrintfResolve)
						dbgPrint(1, 0, "MeassureStop Time  fTimeOut:%f\n",
							pCfg->uMeasurePara.sfTimePara.dTime.fVarSwitched);
				}
				else
				{
					errorInfo.eErrLever = Error;
					errorInfo.ErrCode = 24334;
				}
			}
			else  if ((strcmp(sMeasureType, "Return") == 0) || (strcmp(sMeasureType, "return") == 0))
			{
				pCfg->eMeasureMode = CurveReturn;
				if (jMeasureValue.HasMember("ReturnMode") && jMeasureValue["ReturnMode"].IsString() &&
					jMeasureValue.HasMember("ReturnType") && jMeasureValue["ReturnType"].IsString() &&
					jMeasureValue.HasMember("CurveProcessing") && jMeasureValue["CurveProcessing"].IsString())
				{
					str64 sTmpPara;
					memset(sTmpPara, 0, sizeof(str64));
					strcpy(sTmpPara, jMeasureValue["ReturnMode"].GetString());
					if ((strcmp(sTmpPara, "Left") == 0) || (strcmp(sTmpPara, "left") == 0))
					{
						pCfg->uMeasurePara.sReturnPara.eReturnMode = Left_Reutrn;
					}
					else if ((strcmp(sTmpPara, "Right") == 0) || (strcmp(sTmpPara, "right") == 0))
					{
						pCfg->uMeasurePara.sReturnPara.eReturnMode = Right_Return;
					}
					else
					{
						errorInfo.eErrLever = Error;
						errorInfo.ErrCode = 24336;
					}

					if (!errorInfo.ErrCode)
					{
						memset(sTmpPara, 0, sizeof(str64));
						strcpy(sTmpPara, jMeasureValue["ReturnType"].GetString());
						if ((strcmp(sTmpPara, "Ymax") == 0) || (strcmp(sTmpPara, "ymax") == 0))
						{
							pCfg->uMeasurePara.sReturnPara.eReturnType = Ymax;
						}
						else if ((strcmp(sTmpPara, "Xmax") == 0) || (strcmp(sTmpPara, "xmax") == 0))
						{
							pCfg->uMeasurePara.sReturnPara.eReturnType = Xmax;
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24337;
						}
					}

					if (!errorInfo.ErrCode)
					{
						memset(sTmpPara, 0, sizeof(str64));
						strcpy(sTmpPara, jMeasureValue["CurveProcessing"].GetString());
						if (strcmp(sTmpPara, "None") == 0)
						{
							pCfg->uMeasurePara.sReturnPara.eCurveProcess = None_Processing;
						}
						else if ((strcmp(sTmpPara, "Forward") == 0) || (strcmp(sTmpPara, "forward") == 0))
						{
							pCfg->uMeasurePara.sReturnPara.eCurveProcess = Forward_Processing;
						}
						else if ((strcmp(sTmpPara, "Backward") == 0) || (strcmp(sTmpPara, "backward") == 0))
						{
							pCfg->uMeasurePara.sReturnPara.eCurveProcess = Backward_Processing;
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 24338;
						}
					}

					if (bPrintfResolve)
					{
						const char* sReturnMode[3] = { "None","Left","Right" };
						const char* sReturnType[5] = { "None","Xmin","Xmax","Ymin", "Ymax" };
						const char* sCurveProcess[3] = { "None_Processing","Forward_Processing","Backward_Processing" };
						dbgPrint(1, 0, "MeassureStop Return  ReturnMode:%s ReturnType:%s CurveProcessing:%s\n",
							sReturnMode[pCfg->uMeasurePara.sReturnPara.eReturnMode],
							sReturnType[pCfg->uMeasurePara.sReturnPara.eReturnType],
							sCurveProcess[pCfg->uMeasurePara.sReturnPara.eCurveProcess]);
					}
				}
				else
				{
					errorInfo.eErrLever = Error;
					errorInfo.ErrCode = 24335;
				}
			}
			else
			{
				errorInfo.eErrLever = Error;
				errorInfo.ErrCode = 24323;
			}
		}
		else
		{
			errorInfo.eErrLever = Error;
			errorInfo.ErrCode = 24339;
		}
	}
	else
	{
		errorInfo.eErrLever = Error;
		errorInfo.ErrCode = 24329;
	}

	if (bPrintfResolve)
		dbgPrint(1, 0, "MeassureStop eMeasureMode:%s\n", sMeasureName[pCfg->eMeasureMode]);

	ErrorInfoPack(&errorInfo, "SeqItem MeasureStop Load", "");
	return  errorInfo;
}
/* SeqItem_SetValue_Load              //从Json文本中load Motion参数
* @param[in]     pSeqItem
* @param[in]     jMotion
* @param[in]     LimitParameter
* @return        ErrorInfo
*/
ErrorInfo SeqItem_SetValue_Load(SeqItem* pSeqItem, Value& jSetValue)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	SeqItem_SetValueCfg* pCfg = (SeqItem_SetValueCfg*)(pSeqItem->pCfg);

	str64   sTmpStr;
	//dbgPrint(1, 1, "SeqItem_SetValue_Load\n");
	pCfg->uSetItemCount = 0;
	if (jSetValue.IsArray())
	{
		for (auto& jValueCal : jSetValue.GetArray())
		{
			if (jValueCal.IsNull())
			{
				errorInfo.ErrCode = 25016;
				errorInfo.eErrLever = Error;
			}
			else
			{
				if (pCfg->uSetItemCount < SetValueMaxNumber)
				{
					if ((jValueCal.HasMember("AsgmtType") && jValueCal["AsgmtType"].IsString()) &&
						(jValueCal.HasMember("OpType") && jValueCal["OpType"].IsString()))
					{
						memset(sTmpStr, 0, sizeof(str64));
						strcpy(sTmpStr, jValueCal["AsgmtType"].GetString());
						if (strcmp(sTmpStr, "Y=X") == 0)
						{
							pCfg->sSetValueContext[pCfg->uSetItemCount].eSetType = Y_X;
						}
						else if (strcmp(sTmpStr, "YOX") == 0 || strcmp(sTmpStr, "Y=X1OX2") == 0)
						{
							pCfg->sSetValueContext[pCfg->uSetItemCount].eSetType = Y_X1_X2;
						}
						else
						{
							errorInfo.ErrCode = 24018;
							errorInfo.eErrLever = Error;
						}

						if (!errorInfo.ErrCode)
						{
							memset(sTmpStr, 0, sizeof(str64));
							strcpy(sTmpStr, jValueCal["OpType"].GetString());
							errorInfo = SwitchOpType(sTmpStr, &pCfg->sSetValueContext[pCfg->uSetItemCount].eValueOpType);
						}

						if (!errorInfo.ErrCode &&
							jValueCal.HasMember("Y") &&
							jValueCal.HasMember("X1"))
						{
							if (jValueCal["Y"].IsObject())
							{
								errorInfo = LoadDynaVar(&(pCfg->sSetValueContext[pCfg->uSetItemCount].dyTargtY), jValueCal["Y"]);
							}
							else if (jValueCal["Y"].IsNumber())
							{
								errorInfo.ErrCode = 25002;
								errorInfo.eErrLever = Error;
								break;
							}

							if (!errorInfo.ErrCode)
							{
								if (jValueCal["X1"].IsObject())
								{
									errorInfo = LoadDynaVar(&(pCfg->sSetValueContext[pCfg->uSetItemCount].dyX1), jValueCal["X1"]);
								}
								else if (jValueCal["X1"].IsNumber() || jValueCal["X1"].IsString())
								{
									pCfg->sSetValueContext[pCfg->uSetItemCount].dyX1.eType = DynaConst;
									errorInfo = SetConstX(pCfg->sSetValueContext[pCfg->uSetItemCount].dyTargtY.eDataType,
										jValueCal, &pCfg->sSetValueContext[pCfg->uSetItemCount].dyX1, "X1");
									if (errorInfo.ErrCode)
										break;
								}
								else
								{
									errorInfo.ErrCode = 24019;
									errorInfo.eErrLever = Error;
									break;
								}

								if (!errorInfo.ErrCode &&
									pCfg->sSetValueContext[pCfg->uSetItemCount].eSetType == Y_X1_X2 &&
									jValueCal.HasMember("X2"))
								{
									if (jValueCal["X2"].IsObject())
										errorInfo = LoadDynaVar(&(pCfg->sSetValueContext[pCfg->uSetItemCount].dyX2), jValueCal["X2"]);
									else if (jValueCal["X2"].IsNumber() || jValueCal["X2"].IsString())
									{
										pCfg->sSetValueContext[pCfg->uSetItemCount].dyX2.eType = DynaConst;
										errorInfo = SetConstX(pCfg->sSetValueContext[pCfg->uSetItemCount].dyTargtY.eDataType,
											jValueCal, &pCfg->sSetValueContext[pCfg->uSetItemCount].dyX2, "X2");

										if (errorInfo.ErrCode)
											break;
									}
									else
									{
										errorInfo.ErrCode = 24020;
										errorInfo.eErrLever = Error;
										break;
									}
								}
							}
						}
					}
					else
					{
						if (!(jValueCal.HasMember("AsgmtType") && jValueCal["AsgmtType"].IsString()))
						{
							errorInfo.ErrCode = 24021;
						}
						else
						{
							errorInfo.ErrCode = 24022;
						}
						errorInfo.eErrLever = Error;
						break;
					}
					pCfg->uSetItemCount++;
				}
				else
				{
					errorInfo.ErrCode = 24023;
					errorInfo.eErrLever = Error;
					break;
				}
			}
		}
	}
	ErrorInfoPack(&errorInfo, "SeqItem SetValue Load", "");
	return  errorInfo;
}


/* SeqItem_Motion_Load              //从Json文本中load Motion参数
* @param[in]     pSeqItem
* @param[in]     jMotion
* @param[in]     LimitParameter
* @return        ErrorInfo
*/

ErrorInfo SeqItem_Wait_Load(SeqItem* pSeqItem, Value& jWait)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	SeqItem_WaitCfg* pCfg = (SeqItem_WaitCfg*)(pSeqItem->pCfg);

	//dbgPrint(1, 1, "SeqItem_Wait_Load  iRowIndex:%d\n", pSeqItem->iRowIndex);
	if (strcasecmp(pSeqItem->szType, "WaitNotify") == 0)  // WaitForContinue
	{
		pCfg->eWaitType = WaitForContinue;
		pCfg->uWaitContext.sWaitSingal.WaitContinueIndex = jWait["ContinueIndex"].GetUint();
	}
	else if (strcasecmp(pSeqItem->szType, "WaitConstTime") == 0) //WaitForTime
	{
		pCfg->eWaitType = WaitConstTime;
		errorInfo = LoadDynaVar(&(pCfg->uWaitContext.sWaitTime.dVarWaitTime), jWait["ConstTime"]);

	}
	else if (strcasecmp(pSeqItem->szType, "WaitForMaster") == 0) //WaitForTime
	{
		dbgPrint(1, 1, "SeqItem_Wait_Load  WaitForMaster Not Finish\n");
	}
	else
	{
		dbgPrint(1, 1, "SeqItem_Wait_Load  Errorrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrr\n");
	}

	ErrorInfoPack(&errorInfo, "SeqItem_Wait_Load", "");
	return  errorInfo;
}


/* SeqItem_Motion_SensorTrig_Load
* @param[in]     pSeqItem
* @param[in]     jMotion
* @param[in]     LimitParameter
* @return        ErrorInfo
*/
ErrorInfo SeqItem_Motion_SensorTrig_Load(SeqItem* pSeqItem, Value& jMotionData, sLimitParameter  LimitParameter)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem_MotionCfg* pMotionCfg = (SeqItem_MotionCfg*)pSeqItem->pCfg;
	pMotionCfg->pAxisMc = pSeqItem->pSeqRef->pAxisMc;

	pMotionCfg->eMotion_Mode = Motion_SensorTrig;

	//1.ESensorType
	//pMotionCfg->CmdCfg.SensorTrigCfg.eSensorType = ;(ESensorType)jMotionData["SensorType"].GetInt();
	pMotionCfg->CmdCfg.SensorTrigCfg.eSensorType = StrainGauge;

	//2.sSensorName
   // strncpy(pMotionCfg->CmdCfg.SensorTrigCfg.sSensorName, jMotionData.HasMember("SensorName") ? jMotionData["SensorName"].GetString() : "", 64);
	strncpy(pMotionCfg->CmdCfg.SensorTrigCfg.sSensorName, "Force", strlen("Force"));

	pMotionCfg->CmdCfg.SensorTrigCfg.eSensorCtrlMode = jMotionData["AbsForce"].GetBool() ? SensorAbsTarget : SensorRelTarget;
	//3.TargetVal
	errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.SensorTrigCfg.dTargetVal), jMotionData["TrigForce"]);

	if (errorInfo.ErrCode == 0)
		errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.SensorTrigCfg.dTargetSpeed), jMotionData["Speed"]);


	if (errorInfo.ErrCode == 0)
		errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.SensorTrigCfg.dHoldTime_S), jMotionData["HoldTime"]);

	if (errorInfo.ErrCode == 0)
		errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.SensorTrigCfg.dLimitPos), jMotionData["PosLimit"]);

	if (errorInfo.ErrCode == 0)
	{
		if (jMotionData["Posource"].IsString())
		{
			str32 sTmpStr;
			strcpy(sTmpStr, jMotionData["Posource"].GetString());
			if (strcmp(sTmpStr, "Servo") == 0)
				pMotionCfg->CmdCfg.SensorTrigCfg.ePosLimitSource = ServoAbs;
			else if (strcmp(sTmpStr, "Ext") == 0)
				pMotionCfg->CmdCfg.SensorTrigCfg.ePosLimitSource = SensorAbs;
			else
			{
				errorInfo.ErrCode = 24079;
				errorInfo.eErrLever = Error;
			}
		}
		else
		{
			errorInfo.ErrCode = 24080;
			errorInfo.eErrLever = Error;
		}
	}

	if (errorInfo.ErrCode == 0 && jMotionData.HasMember("AutoSpeedDown"))
	{
		// Value& jReductionSpeedData = jMotionData["ReductionSpeed"];
		if (jMotionData["AutoSpeedDown"].IsBool())
		{
			pMotionCfg->CmdCfg.SensorTrigCfg.sReductionSpeed.bEnable = jMotionData["AutoSpeedDown"].GetBool();
			if (pMotionCfg->CmdCfg.SensorTrigCfg.sReductionSpeed.bEnable)
			{
				if (jMotionData.HasMember("SpeedDownTriggerRatio") && jMotionData["SpeedDownTriggerRatio"].IsNumber())
				{
					if (jMotionData["SpeedDownTriggerRatio"].IsFloat())
						pMotionCfg->CmdCfg.SensorTrigCfg.sReductionSpeed.fReductionRatio = jMotionData["SpeedDownTriggerRatio"].GetFloat();
					else if (jMotionData["SpeedDownTriggerRatio"].IsInt())
						pMotionCfg->CmdCfg.SensorTrigCfg.sReductionSpeed.fReductionRatio = (float32)jMotionData["SpeedDownTriggerRatio"].GetInt();

					pMotionCfg->CmdCfg.SensorTrigCfg.sReductionSpeed.eReductionType = InverseRatio;
				}
			}
			else
			{
				pMotionCfg->CmdCfg.SensorTrigCfg.sReductionSpeed.fReductionRatio = 0;
			}
		}
		else
		{
			pMotionCfg->CmdCfg.SensorTrigCfg.sReductionSpeed.bEnable = false;
			pMotionCfg->CmdCfg.SensorTrigCfg.sReductionSpeed.fReductionRatio = 0;
		}

		//printf("sReductionSpeed bEnable:%s fReductionRatio:%f\n",
		//    pMotionCfg->CmdCfg.SensorTrigCfg.sReductionSpeed.bEnable ? "true" : "false",
		//    pMotionCfg->CmdCfg.SensorTrigCfg.sReductionSpeed.fReductionRatio);
	}

	//if (errorInfo.ErrCode == 0)
	//{
		//dbgPrint(1, 1, "SeqItem_Motion_SensorTrig_Load\nAbsForce:%s  AutoSpeedDown::%s  TrigForce:%f  Speed:%f PosLimit:%f HoldTime:%f\n", 
		//    jMotionData["AbsForce"].GetBool() ? "true" : "false",
		//    jMotionData["AutoSpeedDown"].GetBool() ? "true" : "false",
		//    pMotionCfg->CmdCfg.SensorTrigCfg.dTargetVal.VarUnion.fVar,
		//    pMotionCfg->CmdCfg.SensorTrigCfg.dTargetSpeed.VarUnion.fVar,
		//    pMotionCfg->CmdCfg.SensorTrigCfg.dLimitPos.VarUnion.fVar,
		//    pMotionCfg->CmdCfg.SensorTrigCfg.dHoldTime_S.VarUnion.fVar);
	//}

	ErrorInfoPack(&errorInfo, "SeqItem_Motion_SensorTrig_Load", "");
	return  errorInfo;
}


/* SeqItem_Motion_ForceLoop_Load
* @param[in]     pSeqItem
* @param[in]     jMotion
* @param[in]     LimitParameter
* @return        ErrorInfo
*/
ErrorInfo SeqItem_Motion_SlopeTrig_Load(SeqItem* pSeqItem, Value& jMotionData)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem_MotionCfg* pMotionCfg = (SeqItem_MotionCfg*)pSeqItem->pCfg;
	pMotionCfg->pAxisMc = pSeqItem->pSeqRef->pAxisMc;

	pMotionCfg->eMotion_Mode = Motion_SlopeTrig;

	//1.ESensorType
	//pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.eSensorType = ;(ESensorType)jMotionData["SensorType"].GetInt();
	pMotionCfg->CmdCfg.SlopeTrigCfg.eSensorType = StrainGauge;

	//2.sSensorName
   // strncpy(pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sSensorName, jMotionData.HasMember("SensorName") ? jMotionData["SensorName"].GetString() : "", 64);
	strncpy(pMotionCfg->CmdCfg.SlopeTrigCfg.sSensorName, "Force", strlen("Force"));

	if (jMotionData.HasMember("TargetSlope") &&
		jMotionData.HasMember("RegSpeed") &&
		jMotionData.HasMember("AutoSpeedDown") &&
		jMotionData.HasMember("HoldTime") &&
		jMotionData.HasMember("Posource") &&
		jMotionData.HasMember("PosLimit") &&
		jMotionData.HasMember("ForceMax") &&
		jMotionData.HasMember("ForceMin"))
	{
		errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.SlopeTrigCfg.dTargetSlope), jMotionData["TargetSlope"]);
		if (errorInfo.ErrCode)
		{
			errorInfo.ErrCode = 24075;
			errorInfo.eErrLever = Error;
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.SlopeTrigCfg.dRegSpeed), jMotionData["RegSpeed"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24076;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{

			if (jMotionData["AutoSpeedDown"].IsBool())
				pMotionCfg->CmdCfg.SlopeTrigCfg.sReductionSpeed.bEnable = jMotionData["AutoSpeedDown"].GetBool();
			else
			{
				errorInfo.ErrCode = 24077;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.SlopeTrigCfg.dHoldTime), jMotionData["HoldTime"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24078;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{
			if (jMotionData["Posource"].IsString())
			{
				str32 sTmpStr;
				strcpy(sTmpStr, jMotionData["Posource"].GetString());
				if (strcmp(sTmpStr, "Servo") == 0)
					pMotionCfg->CmdCfg.SlopeTrigCfg.ePosLimitSource = ServoAbs;
				else if (strcmp(sTmpStr, "Ext") == 0)
					pMotionCfg->CmdCfg.SlopeTrigCfg.ePosLimitSource = SensorAbs;
				else
				{
					errorInfo.ErrCode = 24079;
					errorInfo.eErrLever = Error;
				}
			}
			else
			{
				errorInfo.ErrCode = 24080;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.SlopeTrigCfg.dPosLimit), jMotionData["PosLimit"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24081;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.SlopeTrigCfg.dForceMax), jMotionData["ForceMax"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24082;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.SlopeTrigCfg.dForceMin), jMotionData["ForceMin"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24083;
				errorInfo.eErrLever = Error;
			}
		}
	}
	else
	{
		errorInfo.ErrCode = 24074;
		errorInfo.eErrLever = Error;
	}

	//if (errorInfo.ErrCode != 0)
	//{
	//dbgPrint(1, 1, "\n ErrCode:%d AutoSpeedDown:%s  TargetSlope:%f  dRegSpeed:%f dHoldTime:%f  dPosLimit:%f dForceMin:%f dForceMax:%f\n",
	//    errorInfo.ErrCode,
	//    pMotionCfg->CmdCfg.SlopeTrigCfg.sReductionSpeed.bEnable ? "true" : "false",
	//    pMotionCfg->CmdCfg.SlopeTrigCfg.dTargetSlope.fVarSwitched,
	//    pMotionCfg->CmdCfg.SlopeTrigCfg.dRegSpeed.fVarSwitched,
	//    pMotionCfg->CmdCfg.SlopeTrigCfg.dHoldTime.fVarSwitched,
	//    pMotionCfg->CmdCfg.SlopeTrigCfg.dPosLimit.fVarSwitched,
	//    pMotionCfg->CmdCfg.SlopeTrigCfg.dForceMin.fVarSwitched,
	//    pMotionCfg->CmdCfg.SlopeTrigCfg.dForceMax.fVarSwitched);
//    }

	ErrorInfoPack(&errorInfo, "SeqItem_Motion_SlopeTrig_Load", "");
	return  errorInfo;
}


/* SeqItem_Motion_ForceLoop_Load
* @param[in]     pSeqItem
* @param[in]     jMotion
* @param[in]     LimitParameter
* @return        ErrorInfo
*/
ErrorInfo SeqItem_Motion_ForceLoop_Load(SeqItem* pSeqItem, Value& jMotionData)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem_MotionCfg* pMotionCfg = (SeqItem_MotionCfg*)pSeqItem->pCfg;
	pMotionCfg->pAxisMc = pSeqItem->pSeqRef->pAxisMc;

	pMotionCfg->eMotion_Mode = Motion_ForceLoop;

	//1.ESensorType
	//pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.eSensorType = ;(ESensorType)jMotionData["SensorType"].GetInt();
	pMotionCfg->CmdCfg.ForceLoopCfg.eSensorType = StrainGauge;

	//2.sSensorName
   // strncpy(pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sSensorName, jMotionData.HasMember("SensorName") ? jMotionData["SensorName"].GetString() : "", 64);
	strncpy(pMotionCfg->CmdCfg.ForceLoopCfg.sSensorName, "Force", strlen("Force"));

	if (jMotionData.HasMember("AbsForce") && jMotionData["AbsForce"].IsBool())
		pMotionCfg->CmdCfg.ForceLoopCfg.eSensorCtrlMode = jMotionData["AbsForce"].GetBool() ? SensorAbsTarget : SensorRelTarget;
	else
	{
		errorInfo.ErrCode = 24024;
		errorInfo.eErrLever = Error;
	}

	if (jMotionData.HasMember("TargetForce") &&
		jMotionData.HasMember("PosLimit") &&
		jMotionData.HasMember("RegSpeed") &&
		jMotionData.HasMember("HoldTime") &&
		jMotionData.HasMember("ForceMin") &&
		jMotionData.HasMember("ForceMax") &&
		jMotionData.HasMember("ForceRegTout") &&
		jMotionData.HasMember("Posource"))
	{
		if (jMotionData["Posource"].IsString())
		{
			str32 sTmpStr;
			strcpy(sTmpStr, jMotionData["Posource"].GetString());
			if (strcmp(sTmpStr, "Servo") == 0)
				pMotionCfg->CmdCfg.ForceLoopCfg.ePosLimitSource = ServoAbs;
			else if (strcmp(sTmpStr, "Ext") == 0)
				pMotionCfg->CmdCfg.ForceLoopCfg.ePosLimitSource = SensorAbs;
			else
			{
				errorInfo.ErrCode = 24079;
				errorInfo.eErrLever = Error;
			}
		}
		else
		{
			errorInfo.ErrCode = 24080;
			errorInfo.eErrLever = Error;
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.ForceLoopCfg.dTargetForce), jMotionData["TargetForce"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24025;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.ForceLoopCfg.dLimitPos), jMotionData["PosLimit"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24026;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.ForceLoopCfg.dSpeed), jMotionData["RegSpeed"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24027;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.ForceLoopCfg.dHoldTime_S), jMotionData["HoldTime"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24028;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.ForceLoopCfg.dLimitForce_Min), jMotionData["ForceMin"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24029;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.ForceLoopCfg.dLimitForce_Max), jMotionData["ForceMax"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24030;
				errorInfo.eErrLever = Error;
			}
		}

		if (!errorInfo.ErrCode)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.ForceLoopCfg.dRegTimeout), jMotionData["ForceRegTout"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24031;
				errorInfo.eErrLever = Error;
			}
		}
	}
	else
	{
		errorInfo.ErrCode = 24085;
		errorInfo.eErrLever = Error;
	}


	if (!errorInfo.ErrCode)
	{
		if (jMotionData.HasMember("IsPID") && jMotionData["IsPID"].IsBool())
		{
			pMotionCfg->CmdCfg.ForceLoopCfg.bUseOwnPid = jMotionData["IsPID"].GetBool();
			if (pMotionCfg->CmdCfg.ForceLoopCfg.bUseOwnPid)
			{
				if (jMotionData.HasMember("Kp") &&
					jMotionData.HasMember("Ki"))
				{
					errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.ForceLoopCfg.dKpOwn), jMotionData["Kp"]);
					if (errorInfo.ErrCode)
					{
						errorInfo.ErrCode = 24032;
						errorInfo.eErrLever = Error;
					}

					if (!errorInfo.ErrCode)
					{
						errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.ForceLoopCfg.dKiOwn), jMotionData["Ki"]);
						if (errorInfo.ErrCode)
						{
							errorInfo.ErrCode = 24033;
							errorInfo.eErrLever = Error;
						}
					}
				}
			}
		}
		else
		{
			errorInfo.ErrCode = 24034;
			errorInfo.eErrLever = Error;
		}

		if (jMotionData.HasMember("IsPrePoint") && jMotionData["IsPrePoint"].IsBool())
		{
			pMotionCfg->CmdCfg.ForceLoopCfg.bIsPrePoint = jMotionData["IsPrePoint"].GetBool();
			if (pMotionCfg->CmdCfg.ForceLoopCfg.bIsPrePoint)
			{
				if (jMotionData.HasMember("PrePointForce") &&
					jMotionData.HasMember("PrePointSpeed"))
				{
					errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.ForceLoopCfg.dPreForce), jMotionData["PrePointForce"]);
					if (errorInfo.ErrCode)
					{
						errorInfo.ErrCode = 24036;
						errorInfo.eErrLever = Error;
					}

					if (!errorInfo.ErrCode)
					{
						errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.ForceLoopCfg.dPreSpeed), jMotionData["PrePointSpeed"]);
						if (errorInfo.ErrCode)
						{
							errorInfo.ErrCode = 24037;
							errorInfo.eErrLever = Error;
						}
					}
				}
				else
				{
					errorInfo.ErrCode = 24039;
					errorInfo.eErrLever = Error;
				}
			}
		}
		else
		{
			errorInfo.ErrCode = 24035;
			errorInfo.eErrLever = Error;
		}
	}

	if (errorInfo.ErrCode != 0)
	{
		//dbgPrint(1, 1, "PositionOrSensorTrig_Load\nAbsForce:%s  AutoSpeedDown::%s  TrigForce:%f  TrigPos:%f Speed:%f  HoldTime:%f\n",
		//    jMotionData["AbsForce"].GetBool() ? "true" : "false",
		//    jMotionData["AutoSpeedDown"].GetBool() ? "true" : "false",
		//    pMotionCfg->CmdCfg.ForceLoopCfg..VarUnion.fVar,
		//    pMotionCfg->CmdCfg.ForceLoopCfg.dTargetPositon.VarUnion.fVar,
		//    pMotionCfg->CmdCfg.ForceLoopCfg.dTargetSpeed.VarUnion.fVar,
		//    pMotionCfg->CmdCfg.ForceLoopCfg.dHoldTime_S.VarUnion.fVar);
	}

	ErrorInfoPack(&errorInfo, "PositionOrSensorTrig_Load", "");
	return  errorInfo;
}


/* SeqItem_Motion_PositionOrSensorTrig_Load
* @param[in]     pSeqItem
* @param[in]     jMotion
* @param[in]     LimitParameter
* @return        ErrorInfo
*/
ErrorInfo SeqItem_Motion_PositionOrSensorTrig_Load(SeqItem* pSeqItem, Value& jMotionData, sLimitParameter  LimitParameter)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem_MotionCfg* pMotionCfg = (SeqItem_MotionCfg*)pSeqItem->pCfg;
	pMotionCfg->pAxisMc = pSeqItem->pSeqRef->pAxisMc;

	pMotionCfg->eMotion_Mode = Motion_PositonOrSensorTrig;

	//1.ESensorType
	//pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.eSensorType = ;(ESensorType)jMotionData["SensorType"].GetInt();
	pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.eSensorType = StrainGauge;

	//2.sSensorName
   // strncpy(pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sSensorName, jMotionData.HasMember("SensorName") ? jMotionData["SensorName"].GetString() : "", 64);
	strncpy(pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sSensorName, "Force", strlen("Force"));

	str255 tmpType;
	strcpy(tmpType, jMotionData.HasMember("MoveType") ? jMotionData["MoveType"].GetString() : "");
	if (strlen(tmpType) > 0)
	{
		pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.ePosCtrlMode = (EPosCtrlMode)(strcasecmp(tmpType, "ServoAbs") == 0 ? ServoAbs :
			(strcasecmp(tmpType, "ServoRel") == 0 ? ServoRel :
				(strcasecmp(tmpType, "ExtAbs") == 0 ? SensorAbs :
					(strcasecmp(tmpType, "ExtRel") == 0 ? SensorRel : 0xFF))));
	}

	if (jMotionData.HasMember("AbsForce") && jMotionData["AbsForce"].IsBool())
		pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.eSensorCtrlMode = jMotionData["AbsForce"].GetBool() ? SensorAbsTarget : SensorRelTarget;
	else
	{
		errorInfo.ErrCode = 24039;
		errorInfo.eErrLever = Error;
	}
	//3.TargetVal
	if (!errorInfo.ErrCode)
	{
		if (jMotionData.HasMember("TrigForce") &&
			jMotionData.HasMember("TrigPos") &&
			jMotionData.HasMember("Speed") &&
			jMotionData.HasMember("HoldTime"))
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.dTargetVal), jMotionData["TrigForce"]);
			if (errorInfo.ErrCode)
			{
				errorInfo.ErrCode = 24041;
				errorInfo.eErrLever = Error;
			}

			if (!errorInfo.ErrCode)
			{
				errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.dTargetPositon), jMotionData["TrigPos"]);
				if (errorInfo.ErrCode)
				{
					errorInfo.ErrCode = 24042;
					errorInfo.eErrLever = Error;
				}
			}

			if (!errorInfo.ErrCode)
			{
				errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.dTargetSpeed), jMotionData["Speed"]);
				if (errorInfo.ErrCode)
				{
					errorInfo.ErrCode = 24043;
					errorInfo.eErrLever = Error;
				}
			}

			if (!errorInfo.ErrCode)
			{
				errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.dHoldTime_S), jMotionData["HoldTime"]);
				if (errorInfo.ErrCode)
				{
					errorInfo.ErrCode = 24044;
					errorInfo.eErrLever = Error;
				}
			}
		}
		else
		{
			errorInfo.ErrCode = 24040;
			errorInfo.eErrLever = Error;
		}
	}


	if (errorInfo.ErrCode == 0 && jMotionData.HasMember("AutoSpeedDown"))
	{
		if (jMotionData["AutoSpeedDown"].IsBool())
		{
			pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sReductionSpeed.bEnable = jMotionData["AutoSpeedDown"].GetBool();
			if (pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sReductionSpeed.bEnable)
			{
				if (jMotionData.HasMember("SpeedDownTriggerRatio") && jMotionData["SpeedDownTriggerRatio"].IsNumber())
				{
					if (jMotionData["SpeedDownTriggerRatio"].IsFloat())
						pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sReductionSpeed.fReductionRatio = jMotionData["SpeedDownTriggerRatio"].GetFloat();
					else if (jMotionData["SpeedDownTriggerRatio"].IsInt())
						pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sReductionSpeed.fReductionRatio = (float32)jMotionData["SpeedDownTriggerRatio"].GetInt();

					pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sReductionSpeed.eReductionType = InverseRatio;
				}
			}
			else
			{
				pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sReductionSpeed.fReductionRatio = 0;
			}
		}
		else
		{
			pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sReductionSpeed.bEnable = false;
			pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sReductionSpeed.fReductionRatio = 0;
		}

		printf("sReductionSpeed bEnable:%s fReductionRatio:%f\n",
			pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sReductionSpeed.bEnable ? "true" : "false",
			pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.sReductionSpeed.fReductionRatio);
	}


	if (errorInfo.ErrCode != 0)
	{
		//dbgPrint(1, 1, "PositionOrSensorTrig_Load\nAbsForce:%s  AutoSpeedDown::%s  TrigForce:%f  TrigPos:%f Speed:%f  HoldTime:%f\n",
		//    jMotionData["AbsForce"].GetBool() ? "true" : "false",
		//    jMotionData["AutoSpeedDown"].GetBool() ? "true" : "false",
		//    pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.dTargetVal.VarUnion.fVar,
		//    pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.dTargetPositon.VarUnion.fVar,
		//    pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.dTargetSpeed.VarUnion.fVar,
		//    pMotionCfg->CmdCfg.PositionOrSensorTrigCfg.dHoldTime_S.VarUnion.fVar);
	}

	ErrorInfoPack(&errorInfo, "PositionOrSensorTrig_Load", "");
	return  errorInfo;
}


/* SeqItem_Motion_PositionControl_Load
* @param[in]     pSeqItem
* @param[in]     jMotionData
* @param[in]     LimitParameter
* @return        ErrorCode
*/
ErrorInfo SeqItem_Motion_PositionControl_Load(SeqItem* pSeqItem, Value& jMotionData, sLimitParameter  LimitParameter)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem_MotionCfg* pMotionCfg = (SeqItem_MotionCfg*)pSeqItem->pCfg;
	pMotionCfg->pAxisMc = pSeqItem->pSeqRef->pAxisMc;
	str255 tmpType = "";
	memset(&tmpType[0], 0, sizeof(str255));
	if (jMotionData.HasMember("MoveType") &&
		jMotionData.HasMember("TargetPos") &&
		jMotionData.HasMember("Speed") &&
		jMotionData.HasMember("ForceMax") &&
		jMotionData.HasMember("ForceMin") &&
		jMotionData.HasMember("DeformCmpnstn"))            //校验参数的完整性
	{
		strcpy(tmpType, jMotionData.HasMember("MoveType") ? jMotionData["MoveType"].GetString() : "");
		if (strlen(tmpType))
		{
			pMotionCfg->CmdCfg.PosCfg.ePosCtrlMode = (EPosCtrlMode)(strcasecmp(tmpType, "ServoAbs") == 0 ? ServoAbs :
				(strcasecmp(tmpType, "ServoRel") == 0 ? ServoRel :
					(strcasecmp(tmpType, "ExtAbs") == 0 ? SensorAbs :
						(strcasecmp(tmpType, "ExtRel") == 0 ? SensorRel : 0xFF))));

			if (pMotionCfg->CmdCfg.PosCfg.ePosCtrlMode == 0xFF)
			{
				errorInfo.ErrCode = 24049;
				errorInfo.eErrLever = Error;
			}
			else
			{
				pMotionCfg->eMotion_Mode = Motion_Position;
				errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.PosCfg.dTargetPosition), jMotionData["TargetPos"]);

				if (errorInfo.ErrCode == 0)
					errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.PosCfg.dTargetSpeed), jMotionData["Speed"]);

				if (errorInfo.ErrCode == 0)
					errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.PosCfg.dLimitForce_Max), jMotionData["ForceMax"]);

				if (errorInfo.ErrCode == 0)
					errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.PosCfg.dLimitForce_Min), jMotionData["ForceMin"]);


				Value& jDeformCompensation = jMotionData["DeformCmpnstn"];
				pMotionCfg->CmdCfg.PosCfg.sDeformCompensation.IsEnabled = jDeformCompensation["Enabled"].GetBool();

				if (errorInfo.ErrCode == 0)
					errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.PosCfg.sDeformCompensation.DeformCompensationVar), jDeformCompensation["Var"]);
			}
		}
		else
		{
			errorInfo.ErrCode = 24048;
			errorInfo.eErrLever = Error;
		}
	}
	else
	{
		errorInfo.ErrCode = 24047;
		errorInfo.eErrLever = Error;
	}

	ErrorInfoPack(&errorInfo, "SeqItem_Motion_PositionControl_Load", "");
	return  errorInfo;
}

/* SeqItem_Motion_GoHome_Load
* @param[in]     pSeqItem
* @param[in]     jMotionData
* @param[in]     LimitParameter
* @return        ErrorCode
*/
ErrorInfo SeqItem_Motion_GoHome_Load(SeqItem* pSeqItem, Value& jGoHome, sLimitParameter  LimitParameter)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem_MotionCfg* pMotionCfg = (SeqItem_MotionCfg*)pSeqItem->pCfg;
	pMotionCfg->pAxisMc = pSeqItem->pSeqRef->pAxisMc;

	pMotionCfg->eMotion_Mode = Motion_GoHome;

	if (jGoHome.HasMember("Enabled") && jGoHome.HasMember("Pos") && jGoHome.HasMember("Speed") && jGoHome.HasMember("LimitUp") && jGoHome.HasMember("LimitDown"))
	{
		pMotionCfg->CmdCfg.GoHome.bEnableGoHome = jGoHome["Enabled"].GetBool();

		Value& jHomePos = jGoHome["Pos"];
		errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.GoHome.dHomePos), jHomePos);

		Value& jTargetSpeed = jGoHome["Speed"];
		if (errorInfo.ErrCode == 0)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.GoHome.dVelocity), jTargetSpeed);
			if (!errorInfo.ErrCode)
			{
				pSysShareData->sSysJogPara.fProfileHomeVelocity = pMotionCfg->CmdCfg.GoHome.dVelocity.VarUnion.fVar;
				// dbgPrint(1, 0, "\n 1 sSysJogPara.fProfileHomeVelocity %f\n\n", pSysShareData->sSysJogPara.fProfileHomeVelocity);
			}
			//dbgPrint(1, 0, "\n 2 sSysJogPara.fProfileHomeVelocity %f\n\n", pSysShareData->sSysJogPara.fProfileHomeVelocity);
		}


		Value& jLimitUp = jGoHome["LimitUp"];
		if (errorInfo.ErrCode == 0)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.GoHome.dLimitForce_Max), jLimitUp);
			if (!errorInfo.ErrCode)
				pSysShareData->sSysJogPara.fProfileHomeForceMax = pMotionCfg->CmdCfg.GoHome.dLimitForce_Max.VarUnion.fVar;
			else
				pSysShareData->sSysJogPara.fProfileHomeForceMax = pSysShareData->sSysJogPara.fForceMax;
		}


		Value& jLimitDown = jGoHome["LimitDown"];
		if (errorInfo.ErrCode == 0)
		{
			errorInfo = LoadDynaVar(&(pMotionCfg->CmdCfg.GoHome.dLimitForce_Min), jLimitDown);
			if (!errorInfo.ErrCode)
				pSysShareData->sSysJogPara.fProfileHomeForceMin = pMotionCfg->CmdCfg.GoHome.dLimitForce_Min.VarUnion.fVar;
			else
				pSysShareData->sSysJogPara.fProfileHomeForceMin = pSysShareData->sSysJogPara.fForceMin;
		}


		fHomePos = pMotionCfg->CmdCfg.GoHome.dHomePos.VarUnion.fVar;
	}
	else
	{
		errorInfo.ErrCode = 24051;
		errorInfo.eErrLever = Error;
	}

	ErrorInfoPack(&errorInfo, "SeqItem_Motion_GoHome_Load", "");
	return  errorInfo;
}

/* SeqItem_Motion_Load              //从Json文本中load Motion参数
* @param[in]     pSeqItem
* @param[in]     jMotion
* @param[in]     LimitParameter
* @return        ErrorInfo
*/
ErrorInfo SeqItem_Motion_Load(SeqItem* pSeqItem, Value& jMotion, sLimitParameter  LimitParameter)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem_MotionCfg* pCfg = (SeqItem_MotionCfg*)(pSeqItem->pCfg);

	if (strcasecmp(pSeqItem->szType, "PosCntrl") == 0)  // PositionControl
	{
		errorInfo = SeqItem_Motion_PositionControl_Load(pSeqItem, jMotion, LimitParameter);
	}
	else if (strcasecmp(pSeqItem->szType, "ForceTrigCntrl") == 0) //  SensorTrigControl Force Trig Control
	{
		errorInfo = SeqItem_Motion_SensorTrig_Load(pSeqItem, jMotion, LimitParameter);
	}
	else if (strcasecmp(pSeqItem->szType, "PosOrForceTrig") == 0) //0x02:Force Close Loop Control
	{
		errorInfo = SeqItem_Motion_PositionOrSensorTrig_Load(pSeqItem, jMotion, LimitParameter);
	}
	else if (strcasecmp(pSeqItem->szType, "ForceLoopCntrl") == 0) //0x03:Advance Force Close Loop Control
	{
		errorInfo = SeqItem_Motion_ForceLoop_Load(pSeqItem, jMotion);
	}
	else if (strcasecmp(pSeqItem->szType, "SlopeTrigCntrl") == 0) //0x04: Slope Trig Control
	{
		errorInfo = SeqItem_Motion_SlopeTrig_Load(pSeqItem, jMotion);
	}
	else
	{
		errorInfo.ErrCode = 24051;
		errorInfo.eErrLever = Error;
	}
	ErrorInfoPack(&errorInfo, "SeqItem_Motion_Load", "szType:%s", pSeqItem->szType);
	return  errorInfo;
}


//add

/* SeqItem_If_Load              //从Json文本中load If参数
* @param[in]     pSeqItem
* @param[in]     jIf
* @return        ErrorInfo
*/
ErrorInfo SeqItem_If_Load(SeqItem* pSeqItem, Value& jIfContent)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	SeqItem_IfCfg* pCfg = (SeqItem_IfCfg*)(pSeqItem->pCfg);

	Value& content = jIfContent;

	// 检查是否存在 Conditions 字段，并且是一个数组
	if (!content.HasMember("Conditions") || !content["Conditions"].IsArray()) {
		std::cerr << "Invalid JSON format: 'Conditions' array is missing or not an array." << std::endl;
		errorInfo.ErrCode = 24501;
		ErrorInfoPack(&errorInfo, "SeqItem If Load：Invalid JSON format-Condition", "");
		return errorInfo;
	}

	Value& conditionsArray = content["Conditions"];

	pCfg->GrounpCounter = conditionsArray.Size();
	for (SizeType i = 0; i < conditionsArray.Size(); ++i) {
		Value& condition = conditionsArray[i];
		if (!condition.IsObject()) {
			std::cerr << "Invalid JSON format: 'Conditions' array element is not an object." << std::endl;
			errorInfo.ErrCode = 24501;
			ErrorInfoPack(&errorInfo, "SeqItem If Load", "");
			return errorInfo;
		}

		// 获取条件中的字段值
		if (!condition.HasMember("ItemsType") || !condition["ItemsType"].IsString() ||
			!condition.HasMember("OpType") || !condition["OpType"].IsString() || !condition.HasMember("Var1") || !condition.HasMember("Var2")) {
			std::cerr << "Invalid JSON format: 'Conditions' array element is missing required fields or they are of invalid types." << std::endl;
			errorInfo.ErrCode = 24501;
			ErrorInfoPack(&errorInfo, "SeqItem If Load：Invalid JSON format-ItemsType OpType ", "");
			return errorInfo;
		}
		// 更新 SeqItem_IfCfg 结构体中的数据
		pCfg->aIfsData[i].BoolOpType = strcmp(condition["ItemsType"].GetString(), "&&") == 0 ? 0 :
			strcmp(condition["ItemsType"].GetString(), "||") == 0 ? 1 : 2;
		pCfg->aIfsData[i].CompareType = strcmp(condition["OpType"].GetString(), "==") == 0 ? 0 :
			strcmp(condition["OpType"].GetString(), "!=") == 0 ? 1 :
			strcmp(condition["OpType"].GetString(), ">=") == 0 ? 2 :
			strcmp(condition["OpType"].GetString(), ">") == 0 ? 3 :
			strcmp(condition["OpType"].GetString(), "<=") == 0 ? 4 : 5;

		//dVar1 dVar2
		errorInfo = LoadDynaVar(&(pCfg->aIfsData[i].dVar1), condition["Var1"]);
		errorInfo = LoadDynaVar(&(pCfg->aIfsData[i].dVar2), condition["Var2"]);

	}

	// 检查是否存在 SetValue 字段，并且是一个整数???这个需要修改下
	if (!content.HasMember("SetValue")) {
		std::cerr << "Invalid JSON format: 'SetValue' field is missing or not an integer." << std::endl;
		errorInfo.ErrCode = 24501;
		ErrorInfoPack(&errorInfo, "SeqItem If Load：Invalid JSON format-SetValue", "");
		return errorInfo;
	}

	//这个得跟上位机说下是否需要bool量
	pCfg->bResult = false;

	//dSetValue
	errorInfo = LoadDynaVar(&(pCfg->dSetValue), content["SetValue"]);


	ErrorInfoPack(&errorInfo, "SeqItem If Load", "");
	return  errorInfo;
}

/* SeqItem_ElseIf_Load              //从Json文本中load If参数   可能不需要
* @param[in]     pSeqItem
* @param[in]     jIf
* @return        ErrorInfo
*/
ErrorInfo SeqItem_ElseIf_Load(SeqItem* pSeqItem, Value& jIfContent)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	SeqItem_IfCfg* pCfg = (SeqItem_IfCfg*)(pSeqItem->pCfg);

	Value& content = jIfContent;

	// 检查是否存在 Conditions 字段，并且是一个数组
	if (!content.HasMember("Conditions") || !content["Conditions"].IsArray()) {
		std::cerr << "Invalid JSON format: 'Conditions' array is missing or not an array." << std::endl;
		errorInfo.ErrCode = 24501;
		ErrorInfoPack(&errorInfo, "SeqItem If Load：Invalid JSON format-Condition", "");
		return errorInfo;
	}

	Value& conditionsArray = content["Conditions"];

	for (SizeType i = 0; i < conditionsArray.Size(); ++i) {
		Value& condition = conditionsArray[i];
		if (!condition.IsObject()) {
			std::cerr << "Invalid JSON format: 'Conditions' array element is not an object." << std::endl;
			errorInfo.ErrCode = 24501;
			ErrorInfoPack(&errorInfo, "SeqItem If Load", "");
			return errorInfo;
		}

		// 获取条件中的字段值
		if (!condition.HasMember("ItemsType") || !condition["ItemsType"].IsString() ||
			!condition.HasMember("OpType") || !condition["OpType"].IsString() || !condition.HasMember("Var1") || !condition.HasMember("Var2")) {
			std::cerr << "Invalid JSON format: 'Conditions' array element is missing required fields or they are of invalid types." << std::endl;
			errorInfo.ErrCode = 24501;
			ErrorInfoPack(&errorInfo, "SeqItem If Load：Invalid JSON format-ItemsType OpType ", "");
			return errorInfo;
		}
		// 更新 SeqItem_IfCfg 结构体中的数据
		pCfg->aIfsData[i].BoolOpType = strcmp(condition["ItemsType"].GetString(), "&&") == 0 ? 0 :
			strcmp(condition["ItemsType"].GetString(), "||") == 0 ? 1 : 2;
		pCfg->aIfsData[i].CompareType = strcmp(condition["OpType"].GetString(), "=") == 0 ? 0 :
			strcmp(condition["OpType"].GetString(), "!=") == 0 ? 1 :
			strcmp(condition["OpType"].GetString(), ">=") == 0 ? 2 :
			strcmp(condition["OpType"].GetString(), ">") == 0 ? 3 :
			strcmp(condition["OpType"].GetString(), "<=") == 0 ? 4 : 5;

		//dVar1 dVar2
		errorInfo = LoadDynaVar(&(pCfg->aIfsData[i].dVar1), condition["Var1"]);
		errorInfo = LoadDynaVar(&(pCfg->aIfsData[i].dVar2), condition["Var2"]);

	}

	// 检查是否存在 SetValue 字段，并且是一个整数???这个需要修改下
	if (!content.HasMember("SetValue")) {
		std::cerr << "Invalid JSON format: 'SetValue' field is missing or not an integer." << std::endl;
		errorInfo.ErrCode = 24501;
		ErrorInfoPack(&errorInfo, "SeqItem If Load：Invalid JSON format-SetValue", "");
		return errorInfo;
	}

	//这个得跟上位机说下是否需要bool量
	pCfg->bResult = false;

	//dSetValue
	errorInfo = LoadDynaVar(&(pCfg->dSetValue), content["SetValue"]);


	ErrorInfoPack(&errorInfo, "SeqItem If Load", "");
	return  errorInfo;
}
/* SeqItem_Label_Load              //从Json文本中load Jump参数
* @param[in]     pSeqItem
* @param[in]     jJump
* @param[in]
* @return        ErrorInfo
*/
ErrorInfo SeqItem_Label_Load(SeqItem* pSeqItem, Value& jLabel)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	SeqItem_LabelCfg* pCfg = (SeqItem_LabelCfg*)(pSeqItem->pCfg);

	if (jLabel.IsObject()) {
		// 解析 LabelName
		if (jLabel.HasMember("LabelName") && jLabel["LabelName"].IsString()) {
			strncpy(pCfg->sLabelName, jLabel["LabelName"].GetString(), sizeof(pCfg->sLabelName) - 1);
			pCfg->sLabelName[sizeof(pCfg->sLabelName) - 1] = '\0'; // Null-terminate
		}
		else {
			ErrorInfoPack(&errorInfo, "Label-LabelName is missing or not a string", "");
		}
	}
	else {
		ErrorInfoPack(&errorInfo, "Label is not an object", "");
	}

	ErrorInfoPack(&errorInfo, "SeqItem_Label_Load", "");
	return errorInfo;
}

/* SeqItem_Jump_Load              //从Json文本中load Jump参数
* @param[in]     pSeqItem
* @param[in]     jJump
* @param[in]
* @return        ErrorInfo
*/
ErrorInfo SeqItem_Jump_Load(SeqItem* pSeqItem, Value& jJump) {
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	SeqItem_JumpCfg* pCfg = (SeqItem_JumpCfg*)(pSeqItem->pCfg);

	if (!jJump.IsObject()) {
		ErrorInfoPack(&errorInfo, "SeqItem_Jump_Load", "jJump is not a JSON object");
		return errorInfo;
	}

	//确定正确的content对象 - 检查是否需要从jJump中提取Content字段
	const Value* pContent = &jJump;
	if (jJump.HasMember("Content") && jJump["Content"].IsObject()) {
		pContent = &(jJump["Content"]);
	}

	//处理LabelName字段
	if (pContent->HasMember("LabelName")) {
		if ((*pContent)["LabelName"].IsString()) {
			const char* szLabelName = (*pContent)["LabelName"].GetString();
			strncpy(pCfg->sTargetLabelName, szLabelName, sizeof(pCfg->sTargetLabelName) - 1);
			pCfg->sTargetLabelName[sizeof(pCfg->sTargetLabelName) - 1] = '\0'; // 确保字符串终止
		}
		else {
			ErrorInfoPack(&errorInfo, "SeqItem_Jump_Load", "LabelName is not a string");
			return errorInfo;
		}
	}
	else {
		//LabelName不存在时，初始化为空字符串
		pCfg->sTargetLabelName[0] = '\0';
	}

	// 检查TargetRowIndex字段是否存在
	if (!pContent->HasMember("TargetRowIndex")) {
		ErrorInfoPack(&errorInfo, "SeqItem_Jump_Load", "Missing required field: TargetRowIndex");
		errorInfo.ErrCode = 24056;
		return errorInfo;
	}

	// 处理TargetRowIndex字段
	const Value& targetRowValue = (*pContent)["TargetRowIndex"];
	if (targetRowValue.IsInt()) {
		// 直接获取整数值
		pCfg->iTargetRowIndex = targetRowValue.GetInt();
	}
	else if (targetRowValue.IsString()) {
		//尝试将字符串转换为整数
		const char* szTargetRow = targetRowValue.GetString();
		char* endPtr;
		long int liValue = strtol(szTargetRow, &endPtr, 10);
		if (*endPtr != '\0' || liValue < INT_MIN || liValue > INT_MAX) {
			ErrorInfoPack(&errorInfo, "SeqItem_Jump_Load", "TargetRowIndex string is not a valid integer");
			errorInfo.ErrCode = 24054;
			return errorInfo;
		}
		pCfg->iTargetRowIndex = (int)liValue;
	}
	else {
		ErrorInfoPack(&errorInfo, "SeqItem_Jump_Load", "TargetRowIndex must be an integer or string");
		errorInfo.ErrCode = 24055;
		return errorInfo;
	}

	return errorInfo;
}


/* SeqItem_While_Load               //看下While是否和IF一致
* @param[in]     pSeqItem
* @param[in]     jIfContent
* @param[in]
* @return        ErrorInfo
*/
ErrorInfo SeqItem_While_Load(SeqItem* pSeqItem, Value& jIfContent)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	SeqItem_IfCfg* pCfg = (SeqItem_IfCfg*)(pSeqItem->pCfg);

	Value& content = jIfContent;

	// 检查是否存在 Conditions 字段，并且是一个数组
	if (!content.HasMember("Conditions") || !content["Conditions"].IsArray()) {
		std::cerr << "Invalid JSON format: 'Conditions' array is missing or not an array." << std::endl;
		errorInfo.ErrCode = 24501;
		ErrorInfoPack(&errorInfo, "SeqItem If Load：Invalid JSON format-Condition", "");
		return errorInfo;
	}

	Value& conditionsArray = content["Conditions"];

	pCfg->GrounpCounter = conditionsArray.Size();
	for (SizeType i = 0; i < conditionsArray.Size(); ++i) {
		Value& condition = conditionsArray[i];
		if (!condition.IsObject()) {
			std::cerr << "Invalid JSON format: 'Conditions' array element is not an object." << std::endl;
			errorInfo.ErrCode = 24501;
			ErrorInfoPack(&errorInfo, "SeqItem If Load", "");
			return errorInfo;
		}

		// 获取条件中的字段值
		if (!condition.HasMember("ItemsType") || !condition["ItemsType"].IsString() ||
			!condition.HasMember("OpType") || !condition["OpType"].IsString() || !condition.HasMember("Var1") || !condition.HasMember("Var2")) {
			std::cerr << "Invalid JSON format: 'Conditions' array element is missing required fields or they are of invalid types." << std::endl;
			errorInfo.ErrCode = 24501;
			ErrorInfoPack(&errorInfo, "SeqItem If Load：Invalid JSON format-ItemsType OpType ", "");
			return errorInfo;
		}
		// 更新 SeqItem_IfCfg 结构体中的数据
		pCfg->aIfsData[i].BoolOpType = strcmp(condition["ItemsType"].GetString(), "&&") == 0 ? 0 :
			strcmp(condition["ItemsType"].GetString(), "||") == 0 ? 1 : 2;
		pCfg->aIfsData[i].CompareType = strcmp(condition["OpType"].GetString(), "==") == 0 ? 0 :
			strcmp(condition["OpType"].GetString(), "!=") == 0 ? 1 :
			strcmp(condition["OpType"].GetString(), ">=") == 0 ? 2 :
			strcmp(condition["OpType"].GetString(), ">") == 0 ? 3 :
			strcmp(condition["OpType"].GetString(), "<=") == 0 ? 4 : 5;

		//dVar1 dVar2
		errorInfo = LoadDynaVar(&(pCfg->aIfsData[i].dVar1), condition["Var1"]);
		errorInfo = LoadDynaVar(&(pCfg->aIfsData[i].dVar2), condition["Var2"]);

	}

	// 检查是否存在 SetValue 字段，并且是一个整数???这个需要修改下
	if (!content.HasMember("SetValue")) {
		std::cerr << "Invalid JSON format: 'SetValue' field is missing or not an integer." << std::endl;
		errorInfo.ErrCode = 24501;
		ErrorInfoPack(&errorInfo, "SeqItem If Load：Invalid JSON format-SetValue", "");
		return errorInfo;
	}

	//这个得跟上位机说下是否需要bool量
	pCfg->bResult = false;

	//dSetValue
	errorInfo = LoadDynaVar(&(pCfg->dSetValue), content["SetValue"]);


	ErrorInfoPack(&errorInfo, "SeqItem While Load", "");
	return  errorInfo;
}




/* ActionSwitch              //从Json文本中load Motion参数
* @param[in]     char* psActionStr
* @param[in]     RequestsAction* pSwitchedAction
* @return        ErrorInfo
*/
ErrorInfo ActionSwitch(char* psActionStr, RequestsAction* pSwitchedAction)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	// 在此处根据  sTmp的内容来置为
	if (strcmp(psActionStr, "Y") == 0)
	{
		pSwitchedAction->ShowError = true;
	}
	else if (strcmp(psActionStr, "N") == 0)
	{
		pSwitchedAction->ShowError = false;
	}
	else if (strcmp(psActionStr, "GH") == 0)
	{
		pSwitchedAction->GoHome = true;
	}
	else if (strcmp(psActionStr, "ENDSQ") == 0)
	{
		pSwitchedAction->Stop = true;
		pSwitchedAction->Pause = false;
	}
	else if (strcmp(psActionStr, "PAUSE") == 0)
	{
		pSwitchedAction->Pause = true;
		pSwitchedAction->Stop = false;
	}
	else if (strcmp(psActionStr, "PWOFF") == 0)
	{
		pSwitchedAction->PowerDown = true;
	}
	else
	{
		errorInfo.ErrCode = 24060;
		errorInfo.eErrLever = Error;
	}

	ErrorInfoPack(&errorInfo, "ActionSwitch", "ActionStr:%s", psActionStr);
	return errorInfo;
}



/* RequestsActionSwitch              //将请求动作的Json字符串，转换成对应的bool 动作
* @param[in]     psActionStr
* @param[in]     pSwitchedAction
* @return        ErrorInfo
*/
ErrorInfo  RequestsActionSwitch(char* psActionStr, RequestsAction* pSwitchedAction)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	str64 sTmp;
	str16 sTmpChar;
	str16 sInterval = "-";
	uint uActionStrLen = 0;
	uint aSeparateIndex[3];     //分隔符的位置  最多只有4个动作   报错、暂停/停止、回原点、下电  只需要3个分隔符号
	uint uSeparateCounter = 0;
	for (uint i = 0; i < 3; i++)
	{
		aSeparateIndex[i] = 0;
	}

	pSwitchedAction->GoHome = false;
	pSwitchedAction->PowerDown = false;
	pSwitchedAction->Stop = false;
	pSwitchedAction->Pause = false;
	pSwitchedAction->ShowError = false;

	uActionStrLen = strlen(psActionStr);
	if (uActionStrLen > 0)
	{
		for (uint i = 0; i < uActionStrLen; i++)
		{
			memset(&sTmpChar, 0, sizeof(str16));
			memcpy(&sTmpChar, psActionStr + i, 1);
			if (strcmp(sTmpChar, "-") == 0)
			{
				if (uSeparateCounter <= 3)
				{
					aSeparateIndex[uSeparateCounter] = i;
					uSeparateCounter++;
				}
				else
				{
					errorInfo.ErrCode = 24061;
					errorInfo.eErrLever = Error;
					break;
				}
			}
		}

		if (uSeparateCounter > 0)
		{
			for (uint k = 0; k <= uSeparateCounter; k++)
			{
				memset(&sTmp, 0, 64);
				if (k == 0)
					memcpy(&sTmp, psActionStr, aSeparateIndex[k]);
				else if (k < uSeparateCounter)
					memcpy(&sTmp, psActionStr + aSeparateIndex[k - 1] + 1, aSeparateIndex[k] - aSeparateIndex[k - 1] - 1);
				else if (k == uSeparateCounter)
					memcpy(&sTmp, psActionStr + aSeparateIndex[k - 1] + 1, uActionStrLen - aSeparateIndex[k - 1] - 1);

				errorInfo = ActionSwitch(sTmp, pSwitchedAction);
				if (errorInfo.ErrCode)
					break;
			}
		}
		else
		{
			//只有一个字符，也需要判断一下
			errorInfo = ActionSwitch(psActionStr, pSwitchedAction);
		}
	}
	else
	{
		errorInfo.ErrCode = 24062;
		errorInfo.eErrLever = Error;
	}

	ErrorInfoPack(&errorInfo, "RequestsActionSwitch", "");
	return errorInfo;
}


/* ResolverAdvancedSet              //从Json文本中load 高级设置参数
* @param[in]     jFileInfo
* @return        ErrorCode
*/
ErrorInfo ResolverAdvancedSet(char* psAdvancedSet, SeqRef* pOriginSeqRef)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	uint IndexAxisMcs = 0;
	str64 tmpStr;

	rapidjson::Document docAdvancedSet;
	docAdvancedSet.Parse(psAdvancedSet);

	if (docAdvancedSet.IsObject() && docAdvancedSet.HasMember("AxisMcsPara") && docAdvancedSet["AxisMcsPara"].IsArray())
	{
		Value& jAxisMcs = docAdvancedSet["AxisMcsPara"];
		for (auto& jAxisMc : jAxisMcs.GetArray())
		{
			if (jAxisMc.IsNull())
			{
				errorInfo.ErrCode = 24325;
				errorInfo.eErrLever = Error;
				break;
			}
			else
			{
				if (IndexAxisMcs < MaxBranchNum)
				{
					if (!SYSTEM_MOVE_SERIES)
					{
						if (jAxisMc.HasMember("LimitPara"))
						{
							Value& jLimitPara = jAxisMc["LimitPara"];
							if (jLimitPara.HasMember("bActive") && jLimitPara["bActive"].IsBool() &&
								jLimitPara.HasMember("PosLimit") && jLimitPara.HasMember("ForceLimit"))
							{
								sLimitEo.bActive = jLimitPara["bActive"].GetBool();
								if (jLimitPara["PosLimit"].IsFloat())
								{
									sLimitEo.fLimitPos = jLimitPara["PosLimit"].GetFloat();
								}
								else if (jLimitPara["PosLimit"].IsInt())
								{
									sLimitEo.fLimitPos = (float32)jLimitPara["PosLimit"].GetInt();
								}
								else
								{
									errorInfo.ErrCode = 24351;
									errorInfo.eErrLever = Error;
								}

								if (jLimitPara["ForceLimit"].IsFloat())
								{
									sLimitEo.fLimitForce = jLimitPara["ForceLimit"].GetFloat();
								}
								else if (jLimitPara["ForceLimit"].IsInt())
								{
									sLimitEo.fLimitForce = jLimitPara["ForceLimit"].GetInt();
								}
								else
								{
									errorInfo.ErrCode = 24352;
									errorInfo.eErrLever = Error;
								}
								//printf("LimitPara bActive:%s fLimitPos:%f fLimitForce:%f\n",
								//    pOriginSeqRef->sLimitEo.bActive ? "true" : "false",
								//    pOriginSeqRef->sLimitEo.fLimitPos,
								//    pOriginSeqRef->sLimitEo.fLimitForce);
							}
							else
							{
								sLimitEo.bActive = false;
								errorInfo.ErrCode = 24350;
								errorInfo.eErrLever = Error;
							}
						}
					}

					if (!errorInfo.ErrCode)
					{
						if (jAxisMc.HasMember("ErrorHandle"))
						{
							Value& jErrorHandle = jAxisMc["ErrorHandle"];
							if (jErrorHandle.HasMember("PosOverLimit") &&
								jErrorHandle.HasMember("ForceOverLimit") &&
								jErrorHandle.HasMember("TimeOut") &&
								jErrorHandle.HasMember("EOTrig"))
							{
								memset(&tmpStr, 0, 64);
								strcpy(tmpStr, jErrorHandle.HasMember("PosOverLimit") ? jErrorHandle["PosOverLimit"].GetString() : "");
								errorInfo = RequestsActionSwitch(tmpStr, &pOriginSeqRef->vpBranch[IndexAxisMcs]->sSqErrAction.PosOverLimitAction);
								if (!errorInfo.ErrCode)
								{
									memset(&tmpStr, 0, 64);
									strcpy(tmpStr, jErrorHandle.HasMember("ForceOverLimit") ? jErrorHandle["ForceOverLimit"].GetString() : "");
									errorInfo = RequestsActionSwitch(tmpStr, &pOriginSeqRef->vpBranch[IndexAxisMcs]->sSqErrAction.ForceOverLimitAction);
									if (errorInfo.ErrCode)
									{
										break;
									}
								}
								else
								{
									break;
								}

								if (!errorInfo.ErrCode)
								{
									memset(&tmpStr, 0, 64);
									strcpy(tmpStr, jErrorHandle.HasMember("TimeOut") ? jErrorHandle["TimeOut"].GetString() : "");
									errorInfo = RequestsActionSwitch(tmpStr, &pOriginSeqRef->vpBranch[IndexAxisMcs]->sSqErrAction.TimeOutAction);
									if (errorInfo.ErrCode)
										break;
								}
								else
								{
									break;
								}

								if (!errorInfo.ErrCode)
								{
									memset(&tmpStr, 0, 64);
									strcpy(tmpStr, jErrorHandle.HasMember("EOTrig") ? jErrorHandle["EOTrig"].GetString() : "");
									errorInfo = RequestsActionSwitch(tmpStr, &pOriginSeqRef->vpBranch[IndexAxisMcs]->sSqErrAction.EOTrigAction);

									//dbgPrint(1, 1, "EO Action Pause:%s Stop:%s GoHome:%s PowerDown:%s ShowError:%s\n",
									//    pOriginSeqRef->vpBranch[IndexAxisMcs]->sSqErrAction.EOTrigAction.Pause ? "true" : "false",
									//    pOriginSeqRef->vpBranch[IndexAxisMcs]->sSqErrAction.EOTrigAction.Stop ? "true" : "false",
									//    pOriginSeqRef->vpBranch[IndexAxisMcs]->sSqErrAction.EOTrigAction.GoHome ? "true" : "false",
									//    pOriginSeqRef->vpBranch[IndexAxisMcs]->sSqErrAction.EOTrigAction.PowerDown ? "true" : "false",
									//    pOriginSeqRef->vpBranch[IndexAxisMcs]->sSqErrAction.EOTrigAction.ShowError ? "true" : "false");

									if (errorInfo.ErrCode)
										break;
								}
								else
								{
									break;
								}
							}
							else
							{
								errorInfo.ErrCode = 24063;
								errorInfo.eErrLever = Error;
								break;
							}
						}
					}
					else
					{
						break;
					}

					if (!errorInfo.ErrCode && jAxisMc.HasMember("MotionPara"))
					{
						Value& jMotionPara = jAxisMc["MotionPara"];
						if (jMotionPara.HasMember("ForceLoop") && jMotionPara["ForceLoop"].IsObject())
						{
							Value& jForceLoop = jMotionPara["ForceLoop"];
							if (jForceLoop.HasMember("Kp") &&
								jForceLoop.HasMember("Ki") &&
								jForceLoop.HasMember("TrigRate"))
							{
								if (jForceLoop["Kp"].IsFloat())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.ForceLoop.Kp = jForceLoop["Kp"].GetFloat();
								else  if (jForceLoop["Kp"].IsInt())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.ForceLoop.Kp = (float32)jForceLoop["Kp"].GetInt();

								if (jForceLoop["Ki"].IsFloat())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.ForceLoop.Ki = jForceLoop["Ki"].GetFloat();
								else  if (jForceLoop["Ki"].IsInt())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.ForceLoop.Ki = (float32)jForceLoop["Ki"].GetInt();

								if (jForceLoop["TrigRate"].IsFloat())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.ForceLoop.TrigRate = jForceLoop["TrigRate"].GetFloat();
								else  if (jForceLoop["TrigRate"].IsInt())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.ForceLoop.TrigRate = (float32)jForceLoop["TrigRate"].GetInt();
							}
							else
							{
								errorInfo.ErrCode = 24064;
								errorInfo.eErrLever = Error;
							}
						}

						if (jMotionPara.HasMember("PosLoop") && jMotionPara["PosLoop"].IsObject())
						{
							Value& jPosLoop = jMotionPara["PosLoop"];
							if (jPosLoop.HasMember("Kf") &&
								jPosLoop.HasMember("Ki") &&
								jPosLoop.HasMember("Kp") &&
								jPosLoop.HasMember("MinRegSpeed") &&
								jPosLoop.HasMember("MinRegTime") &&
								jPosLoop.HasMember("Offset") &&
								jPosLoop.HasMember("TrigRate"))
							{
								if (jPosLoop["TrigRate"].IsFloat())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.TrigRate = jPosLoop["TrigRate"].GetFloat();
								else if (jPosLoop["TrigRate"].IsInt())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.TrigRate = (float32)jPosLoop["TrigRate"].GetInt();

								if (jPosLoop["Kf"].IsFloat())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.Kf = jPosLoop["Kf"].GetFloat();
								else if (jPosLoop["Kf"].IsInt())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.Kf = (float32)jPosLoop["Kf"].GetInt();

								if (jPosLoop["Ki"].IsFloat())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.Ki = jPosLoop["Ki"].GetFloat();
								else if (jPosLoop["Ki"].IsInt())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.Ki = (float32)jPosLoop["Ki"].GetInt();

								if (jPosLoop["Kp"].IsFloat())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.Kp = jPosLoop["Kp"].GetFloat();
								else if (jPosLoop["Kp"].IsInt())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.Kp = (float32)jPosLoop["Kp"].GetInt();

								if (jPosLoop["MinRegSpeed"].IsFloat())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.MinRegSpeed = jPosLoop["MinRegSpeed"].GetFloat();
								else if (jPosLoop["MinRegSpeed"].IsInt())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.MinRegSpeed = (float32)jPosLoop["MinRegSpeed"].GetInt();

								if (jPosLoop["MinRegTime"].IsFloat())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.MinRegTime = jPosLoop["MinRegTime"].GetFloat();
								else if (jPosLoop["MinRegTime"].IsInt())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.MinRegTime = (float32)jPosLoop["MinRegTime"].GetInt();

								if (jPosLoop["Offset"].IsFloat())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.Offset = jPosLoop["Offset"].GetFloat();
								else if (jPosLoop["Offset"].IsInt())
									pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.PosLoop.Offset = (float32)jPosLoop["Offset"].GetInt();
							}
							else
							{
								errorInfo.ErrCode = 24065;
								errorInfo.eErrLever = Error;
							}
						}

						if (jMotionPara.HasMember("MotionAcc") &&
							jMotionPara.HasMember("HomeAcc"))
						{
							if (jMotionPara["MotionAcc"].IsFloat())
								pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.MotionAcc = jMotionPara["MotionAcc"].GetFloat();
							else if (jMotionPara["MotionAcc"].IsInt())
								pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.MotionAcc = (float32)jMotionPara["MotionAcc"].GetInt();

							if (jMotionPara["HomeAcc"].IsFloat())
								pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.HomeAcc = jMotionPara["HomeAcc"].GetFloat();
							else if (jMotionPara["HomeAcc"].IsInt())
								pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.HomeAcc = (float32)jMotionPara["HomeAcc"].GetInt();

							if (pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.HomeAcc > pSysShareData->sSysFbkVar.fAccMax)
							{
								errorInfo.ErrCode = 24130;
								errorInfo.eErrLever = Error;
							}

							if (!errorInfo.ErrCode && pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.HomeAcc < pSysShareData->sSysFbkVar.fAccMin)
							{
								errorInfo.ErrCode = 24131;
								errorInfo.eErrLever = Error;
							}

							if (!errorInfo.ErrCode && pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.MotionAcc > pSysShareData->sSysFbkVar.fAccMax)
							{
								errorInfo.ErrCode = 24132;
								errorInfo.eErrLever = Error;
							}

							if (!errorInfo.ErrCode && pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.MotionAcc < pSysShareData->sSysFbkVar.fAccMin)
							{
								errorInfo.ErrCode = 24133;
								errorInfo.eErrLever = Error;
							}
						}
						else
						{
							errorInfo.ErrCode = 24066;
							errorInfo.eErrLever = Error;
						}

						if (jMotionPara.HasMember("ForceRate"))
						{
							if (jMotionPara["ForceRate"].IsFloat())
								pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.ForceRate = jMotionPara["ForceRate"].GetFloat();
							else if (jMotionPara["ForceRate"].IsInt())
								pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.ForceRate = (float32)jMotionPara["ForceRate"].GetInt();
						}
						else
						{
							errorInfo.ErrCode = 24067;
							errorInfo.eErrLever = Error;
						}

						if (jMotionPara.HasMember("SlopeRate"))
						{
							if (jMotionPara["SlopeRate"].IsFloat())
								pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.SlopeRate = jMotionPara["SlopeRate"].GetFloat();
							else if (jMotionPara["SlopeRate"].IsInt())
								pOriginSeqRef->vpBranch[IndexAxisMcs]->sBranchMotionPara.SlopeRate = (float32)jMotionPara["SlopeRate"].GetInt();
						}
						else
						{
							errorInfo.ErrCode = 24068;
							errorInfo.eErrLever = Error;
						}
					}
					IndexAxisMcs++;
				}
				else
				{

					errorInfo.ErrCode = 24069;
					errorInfo.eErrLever = Error;
					break;
				}
			}
		}
	}

	if (docAdvancedSet.IsObject() && docAdvancedSet.HasMember("Other") && docAdvancedSet["Other"].IsObject())
	{
		Value& jOtherPara = docAdvancedSet["Other"];
		if (jOtherPara.HasMember("AutoLocalDown") && jOtherPara["AutoLocalDown"].IsBool())
			pSysShareData->bAutoResetLocalVar = jOtherPara["AutoLocalDown"].GetBool();

		//dbgPrint(1, 1, "Resolver Local bAutoReset:%s\n", pSysShareData->bAutoResetLocalVar ==true?"true":"false");

		if (jOtherPara.HasMember("ChartConfig") && jOtherPara["ChartConfig"].IsArray())
		{
			Value& jAxisMcGroups = jOtherPara["ChartConfig"];
			uint uAxisCount = 0;
			for (auto& jAxisMc : jAxisMcGroups.GetArray())
			{
				if (jAxisMc.IsNull())
				{
				}
				else
				{
					uAxisCount++;
				}
			}
		}

		if (SYSTEM_MOTION_MODE)
		{
			if (jOtherPara.HasMember("NoHomeStart") && jOtherPara["NoHomeStart"].IsBool())
			{
				pOriginSeqRef->sAdvancePara.bNoHomeRunSeq = jOtherPara["NoHomeStart"].GetBool();
				//   dbgPrint(1, 1, "NoHomeStart:%s\n", pOriginSeqRef->sAdvancePara.bNoHomeRunSeq == true ? "true" : "false");
			}
			else
			{
				errorInfo.ErrCode = 24070;
				errorInfo.eErrLever = Error;
			}
		}
	}

	ErrorInfoPack(&errorInfo, "ResolverAdvancedSet", "");
	return errorInfo;
}


/* ResolverSequence              //从Json文本中load Motion参数
* @param[in]     SeqRef* pTmpSeqRef
* @param[in]     SeqRef* pOriginSeqRef
* @param[in]     Value& jSeq
* @param[in]     sAxisMcPara* pAxisMc
* @param[in]     int AxisMcNum
* @return        ErrorInfo
*/
bool bSeqUsedGlobalVar = false;
ErrorInfo ResolverSequence(SeqRef* pTmpSeqRef, SeqRef* pOriginSeqRef, char* psSeq, sAxisMcPara* pAxisMc, int AxisMcNum)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem* pSeqItem;

	int iItemIndex = 0;
	int branchIndex = 0;
	int startNode = 0, endNode = 0;
	char tmpStr[256];
	int iErrCode, iAction;
	SeqItem_MotionCfg* pMotion;

	memset(pTmpSeqRef, 0, sizeof(SeqRef));
	pTmpSeqRef->pAxisMc = pOriginSeqRef->pAxisMc;
	sLimitParameter  LimitParameter;
	LimitParameter.MaxTorque = pTmpSeqRef->LimitParameter.MaxTorque;
	LimitParameter.MaxVelocity = pTmpSeqRef->LimitParameter.MaxVelocity;

	rapidjson::Document docSeq;
	docSeq.Parse(psSeq);
	bSeqUsedGlobalVar = false;
	if (docSeq.IsObject() && docSeq.HasMember("Branchs"))
	{
		for (auto it = docSeq.MemberBegin(); it != docSeq.MemberEnd(); it++)
		{
			;// dbgPrint(1, 1, "[%s]\n", it->name.GetString());
		}

		Value& jBranchs = docSeq["Branchs"];
		if (jBranchs.IsArray())
		{
			branchIndex = 0;
			int maxNodeNum = 0;
			maxNodeNum++;
			for (int i = 0; i < maxNodeNum; i++)
			{
				sSeqRef_Node* pSeqNode = (sSeqRef_Node*)malloc(sizeof(sSeqRef_Node));
				memset(pSeqNode, 0, sizeof(sSeqRef_Node));

				pTmpSeqRef->vpNode[pTmpSeqRef->nNodeNum] = pSeqNode;
				pTmpSeqRef->nNodeNum++;
			}

			for (auto& jBranch : jBranchs.GetArray())
			{
				//pBranch->pSeqRef = pTmpSeqRef;
				if (jBranch.IsNull())
				{
					errorInfo.ErrCode = 24324;
					errorInfo.eErrLever = Error;
					break;
				}
				else
				{
					_SeqItem* pLastSeqItem = 0;
					SeqItem* pTmpSeqItem = 0;
					int deltaIndentLevel = 0;
					startNode = 0;
					endNode = +1;

					//Node 的用法持保持，先搁置
					//sSeqRef_Node* pNode = pTmpSeqRef->vpNode[startNode];
					//pNode->vOutBranchList[pNode->nOutBranchNum++] = branchIndex;
					//pNode = pTmpSeqRef->vpNode[endNode];
					//pNode->vInBranchList[pNode->nInBranchNum++] = branchIndex;

					sSeqRef_Branch* pBranch = (sSeqRef_Branch*)malloc(sizeof(sSeqRef_Branch));
					memset(pBranch, 0, sizeof(sSeqRef_Branch));

					//  dbgPrint(1, 1, "New Count:%d  pBranch:%u\n", pTmpSeqRef->nBranchNum, pBranch);
					pTmpSeqRef->vpBranch[pTmpSeqRef->nBranchNum] = pBranch;
					pTmpSeqRef->nBranchNum++;

					//pBranch->pSeqRef = pTmpSeqRef;
					if (jBranch.HasMember("SeqItems"))
					{
						Value& jSeqItems = jBranch["SeqItems"];
						for (auto& jSeqItem : jSeqItems.GetArray())
						{
							pSeqItem = 0;
							errorInfo.ErrCode = 0;

							int level = jSeqItem.HasMember("Level") && jSeqItem["Level"].IsInt() ? jSeqItem["Level"].GetInt() : -1;

							memset(&tmpStr, 0, sizeof(tmpStr));
							strcpy(tmpStr, jSeqItem.HasMember("Type") ? jSeqItem["Type"].GetString() : "");

							//rt_dbgPrint(1, 0, "\nSeqItems:%s\n", tmpStr);
							if (strcasecmp(tmpStr, "If") == 0)                      // 0x00:If
							{
								pSeqItem = SeqItem_If_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, "If");
								strcpy(pSeqItem->szName, jSeqItem.HasMember("Name") ? jSeqItem["Name"].GetString() : "");
								Value& jIfItem = jSeqItem["Content"];
								errorInfo = SeqItem_If_Load(pSeqItem, jIfItem);
							}
							else if (strcasecmp(tmpStr, "ElseIf") == 0)              //0x01:ElesIf
							{
								pSeqItem = SeqItem_ElseIf_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, "ElseIf");
								strcpy(pSeqItem->szName, jSeqItem.HasMember("Name") ? jSeqItem["Name"].GetString() : "");
								Value& jIfItem = jSeqItem["Content"];
								errorInfo = SeqItem_If_Load(pSeqItem, jIfItem);     //这个是否就可以直接是一个IFLoad??
							}
							else if (strcasecmp(tmpStr, "Else") == 0)               //0x02:Else
							{
								pSeqItem = SeqItem_Else_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, "Else");
								strcpy(pSeqItem->szName, jSeqItem.HasMember("Name") ? jSeqItem["Name"].GetString() : "");
							}
							else if (strcasecmp(tmpStr, "Label") == 0)              //0x03:Label
							{
								pSeqItem = SeqItem_Label_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, "Label");
								strcpy(pSeqItem->szName, jSeqItem.HasMember("Name") ? jSeqItem["Name"].GetString() : "");
								Value& jLabel = jSeqItem["Content"];
								errorInfo = SeqItem_Label_Load(pSeqItem, jLabel);
							}
							else if (strcasecmp(tmpStr, "Jump") == 0)               //0x10:Jump
							{
								pSeqItem = SeqItem_Jump_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, "Jump");
								strcpy(pSeqItem->szName, jSeqItem.HasMember("Name") ? jSeqItem["Name"].GetString() : "");
								Value& jJumpItem = jSeqItem["Content"];
								errorInfo = SeqItem_Jump_Load(pSeqItem, jJumpItem);
							}
							else if (strcasecmp(tmpStr, "While") == 0)
							{
								pSeqItem = SeqItem_While_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, "While");
								strcpy(pSeqItem->szName, jSeqItem.HasMember("ItemType") ? jSeqItem["ItemType"].GetString() : "");
								Value& jWhileItem = jSeqItem["Content"];
								errorInfo = SeqItem_While_Load(pSeqItem, jWhileItem);
							}
							else if (strcasecmp(tmpStr, "Continue") == 0)
							{
								pSeqItem = SeqItem_Continue_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, "Continue");
								strcpy(pSeqItem->szName, jSeqItem.HasMember("ItemType") ? jSeqItem["ItemType"].GetString() : "");
							}
							else if (strcasecmp(tmpStr, "Break") == 0)
							{
								pSeqItem = SeqItem_Break_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, "Break");
								strcpy(pSeqItem->szName, jSeqItem.HasMember("ItemType") ? jSeqItem["ItemType"].GetString() : "");
							}
							else if (strcasecmp(tmpStr, "MeasureStart") == 0)   //0x11:Measurement Start
							{
								pSeqItem = SeqItem_MeasurementStart_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, "MeasureStart");
								if (jSeqItem.HasMember("Content") && jSeqItem["Content"].IsObject())
								{
									Value& jMeasureStar = jSeqItem["Content"];
									errorInfo = SeqItem_MeasureStar_Load(pSeqItem, jMeasureStar);
								}
								else
								{
									SeqItem_MeasurementStart* pCfg = (SeqItem_MeasurementStart*)(pSeqItem->pCfg);
									pCfg->eMeasureMode = Auto;
								}
							}
							else if (strcasecmp(tmpStr, "MeasureStop") == 0)    //0x12:Measurement Stop
							{
								pSeqItem = SeqItem_MeasurementStop_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, "MeasureStop");
								if (jSeqItem.HasMember("Content") && jSeqItem["Content"].IsObject())
								{
									Value& jMeasureStar = jSeqItem["Content"];
									errorInfo = SeqItem_MeasureStop_Load(pSeqItem, jMeasureStar);
								}
								else
								{
									SeqItem_MeasurementStop* pCfg = (SeqItem_MeasurementStop*)(pSeqItem->pCfg);
									pCfg->eMeasureMode = Auto;
								}
							}
							else if (strcasecmp(tmpStr, "SetValue") == 0)           //0x13:SetValue
							{
								pSeqItem = SeqItem_SetValue_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, tmpStr);
								strcpy(pSeqItem->szName, jSeqItem.HasMember("Name") ? jSeqItem["Name"].GetString() : "");
								Value& jSetValItem = jSeqItem["Content"];
								errorInfo = SeqItem_SetValue_Load(pSeqItem, jSetValItem);
							}
							else if (strcasecmp(tmpStr, "WaitConstTime") == 0 ||
								strcasecmp(tmpStr, "WaitNotify") == 0 ||
								strcasecmp(tmpStr, "WaitForMaster") == 0)             //0x14:Wait
							{
								pSeqItem = SeqItem_Wait_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, tmpStr);
								strcpy(pSeqItem->szName, jSeqItem.HasMember("Name") ? jSeqItem["Name"].GetString() : "");

								Value& jWaitItem = jSeqItem["Content"];
								errorInfo = SeqItem_Wait_Load(pSeqItem, jWaitItem);
							}
							else if (strcasecmp(tmpStr, "PosCntrl") == 0 ||
								strcasecmp(tmpStr, "ForceTrigCntrl") == 0 ||
								strcasecmp(tmpStr, "PosOrForceTrig") == 0 ||
								strcasecmp(tmpStr, "ForceLoopCntrl") == 0 ||
								strcasecmp(tmpStr, "SlopeTrigCntrl") == 0)
							{
								pSeqItem = SeqItem_Motion_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, tmpStr);

								Value& jMotionItem = jSeqItem["Content"];
								errorInfo = SeqItem_Motion_Load(pSeqItem, jMotionItem, LimitParameter);
							}
							else if (strcasecmp(tmpStr, "GoHome") == 0)
							{
								pSeqItem = SeqItem_Motion_New(pOriginSeqRef, pTmpSeqRef);
								strcpy(pSeqItem->szType, tmpStr);
								Value& jGoHomePara = jSeqItem["Content"];
								errorInfo = SeqItem_Motion_GoHome_Load(pSeqItem, jGoHomePara, LimitParameter);
							}
							else
							{
								errorInfo.ErrCode = 24053;
								errorInfo.eErrLever = Error;
								break;
							}

							if (!jSeqItem.HasMember("Level") || !jSeqItem["Level"].IsInt())
							{
								;//error
							}
							else
							{
								pSeqItem->iLevel = jSeqItem["Level"].GetInt();
							}

							if (pSeqItem)
							{
								pSeqItem->iBranchIndex = branchIndex;
							}
							else
								dbgPrint(1, 0, "%24s]\n", "");

							if (errorInfo.ErrCode)
							{
								dbgPrint(1, 0, "*******Load Seqeunce Para ErrCode:%d\n", errorInfo.ErrCode);
								goto Clean;
							}
							else
							{
								if (pBranch->pFirstItem == 0)
								{
									pBranch->pFirstItem = pSeqItem;
								}
								if (pLastSeqItem)
								{
									//add Q
									deltaIndentLevel = pSeqItem->iLevel - pLastSeqItem->iLevel;

									switch (deltaIndentLevel) {
									case 0:
										pSeqItem->pPrevious = pLastSeqItem;
										pSeqItem->pParent = pSeqItem->pPrevious->pParent;
										pSeqItem->pPrevious->pNext = pSeqItem;
										break;
									case 1:
										pLastSeqItem->pChild = pSeqItem;
										pSeqItem->pParent = pLastSeqItem;
										break;
									default:
										if (deltaIndentLevel < 0) {
											pTmpSeqItem = pLastSeqItem;
											for (int i = 0; i < abs(deltaIndentLevel); i++)
											{
												pTmpSeqItem = pTmpSeqItem->pParent;
											}
											pSeqItem->pPrevious = pTmpSeqItem;
											pSeqItem->pParent = pSeqItem->pPrevious->pParent;
											pSeqItem->pPrevious->pNext = pSeqItem;
											break;
										}
										else if (deltaIndentLevel > 1) {
											errorInfo.ErrCode = 24054;
											errorInfo.eErrLever = Error;
										}
										break;
									}

									if (errorInfo.ErrCode)
									{
										goto Clean;
									}
								}
								pLastSeqItem = pSeqItem;
								iItemIndex++;
								if (strcasecmp(tmpStr, "GoHome") == 0)
								{
									pSeqItem->iRowIndex = 0;
								}
								else
								{
									if (pSeqItem != NULL)
										pSeqItem->iRowIndex = iItemIndex;
								}

								pBranch->nRowCount++;
								//dbgPrint(1, 2, "nRowCount:%d\n", pBranch->nRowCount);
							}
						}
					}
					else
					{
						dbgPrint(1, 1, "SeqItems NULL \n");
						errorInfo.ErrCode = 24055;
						errorInfo.eErrLever = Error;
						break;
					}

					if (errorInfo.ErrCode)
					{
						dbgPrint(1, 0, "*******Load Seqeunce Para ErrCode:%d\n", errorInfo.ErrCode);
						goto Clean;
					}
					else
					{
						branchIndex++;
						if (pTmpSeqRef->nBranchNum > MaxBranchNum)
						{
							errorInfo.ErrCode = 24500;
							errorInfo.eErrLever = Error;
							break;
						}
					}
				}
			}
			if (branchIndex == 0)
			{
				errorInfo.ErrCode = 24056;
				errorInfo.eErrLever = Error;
			}
		}
		else
		{
			errorInfo.ErrCode = 24057;
			errorInfo.eErrLever = Error;
		}
	}
	else
	{
		errorInfo.ErrCode = 24058;
		errorInfo.eErrLever = Error;
	}

	pSysShareData->SeqAux.iErrorRowIndex = -1;
	for (uint8 IndexBranch = 0; IndexBranch < pTmpSeqRef->nBranchNum; IndexBranch++)
	{
		//解析完工艺后  检查参数   后续挪到   实时中，当工艺切换完成后再检查
		errorInfo = BranchItemParaCheck(pTmpSeqRef->vpBranch[IndexBranch]);
		if (errorInfo.ErrCode)
		{
			pSysShareData->SeqAux.iErrorRowIndex = pTmpSeqRef->vpBranch[IndexBranch]->iActiveRowIndex;
			break;
		}
	}

	if (errorInfo.ErrCode)
	{
		goto Clean;
	}
	else
	{
		// copy前应该将原始Sequence的数据释放掉; 不然会照成内存泄露
		Seq_Free(pOriginSeqRef);

		pSysShareData->SeqAux.iActiveRowIndex = -1;
		memcpy(pOriginSeqRef, pTmpSeqRef, sizeof(SeqRef));

		pSysShareData->sSysJogPara.fHomePos = fHomePos;
		//  dbgPrint(1, 0, "fHomePos:%f\n", fHomePos);
		ErrorInfoPack(&errorInfo, "ResolverSequence", "");
	}
	return  errorInfo;

Clean:
	SeqRelease(pTmpSeqRef);

	ErrorInfoPack(&errorInfo, "ResolverSequence", "");
	return  errorInfo;
}







