﻿#include "alarmhandler.h"
#include "responsejson.h"
#include "nlohmann/json.hpp"

ErrorInfo errorInfoapi;


AlarmHandler::AlarmHandler(DBI* _dbi) : ApiHandler(_dbi)
{
	initSql();
	tableCheck();
}

AlarmHandler::~AlarmHandler()
{
}

void AlarmHandler::tableCheck()
{
	std::string	sql = R"( CREATE TABLE IF NOT EXISTS AlarmRecord( Id INTEGER UNIQUE PRIMARY KEY AUTOINCREMENT, AlarmCode VARCHAR (16) NOT NULL, UpdateTime DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') )NOT NULL,Brief VARCHAR(256));
                          CREATE TABLE IF NOT EXISTS AlarmFatalRecord( Id INTEGER UNIQUE PRIMARY KEY AUTOINCREMENT, AlarmCode VARCHAR (16) NOT NULL, UpdateTime DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') )NOT NULL,Brief VARCHAR(256),Fatalerrorinformation JSON);
              CREATE TABLE IF NOT EXISTS AlarmDefine( Id INTEGER UNIQUE PRIMARY KEY AUTOINCREMENT, Ver VARCHAR (16), Level VARCHAR (32) , AlarmCode VARCHAR (16) NOT NULL, Advice VARCHAR(128), UpdateTime DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') ),BriefCN VARCHAR(256),BriefEN VARCHAR(256));)";
	std::string	sql1 = R"(CREATE TRIGGER IF NOT EXISTS limit_alarmrecord_rows AFTER INSERT ON AlarmRecord BEGIN DELETE FROM AlarmRecord WHERE rowid <= new.rowid - 100000; END; )";
	auto rc = dbi->exec(sql, NULL, NULL);
	if (rc != SQLITE_OK)
	{
		std::cerr << "创建报警记录配置表失败,错误码 " << rc << std::endl;
	}
	else {
		rc = dbi->exec(sql1, NULL, NULL);
		if (rc != SQLITE_OK) {
			std::cerr << "创建报警记录触发器失败,错误码 " << rc << std::endl;
		}
	}
}
void AlarmHandler::initSql()
{
	sqlQuery = "select r.Id, r.AlarmCode,d.Level,d.BriefCN,d.BriefEN,d.Advice ,r.UpdateTime from AlarmRecord as r left join AlarmDefine as d on r.AlarmCode=d.AlarmCode where r.alarmcode like ? Order by r.Id DESC limit 100 ";
	sqlQueryfataldes = "select r.Id, r.AlarmCode,d.Level,d.BriefCN,d.BriefEN,d.Advice ,r.UpdateTime,r.Fatalerrorinformation from AlarmFatalRecord as r left join AlarmDefine as d on r.AlarmCode=d.AlarmCode where r.alarmcode like ? Order by r.UpdateTime DESC limit 100 ";
}

void AlarmHandler::bindServe(httplib::Server& svr)
{
	svr.Get("/pd/alarm/fatal", std::bind(&AlarmHandler::fataldes, this, std::placeholders::_1, std::placeholders::_2));
	svr.Get("/pd/alarm/info", std::bind(&AlarmHandler::handle, this, std::placeholders::_1, std::placeholders::_2));
	svr.Get("/pd/alarm/codedes", std::bind(&AlarmHandler::GetErrcodedes, this, std::placeholders::_1, std::placeholders::_2));
	svr.Post("/pd/alarm/rm", (httplib::Server::Handler)std::bind(&AlarmHandler::delAlarmHandle, this, std::placeholders::_1, std::placeholders::_2));
}

void AlarmHandler::fataldes(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	auto param = req.params.begin(); // 获取一个包含所有key和value的std::multimap
	ResponseJson resJson;
	if (param == req.params.end() || param->first != "code")
	{
		resJson.json(17161, "传入严重报警信息错误");
		res.set_content(resJson.toJson(), "application/json");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17161;
		ErrorInfoPack(&errorInfoapi, "fataldes", "");
		return;
	}
	std::string key = param->first; // 获取第一个key值
	std::string val = param->second;
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare(sqlQueryfataldes, stmt);
	rc += sqlite3_bind_text(stmt, 1, val.c_str(), val.length(), NULL);
	if (rc == SQLITE_OK) {
		// 如果查询成功
		int _cnt = 0; resJson.predata();
		char prefix = '[';
		while (sqlite3_step(stmt) == SQLITE_ROW) {
			std::string p[8];
			for (int i = 0; i < 8; i++)
			{
				auto p0 = sqlite3_column_blob(stmt, i);
				if (p0 == nullptr)
				{
					p[i] = "";
				}
				else
				{
					p[i] = std::string{ (char*)p0 };
				}
			}
			resJson << prefix << R"({"Id":)" << p[0] << R"(,"AlarmCode":")" << p[1] << R"(","Level":")" << p[2] << R"(","BriefCN":")" << p[3] << R"(","BriefEN":")" << p[4] << R"(","Advice":")" << p[5] << R"(","UpdateTime":")" << p[6] << R"(","Fatalerrorinformation":)" << p[7] << "}";
			prefix = ',';
			_cnt++;
		}

		if (_cnt == 0) {
			resJson.json(17162, "严重报警记录列表为空");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17162;
		}
		else
		{
			resJson << "]}";
		}

	}
	else
	{
		resJson.json(17539, "获取严重报警信息失败，SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17539;
	}
	res.set_content(resJson.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, "fataldes", "");
}

void AlarmHandler::handle(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	auto param = req.params.begin(); // 获取一个包含所有key和value的std::multimap
	ResponseJson resJson;
	if (param == req.params.end() || param->first != "code")
	{
		resJson.json(17091, "传入报警信息错误");
		res.set_content(resJson.toJson(), "application/json");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17091;
		ErrorInfoPack(&errorInfoapi, "handle", "");
		return;
	}
	std::string key = param->first; // 获取第一个key值
	std::string val = param->second;
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare(sqlQuery, stmt);
	rc += sqlite3_bind_text(stmt, 1, val.c_str(), val.length(), NULL);
	if (rc == SQLITE_OK) {
		// 如果查询成功
		int _cnt = 0; resJson.predata();
		char prefix = '[';
		while (sqlite3_step(stmt) == SQLITE_ROW) {
			std::string p[7];
			for (int i = 0; i < 7; i++)
			{
				auto p0 = sqlite3_column_blob(stmt, i);
				if (p0 == nullptr)
				{
					p[i] = "";
				}
				else
				{
					p[i] = std::string{ (char*)p0 };
					if (i == 4) {
						p[i].erase(std::remove(p[i].begin(), p[i].end(), '\r'), p[i].end());
					}
				}
			}
			resJson << prefix << R"({"Id":)" << p[0] << R"(,"AlarmCode":")" << p[1] << R"(","Level":")" << p[2] << R"(","BriefCN":")" << p[3] << R"(","BriefEN":")" << p[4] << R"(","Advice":")" << p[5] << R"(","UpdateTime":")" << p[6] << "\"}";
			prefix = ',';
			_cnt++;
		}

		if (_cnt == 0) {
			resJson << prefix << "]}";
			/*resJson.json(17092, "报警记录列表为空");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17092;*/
		}
		else
		{
			resJson << "]}";
		}

	}
	else
	{
		resJson.json(17501, "获取报警信息失败，SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17501;
	}
	res.set_content(resJson.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, "handle", "");
}


void AlarmHandler::delAlarmHandle(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson resJson;
	sqlite3_stmt* stmt = nullptr;
	std::string sql = "DELETE FROM AlarmRecord where Id ";
	nlohmann::json json_req = nlohmann::json::parse(req.body);
	int cnt = json_req["cnt"];

	if (cnt > 0 && json_req["data"].is_array())
	{
		const std::string s = json_req["data"].dump();
		sql += " in (" + s.substr(1, s.length() - 2) + ')';
	}
	else if (cnt == 1 && json_req["data"].is_string())
	{
		sql += " = " + (std::string)json_req["data"];
	}
	else if (cnt == 0)
	{
		sql = "DELETE FROM AlarmRecord";
	}
	else
	{
		resJson.json(17093, "删除报错信息时，传入报警信息错误");
		res.set_content(resJson.toJson(), "application/json");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17093;
		ErrorInfoPack(&errorInfoapi, "delAlarmHandle", "");
		return;
	}
	auto rc = dbi->prepare(sql, stmt);
	if (rc == SQLITE_OK) {
		// 如果查询成功
		if (sqlite3_step(stmt) == SQLITE_DONE && (dbi->affectedRows() == cnt || cnt == 0)) {
			resJson.json(0, "删除报警记录成功,删除数量:" + cnt);
		}
		else {
			resJson.json(17094, "删除报警记录失败");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17094;
		}
	}
	else
	{
		resJson.json(17502, "删除报警信息SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17502;
	}
	res.set_content(resJson.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, "delAlarmHandle", "");
}

void AlarmHandler::GetErrcodedes(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson resJson;
	sqlite3_stmt* stmt = nullptr;
	bool jsonflag = false;
	std::string jsonStr;
	std::string sql = "SELECT Ver,Level,Advice,UpdateTime,BriefCN ,BriefEN FROM AlarmDefine ";
	auto param = req.params.find("filter");
	if (param == req.params.end())
	{
		resJson.json(17095, "获取错误码详细信息失败,传递参数错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17095;
	}
	else {
		sql += param->second;
		//std::cout << sql << std::endl;
		if (sql == "SELECT Ver,Level,Advice,UpdateTime,BriefCN ,BriefEN FROM AlarmDefine where AlarmCode=0") {
			ErrorInfoPack(&errorInfoapi, "GetErrcodedes", "");
			return;
		}
		auto rc = dbi->prepare(sql, stmt);
		if (rc == SQLITE_OK)
		{
			resJson.predata();
			resJson << '{';
			rc = sqlite3_step(stmt);
			if (rc == SQLITE_ROW)
			{
				auto Ver = dbi->dbStr(stmt, 0); //user_name
				auto Level = dbi->dbStr(stmt, 1); //
				auto Advice = dbi->dbStr(stmt, 2); //
				auto UpdateTime = dbi->dbStr(stmt, 3); //
				auto BriefCN = dbi->dbStr(stmt, 4); // 
				auto BriefEN = dbi->dbStr(stmt, 5);
				BriefEN.erase(std::remove(BriefEN.begin(), BriefEN.end(), '\r'), BriefEN.end());
				resJson.fillKeyVal("Ver", Ver) << ',';
				resJson.fillKeyVal("Level", Level) << ',';
				resJson.fillKeyVal("Advice", Advice) << ',';
				resJson.fillKeyVal("UpdateTime", UpdateTime) << ',';
				resJson.fillKeyVal("BriefCN", BriefCN) << ',';
				resJson.fillKeyVal("BriefEN", BriefEN) << '}';
				char prefix = '}';
				resJson << prefix;
				// 自动转换并验证 JSON 是否有效
				jsonStr = resJson.toJson();
				nlohmann::json jsonResponse;
				try {
					jsonResponse = nlohmann::json::parse(jsonStr); // 尝试解析 JSON 字符串
					jsonflag = true; // 如果解析成功，设置标志
				}
				catch (const nlohmann::json::parse_error& e) {
					// 如果解析失败，返回错误信息
					resJson.json(17201, "生成的错误码详细信息JSON格式无效");
					errorInfoapi.eErrLever = Warm;
					errorInfoapi.ErrCode = 17201;
				}
			}
			else
			{
				resJson.json(17096, "执行获取错误码详细信息失败");
				errorInfoapi.eErrLever = Error;
				errorInfoapi.ErrCode = 17096;
			}
		}
		else
		{
			resJson.json(17530, "获取错误码详细信息SQL语法错误");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17530;
		}
	}
	if (jsonflag) {
		res.set_content(jsonStr, "application/json");
	}
	else {
		res.set_content(resJson.toJson(), "application/json");
	}
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, "GetErrcodedes", "");
}

