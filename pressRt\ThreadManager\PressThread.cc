#include "PressThread.h"

#include <pthread.h>
#include <unistd.h>
#include "./core/rttimer.h"
#include "SysVarDefine.h"
#include "FsmOp.h"
#include "SysShareMemoryDefine.h"
#include "SqliteParaRw.h"
#include "Sn/Statistics.h"
#include "Sn/Sn.h"
#include "comm/ServerMain.h"
#include "Set/SetPara.h"
#include "Channel.h"
#include "SysShareMemoryOp.h"
#include "../FSM/FSM_SysPreloadPara.h"
#include "SnMaker.h"
#include "Profile.h"
#include "sqliteapi/WebApi/apistate.h"
#include "FSM_SysSelfCheck.h"
#include "FieldbusThread.h"
#include "EO.h"
#include "EoThread.h"

TimeData	sSaveUserParaTime;
uint64 ulStartTime;
uint64 ulFininshTime;
float64 dTotalSaveTime = 0;
uint32 ulSaveCount = 0;

TrigData sProfileStaRtrig;               //工艺统计数据复位   上升沿触发
TrigData sGlobalStaRtrig;               //工艺统计数据复位   上升沿触发

//TrigData sPointsTrigRtrig;
TrigData sResultSaveRtrig;
TrigData sSetTimeRtrig;
//bool bSavePointsing = false;

TrigData FileServerConnectTest;

bool bFinishLoad = false;
bool bSnBingdingResult = false;

bool bHadTrigSetTime = false;
uint64 uSetOkCounter = 0;

bool bNeedModifyTime = false;
bool bFinishSetTime;

uint64 uLastHmiIpModifyCounter;

void InitDriverPara()
{
	pSysShareData->AxisMc[0].Axis.Cfg.MotorPlusePerRevolution = 8388608;// SysCfg.Motor[i].iMotorPlusePerRevolution;//8388608;
	pSysShareData->AxisMc[0].Axis.Cfg.ShaftPlusePerRevolution = 1000000;// SysCfg.Motor[i].iShaftPlusePerRevolution;//1000000;

	pSysShareData->SeqAux.sMotionAux.fMoveDelayExitTime_ms = 100;
}

/* ExecuteTimeTest              //从数据库中加载设置参数   参数被修改时会触发
* @param[in]     NONE
* @return        NONE
*/
void ExecuteTimeTest()
{
	clock_gettime_nS(&ulStartTime);

	clock_gettime_nS(&ulFininshTime);

	sSaveUserParaTime.CurTime = float(ulFininshTime - ulStartTime) / 1000000;//ms
	dTotalSaveTime += sSaveUserParaTime.CurTime;
	ulSaveCount += 1;
	sSaveUserParaTime.AverageTime = (float32)(dTotalSaveTime / ulSaveCount);

	if (sSaveUserParaTime.CurTime > sSaveUserParaTime.MaxTime)
		sSaveUserParaTime.MaxTime = sSaveUserParaTime.CurTime;

	if (sSaveUserParaTime.MinTime != 0)
	{
		if (sSaveUserParaTime.CurTime < sSaveUserParaTime.MinTime)
			sSaveUserParaTime.MinTime = sSaveUserParaTime.CurTime;
	}
	else
		sSaveUserParaTime.MinTime = sSaveUserParaTime.CurTime;
}

/* SaveResultFunc              //从数据库中加载设置参数   参数被修改时会触发
* @param[in]     NONE
* @return        NONE
*/
void SaveResultFunc()
{
	sResultSaveRtrig.bTrigSignal = psChartsData->bChartEvalFinish;
	R_Trig(&sResultSaveRtrig);
	if (sResultSaveRtrig.bTrig)
	{
		BindingResult(&gSysSnData, &gCurProfileSnData, &psChartsData->bChartEvalOK);
		pSysShareData->bStartSaveResult = true;                    // rt_dbgPrint(1, 0, "bChartEvalOK:%s\n", psChartsData->bChartEvalOK?"OK":"NOK");
	}

	//存储工艺运行结果   曲线点和EO结果
	if (pSysShareData->bStartSaveResult)        //可以实现先存点 再存eo结果
	{
		SaveRunResultToDataBase(&pSysShareData->bStartSaveResult, pSysShareData->sExSW.ActiveMPId, "V1.0.0");
	}
}

/* UpdateDataStateFunc              //统计数据相关功能
* @param[in]     NONE
* @return        NONE
*/
void UpdateDataStateFunc()
{
	FileServerConnectTest.bTrigSignal = bServerConnectTest;
	R_Trig(&FileServerConnectTest);
	if (FileServerConnectTest.bTrig)
	{
		bServerConnectflag = false;
	}
}
/* StatisticsFunc              //统计数据相关功能
* @param[in]     NONE
* @return        NONE
*/
void StatisticsFunc()
{
	if (pSysShareData->sExSW.eSysState == Sys_State_Ready && !pSysShareData->sExSW.bSequenceBusy)
	{
		//重置计数器
		ResetCounter(&gSysSnData, &gCurProfileSnData, pSysShareData->sExSW.ActiveMPId);

		//重置统计数据   工艺
		sProfileStaRtrig.bTrigSignal = sSysStaData.bResetProfleSta;
		R_Trig(&sProfileStaRtrig);
		if (sProfileStaRtrig.bTrig)
		{
			rt_dbgPrint(1, 0, "ResetStatisticsData\n");
			ResetStatisticsData(pSysShareData->sExSW.ActiveMPId);
		}

		//全局统计数据复位   上升沿触发
		sGlobalStaRtrig.bTrigSignal = sSysStaData.bResetGlobalSta;
		R_Trig(&sGlobalStaRtrig);
		if (sGlobalStaRtrig.bTrig)
		{
			rt_dbgPrint(1, 0, "ResetStatisticsData\n");
			ResetStatisticsData(0);
		}
	}
}
/* LoadProtileFunc              //从数据库中加载设置参数   参数被修改时会触发
* @param[in]     NONE
* @return        NONE
*/
void LoadProfileFunc()
{
	if (pSysShareData->bNeedLoadProfile &&
		pSysShareData->sExSW.eSysState != Sys_State_RunSequence
		&& !pSysShareData->sExSW.bSequenceBusy)
	{
		if (pSysShareData->sExSW.eSysState == Sys_State_Ready && pSysShareData->sExSW.bSequenceBusy)
		{
			pSysShareData->bLoadProfile = false;
		}
		else
		{
			pSysShareData->bSwitchRequest = true;
			pSysShareData->bLoadProfile = true;
			iProfileLoadStep = 0;
			bFinishLoad = false;
		}
		pSysShareData->bNeedLoadProfile = false;
	}

	if (pSysShareData->bLoadProfile)
	{
		pSysShareData->PressErrInfo = ExecuteLoadProfile(pSysShareData->uNeedActiveMp, &bFinishLoad);
		if (pSysShareData->PressErrInfo.ErrCode || bFinishLoad)
		{
			if (pSysShareData->PressErrInfo.ErrCode)
			{
				pSysShareData->bLoadProfile = false;
				bFinishLoad = true;
			}
			else
			{
				if (!pSysShareData->PressErrInfo.ErrCode)
				{
					pSysShareData->PressErrInfo = LoadStatisticsData(pSysShareData->sExSW.ActiveMPId);
					if (pSysShareData->PressErrInfo.ErrCode == 0)
					{
						bSaveSdoPara = true;        //load 完工艺后保存一下参数;
					}
				}
				pSysShareData->bLoadProfile = false;
				bFinishLoad = true;
			}
			pSysShareData->bSwitchRequest = false;
		}
	}
}

/* ModifyIpFunc              //修改IP
* @param[in]     NONE
* @return        NONE
*/
void ModifyIpFunc()
{
	//传递修改Ip的参数
	if (uLastHmiIpModifyCounter != uHmiIpModifyCounter)
	{
		if (!bNeedModifyIp)
		{
			char sNcHmiIp[1025];                //获取到的真正的IP
			ErrorInfo getIpResult;
			getIpResult = GetNcHmiIp((char*)&sNcHmiIp);
			if (!getIpResult.ErrCode)
			{
				int opipeth3Len = strlen(sNcHmiIp);
				if ((opipeth3Len > 0 && opipeth3Len <= 16) &&
					strcmp(sNcHmiIp, sSysDeviceSetInfo.ControlIp) != 0)
				{
					bNeedModifyIp = true;
					memcpy(sth3Ip, sNcHmiIp, opipeth3Len + 1);
					rt_dbgPrint(1, 0, "*********** uHmiIpModifyCounter:%ld Ip[%s->%s]\n",
						uHmiIpModifyCounter,
						sSysDeviceSetInfo.ControlIp,
						sth3Ip);

					memcpy(&sSysDeviceSetInfo.ControlIp, &sth3Ip, opipeth3Len + 1);
				}
				else
				{
					if (!pSysShareData->PressErrInfo.ErrCode)
					{
						pSysShareData->PressErrInfo.ErrCode = 905;
						pSysShareData->PressErrInfo.eErrLever = Error;
					}
					bNeedModifyIp = false;
				}
			}
		}
		uLastHmiIpModifyCounter = uHmiIpModifyCounter;
	}

}

/* SetSystemTimeFunc              //设置系统时间
* @param[in]     NONE
* @return        NONE
*/
void SetSystemTimeFunc()
{
	//设置系统时间
	if (pSysShareData->sExSW.eSysState > Sys_State_Init)
	{
		//设置系统时间
		sSetTimeRtrig.bTrigSignal = sSysDeviceSetInfo.bUpdateTime;
		R_Trig(&sSetTimeRtrig);
		if (sSetTimeRtrig.bTrig && !bNeedModifyTime)
		{
			bNeedModifyTime = true;
		}

		if (pSysShareData->sExSW.eSysState != Sys_State_RunSequence
			&& !pSysShareData->sExSW.bSequenceBusy)
		{
			if (bNeedModifyTime)
			{
				pSysShareData->PressErrInfo = SetSystemTime(sSysDeviceSetInfo.SetTime, &bFinishSetTime);
				if (bFinishSetTime && !pSysShareData->PressErrInfo.ErrCode)
				{
					//更新系统时间检查时间
					sprintf(sSysDeviceSetInfo.sProtectTime, "%04d-%02d-%02d",
						sSysProtectTime.tm_year,
						sSysProtectTime.tm_mon,
						sSysProtectTime.tm_mday);

					sSysDeviceSetInfo.bHadUpdateProtectTime = true;
					bNeedModifyTime = false;
					bSaveSdoPara = true;

					bHadTrigSetTime = true;
					uSetOkCounter++;
					rt_dbgPrint(1, 0, "uSetOkCounter:%ld \n", uSetOkCounter);
				}
			}
		}
	}

}

/* UpdateAvalibParaFunc              //加载参数
* @param[in]     NONE
* @return        NONE
*/
void UpdateAvalibParaFunc()
{
	//如果0x5001 ， 0x5002组参数被修改了，触发 自动刷新 0x5000参数，同时 保存Sdo
	if ((SdoValueChangeCounter != LastSdoValueChangeCounter || bModifyChannelPara))
	{
		if (pSysShareData->sExSW.eSysState >= Sys_State_Init)
		{
			// rt_dbgPrint(1, 0, "UpdateAvaliablePara\n");
			pSysShareData->PressErrInfo = UpdateAvaliablePara();
			if (!pSysShareData->PressErrInfo.ErrCode)
			{
				bSaveSdoPara = true;
				if (pSysShareData->SCSW.ActiveMPId != 0)
				{
					pSysShareData->bNeedLoadProfile = true;
					pSysShareData->uNeedActiveMp = pSysShareData->SCSW.ActiveMPId;
				}
			}
		}

		if (bModifyChannelPara)
			bModifyChannelPara = false;

		LastSdoValueChangeCounter = SdoValueChangeCounter;
	}
}

/* LoadSdoFunc                      //加载参数
* @param[in]     NONE
* @return        NONE
*/
void LoadSdoFunc()
{
	if (bLoadSdoPara)       //从数据库中加载sdo参数
	{
		if (pSysShareData->sExSW.eSysState != Sys_State_RunSequence
			&& !pSysShareData->sExSW.bSequenceBusy
			&& pSysShareData->sExSW.eSysState != Sys_State_Manual_Op)
		{
			LoadSdoParaFromDataBase();
			// LoadSdoParaFromDataBaseNewTable();
			bLoadSdoPara = false;
		}
	}
}

volatile int64  LoadSdoFuncStart_us;
volatile int64  WriteFuncStart_us;

volatile int64    pressFuncStart_us;
volatile float64  initFuncExecTime_uS;
volatile float64  loadStaFuncExecTime_uS;
volatile float64  loadPfFuncExecTime_uS;
volatile float64  saveRFuncExecTime_uS;
volatile float64  writeFuncExecTime_uS;
volatile float64  upAFuncExecTime_uS;
volatile float64  loadDbFuncExecTime_uS;
volatile float64  sdoReadFuncExecTime_uS;
volatile float64  sdoWriteFuncExecTime_uS;
volatile float64  pressFuncExecTime_uS;

void PressFunction()
{
	pressFuncStart_us = GetSysTick();

	//初始化系统参数
	if (bSysInitOp)
	{
		ExeCuteSysInit();
	}
	initFuncExecTime_uS = (GetSysTick() - pressFuncStart_us) / 1000;

	//加载sdo
	LoadSdoFuncStart_us = GetSysTick();
	LoadSdoFunc();
	sdoReadFuncExecTime_uS = (GetSysTick() - LoadSdoFuncStart_us) / 1000;

	//从数据库中加载设置中的数据  
	UpdateParaFromDatabase();
	loadDbFuncExecTime_uS = (GetSysTick() - pressFuncStart_us) / 1000;

	//更新Avaliable参数
	UpdateAvalibParaFunc();
	upAFuncExecTime_uS = (GetSysTick() - pressFuncStart_us) / 1000;

	//加载工艺
	LoadProfileFunc();
	loadPfFuncExecTime_uS = (GetSysTick() - pressFuncStart_us) / 1000;

	if (pSysShareData->sExSW.eSysState > Sys_State_Init)                  //订阅的曲线数据
	{
		SnMaker(&bSnBingdingResult);

		if (pSysShareData->gSysCounter % 10 == 0)
			UpdateDataStateFunc();

		//错误码写入数据库
		ExecuteFaultSave();
		writeFuncExecTime_uS = (GetSysTick() - pressFuncStart_us) / 1000;

		if (bHadRegistSdo && (pSysShareData->sExCW.bSaveSdoConfig || bSaveSdoPara) && SysError(&pSysShareData->sErrInfoPara) < 0)
		{
			//rt_dbgPrint(1, 0, "SaveSdoParaToDataBase\n");
			WriteFuncStart_us = GetSysTick();
			pSysShareData->PressErrInfo = SaveSdoParaToDataBase();
			sdoWriteFuncExecTime_uS = (GetSysTick() - WriteFuncStart_us) / 1000;

			//rt_dbgPrint(1, 0, "\n\n sdoWriteFuncExecTime_uS:%lf\n",sdoWriteFuncExecTime_uS);
			if (!pSysShareData->PressErrInfo.ErrCode)
			{
				lSdoSaveCounter++;
			}
			pSysShareData->sExCW.bSaveSdoConfig = false;
			bSaveSdoPara = false;
		}

		//存结果
		SaveResultFunc();
		saveRFuncExecTime_uS = (GetSysTick() - pressFuncStart_us) / 1000;

		//统计数据
		StatisticsFunc();

		ModifyIpFunc();

		SetSystemTimeFunc();
		loadStaFuncExecTime_uS = (GetSysTick() - pressFuncStart_us) / 1000;
	}


	//pressFuncExecTime_uS = (GetSysTick() - pressFuncStart_us) / 1000;
	//if (pressFuncExecTime_uS > 1000)
	//{
		//rt_dbgPrint(1, 0, "\n\n pressFuncExecTime_uS:%lf \n  sdoReadFuncExecTime_uS:%lf \n sdoWriteFuncExecTime_uS:%lf\n",
		//            pressFuncExecTime_uS,
		//            sdoReadFuncExecTime_uS,
		//            sdoWriteFuncExecTime_uS);
		//rt_dbgPrint(1, 0, "\n\n pressFuncExecTime_uS:%lf \n  initFuncExecTime_uS:%lf \n sdoFuncExecTime_uS:%lf \n loadDbFuncExecTime_uS:%lf \n upAFuncExecTime_uS:%lf \n",
		//    pressFuncExecTime_uS,
		//    initFuncExecTime_uS,
		//    sdoReadFuncExecTime_uS,
		//    loadDbFuncExecTime_uS,
		//    upAFuncExecTime_uS);
		//rt_dbgPrint(1, 0, "loadPfFuncExecTime_uS:%lf \n writeFuncExecTime_uS:%lf \n saveRFuncExecTime_uS:%lf \n loadStaFuncExecTime_uS:%lf\n\n",
		//    loadPfFuncExecTime_uS,
		//    writeFuncExecTime_uS,
		//    saveRFuncExecTime_uS,
		//    loadStaFuncExecTime_uS);

   // }
	ErrorInfoPack(&pSysShareData->PressErrInfo, "EoCalcThread", "");
}

volatile int64 pressStart_us;
volatile float64  pressExecTime_uS;

volatile int64 eoStart_us;
volatile float64  eoExecTime_uS;

void* FunctionThread(void* arg)
{
	ThreadExecInfo* pRtThreadInfo = (ThreadExecInfo*)arg;
	psChartsData->bChartEvalFinish = false;
	psChartsData->uPointsCount = 0;

	//预加载参数
	FsmSysPreloadPara();
	while (1)
	{
		pressStart_us = GetSysTick();

		PressFunction();

		eoStart_us = GetSysTick();
		EoCalc();                   //Eo计算

		eoExecTime_uS = (GetSysTick() - eoStart_us) / 1000;

		pressExecTime_uS = (GetSysTick() - pressStart_us) / 1000;
		if (pressExecTime_uS < 1000)
			Sleep_uS(1000 - pressExecTime_uS);
		//else
		//    printf("\n\n FunctionThread_us:%lf > 1000us     eoExecTime_uS:%lf\n", pressExecTime_uS, eoExecTime_uS);
	}
}


