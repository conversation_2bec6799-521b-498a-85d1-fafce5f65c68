#include <gtest/gtest.h>
#include "base.h"
extern "C" { void R_Trig(TrigData*); void F_Trig(TrigData*); }

TEST(Trig, RisingEdge) {
  TrigData r{}; r.bTrigSignal=false; R_Trig(&r); EXPECT_FALSE(r.bTrig);
  r.bTrigSignal=true; R_Trig(&r); EXPECT_TRUE(r.bTrig);
  R_Trig(&r); EXPECT_FALSE(r.bTrig);
  r.bTrigSignal=false; R_Trig(&r); EXPECT_FALSE(r.bTrig);
}

TEST(Trig, FallingEdge) {
  TrigData f{}; f.bTrigSignal=true; F_Trig(&f); EXPECT_FALSE(f.bTrig);
  f.bTrigSignal=false; F_Trig(&f); EXPECT_TRUE(f.bTrig);
  F_Trig(&f); EXPECT_FALSE(f.bTrig);
  f.bTrigSignal=true; F_Trig(&f); EXPECT_FALSE(f.bTrig);
}