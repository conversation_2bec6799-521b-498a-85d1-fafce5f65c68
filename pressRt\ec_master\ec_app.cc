#include "ethercat.h"
#include "ec_app.h"
#include "base.h"
#include "rttimer.h"
#include "ethercatcoe.h"
#include "motion.h"
#include "driver.h"
#include "ethercatmain.h"
#include "../Lib/soem/ethercatdc.h"
#include "SysShareMemoryDefine.h"

extern ThreadExecInfo rt_ThreadExecInfo;
//extern int64 sync0StarTtime;

char <PERSON>map[128];

int dorun = 0;
bool bEtherCatHandShake;

int expectedWKC;
int wkc;
uint8 currentgroup = 0;

int oloop, iloop;

int64 integral=0;
int64 toff;

PDO_Servo_Output *pPdoCWData;
PDO_Servo_Input *pPdoSWData;

uint16 cw;
uint8_t startup_step=0;
int32 cur_pos = 0;
uint16 csp_pos_delay;
int cmdpos_raw;
int ecState = 0;

int32 iCycleOffSet_ns = 0;
uint32 uLostCounter = 0;
//#define userServo

int Servosetup(uint16 slave)
{	
	int retval;
    uint16 u16val;
    uint8  u8val;
    uint32 u32val;
    retval = 0;

    u16val = 4;
    retval += ec_SDOwrite(slave, 0x1c32, 0x01, FALSE, 2, &u16val, EC_TIMEOUTRXM);
    u16val = 4;
    retval += ec_SDOwrite(slave, 0x1c33, 0x01, FALSE,2, &u16val, EC_TIMEOUTRXM);

    u8val = 0;
    retval += ec_SDOwrite(slave, 0x1c12, 0x00, FALSE, 1, &u8val, EC_TIMEOUTRXM);
    u16val = 0x1600;	
    retval += ec_SDOwrite(slave, 0x1c12, 0x01, FALSE, 2, &u16val, EC_TIMEOUTRXM);
    u8val = 1;
    retval += ec_SDOwrite(slave, 0x1c12, 0x00, FALSE, 1, &u8val, EC_TIMEOUTRXM);

	u8val = 0;
    retval += ec_SDOwrite(slave, 0x1600, 0x00, FALSE, 1, &u8val, EC_TIMEOUTRXM);
	u32val = 0x60400010;	///ControlWord
    retval += ec_SDOwrite(slave, 0x1600, 0x01, FALSE, 4, &u32val, EC_TIMEOUTRXM);
	u32val = 0x607A0020;	///TargetPos
    retval += ec_SDOwrite(slave, 0x1600, 0x02, FALSE, 4, &u32val, EC_TIMEOUTRXM);
	u32val = 0x60FF0020;	///TargetVelocity
    retval += ec_SDOwrite(slave, 0x1600, 0x03, FALSE, 4, &u32val, EC_TIMEOUTRXM);
    u32val = 0x60720010;	///ToqueLimit
    retval += ec_SDOwrite(slave, 0x1600, 0x04, FALSE, 4, &u32val, EC_TIMEOUTRXM);
	u32val = 0x60600008;	///ModeOfOperation
    retval += ec_SDOwrite(slave, 0x1600, 0x05, FALSE, 4, &u32val, EC_TIMEOUTRXM);

	u8val = 5;
    retval += ec_SDOwrite(slave, 0x1600, 0x00, FALSE, 1, &u8val, EC_TIMEOUTRXM);
		

    u8val = 0;
    retval += ec_SDOwrite(slave, 0x1c13, 0x00, FALSE,1, &u8val, EC_TIMEOUTRXM);
    u16val = 0x1a00;
    retval += ec_SDOwrite(slave, 0x1c13, 0x01, FALSE, 2, &u16val, EC_TIMEOUTRXM);
    u8val = 1;
    retval += ec_SDOwrite(slave, 0x1c13, 0x00, FALSE, 1, &u8val, EC_TIMEOUTRXM);
	  
	u8val = 0;
    retval += ec_SDOwrite(slave, 0x1a00, 0x00, FALSE, 1, &u8val, EC_TIMEOUTRXM);

	u32val = 0x60410010;	////Status Word
    retval += ec_SDOwrite(slave, 0x1a00, 0x01, FALSE, 4, &u32val, EC_TIMEOUTRXM);
	u32val = 0x60640020;	///ActualPosition
    retval += ec_SDOwrite(slave, 0x1a00, 0x02, FALSE, 4, &u32val, EC_TIMEOUTRXM);
	u32val = 0x606C0020;	///ActualVelocity
    retval += ec_SDOwrite(slave, 0x1a00, 0x03, FALSE, 4, &u32val, EC_TIMEOUTRXM);

	u32val = 0x60610008;	//ModeOfOperationDisplay
	retval += ec_SDOwrite(slave, 0x1a00, 0x04, FALSE, 4, &u32val, EC_TIMEOUTRXM);
	u32val = 0x603F0010;	///ErrorCode
	retval += ec_SDOwrite(slave, 0x1a00, 0x05, FALSE, 4, &u32val, EC_TIMEOUTRXM);

	//SYSTEM_DRIVE_TYPE			//1:INOVANCE_DRIVE,		2:RAYNEN_DRIVE
#if (SYSTEM_DRIVE_TYPE == 2)
	{
		u32val = 0x60750020;	//Rated Current 		  //0x60750020 
		retval += ec_SDOwrite(slave, 0x1a00, 0x06, FALSE, 4, &u32val, EC_TIMEOUTRXM);

		u32val = 0x60760020;	//Rated Torque 		  //0x60760020 
		retval += ec_SDOwrite(slave, 0x1a00, 0x07, FALSE, 4, &u32val, EC_TIMEOUTRXM);

		u32val = 0x60770010;	//Torque fbk		  //0x60770010 ActualTorque
		retval += ec_SDOwrite(slave, 0x1a00, 0x08, FALSE, 4, &u32val, EC_TIMEOUTRXM);

		u32val = 0x60780010;	//Current fbk		  //0x60770010 ActualCurrent
		retval += ec_SDOwrite(slave, 0x1a00, 0x09, FALSE, 4, &u32val, EC_TIMEOUTRXM);

		u8val = 9;
		retval += ec_SDOwrite(slave, 0x1a00, 0x00, FALSE, 1, &u8val, EC_TIMEOUTRXM);
	}
#else
	{
		u32val = 0x60770010;	//Torque fbk		  //0x60770010 ActualTorque
		retval += ec_SDOwrite(slave, 0x1a00, 0x06, FALSE, 4, &u32val, EC_TIMEOUTRXM);

		u32val = 0x203F0020;	//SubErrcode    
		retval += ec_SDOwrite(slave, 0x1a00, 0x07, FALSE, 4, &u32val, EC_TIMEOUTRXM);

		u8val = 7;
		retval += ec_SDOwrite(slave, 0x1a00, 0x00, FALSE, 1, &u8val, EC_TIMEOUTRXM);
	}
#endif
    return 1;
}


uint64 uTestCounter = 0;
void ec_sync(int64 reftime, int64 cycletime, int64* offsettime) {
	int64 delta;
	/* set linux sync point 50us later than DC sync, just as example */
	delta = (reftime - 1000*10) % cycletime;
	if (delta > (cycletime / 2)) {
		delta = delta - cycletime;
	}
	if (delta > 0) {
		integral += 1;
	}
	if (delta < 0) {
		integral -= 1;
	}
	//*offsettime = -(delta / 5) - (integral / 2);
	uTestCounter++;
	if(uTestCounter > 10000)
		*offsettime = -(delta / 200) - (integral * 50);
	else
		*offsettime = -(delta / 200) - (integral*100);

	//if(uTestCounter %1000 == 0)
	//	dbgPrint(1, 0, "!!!!!ec_sync  offsettime:%ld reftime:%ld delta:%ld \n", *offsettime, reftime, delta);
	//*offsettime = Saturation(*offsettime, -2000, 2000);  //pxf
}

void ec_sync1(int64 reftime, int64 cycletime, int64* offsettime)
{
	int64 delta;
	/* set linux sync point 50us later than DC sync, just as example */
	delta = (reftime - 1000 * 50) % cycletime;
	if (delta > (cycletime / 2)) {
		delta = delta - cycletime;
	}
	if (delta > 0) {
		integral += 1;
	}
	if (delta < 0) {
		integral -= 1;
	}

	*offsettime = -(delta / 100) - (integral/20);
}


uint64 slaveLocalTime;
uint64 slaveLocalTimeLast;
uint64 sync0Time;
uint64 LastSync0;
int sync0Wkc;
int localTimeWkc;

uint16 slaveh;
uint64 readSlaveLocalTime()
{
	slaveh = ecx_context.slavelist[1].configadr;
	localTimeWkc = ecx_FPRD(ecx_context.port, slaveh, ECT_REG_DCSYSTIME, sizeof(slaveLocalTime), &slaveLocalTime, EC_TIMEOUTRET); /* read local time of slave */
	slaveLocalTime = etohll(slaveLocalTime);
	return slaveLocalTime;
}

bool bFirstReadSync0 = true;
void readSlaveTime()
{
	localTimeWkc = ecx_FPRD(ecx_context.port, slaveh, ECT_REG_DCSYSTIME, sizeof(slaveLocalTime), &slaveLocalTime, EC_TIMEOUTRET); /* read local time of slave */
	slaveLocalTime = etohll(slaveLocalTime);

	if (bFirstReadSync0)
	{
		slaveh = ecx_context.slavelist[1].configadr;
		sync0Wkc = ecx_FPRD(ecx_context.port, slaveh, ECT_REG_DCSTART0, sizeof(sync0Time), &sync0Time, EC_TIMEOUTRET); /*slave  从站的后期 */
		sync0Time = etohll(sync0Time);
		bFirstReadSync0 = false;
		//dbgPrint(1, 0, "2222222\n");
	}
	else
	{
		sync0Time = sync0Time + (uint64)SYS_BASE_TIME_uS*1000;
		//dbgPrint(1, 0, "*******1\n");
	}

}

uint64 gCountor1 = 0;
int64	ulLastDcTime = 0;
uint64 uRecTime_tick = 0;

ErrorInfo EthercatErrInfo;
uint64 inStartSlaveTime;
ErrorInfo ec_master_period_data_in(void)
{
	EthercatErrInfo.ErrCode = 0;
	if (dorun || bEtherCatHandShake)
	{
		wkc = ec_receive_processdata(EC_TIMEOUTRET);
		//	ec_sync(slaveLocalTime, pSysShareData->EtherCat_ThreadExecInfo.RunPeriod_uS * 1000, &toff);
		//	pSysShareData->EtherCat_ThreadExecInfo.NextPeriod_uS = pSysShareData->EtherCat_ThreadExecInfo.RunPeriod_uS - (toff / 1000.0);
		//for (int i = 0; i < AxisNum; i++)
		//{
		Axis_Para* pAxis = &pSysShareData->AxisMc[0].Axis;
		pPdoSWData = ((PDO_Servo_Input*)ec_slave[1].inputs);// +i;
		pAxis->Motor.LinkVar.SW = pPdoSWData->SW;
		pAxis->Motor.LinkVar.PosFbk = pPdoSWData->PosFbk; 
		pAxis->Motor.LinkVar.VelFbk = pPdoSWData->VelFbk;
		pAxis->Motor.LinkVar.TorqueFbk_Percent = pPdoSWData->TorqueFbk;
#if SYSTEM_DRIVE_TYPE == 2
		pAxis->Motor.LinkVar.CurrentFbk = pPdoSWData->CurrentFbk;
#elif  SYSTEM_DRIVE_TYPE == 1
		pAxis->Motor.LinkVar.SubErrCode = pPdoSWData->SubErrCode;
#endif // SYSTEM_DRIVE_TYPE == 2


		pAxis->Motor.LinkVar.ErrCode = pPdoSWData->ErrCode;
		pAxis->Motor.LinkVar.ModeOfOpDis = pPdoSWData->CurrentMode;
		//}

		if (wkc == expectedWKC)
		{
			pSysShareData->gEthercatDeviceInOp = true;
			if (uLostCounter)
			{
				dbgPrint(1, 0, "!!!!!Reset Ethercat Lost Counter:%d\n", uLostCounter);
				uLostCounter = 0;
			}
		}
		else
		{
			uLostCounter++;
			if (uLostCounter > 10)		
			{
				pSysShareData->gEthercatDeviceInOp = false;
				EthercatErrInfo.ErrCode = 8000;
				EthercatErrInfo.eErrLever = HwError;
				dorun = 0;
				dbgPrint(1, 0, "!!!!! Ethercat Lost uLostCounter:%d\n", uLostCounter);
			}
			else
			{
				dbgPrint(1, 0, "wkc:%d != expectedWKC:%d\n", wkc, expectedWKC);
			}
		}
	}

	ErrorInfoPack(&EthercatErrInfo, "ec_master_period_data_in", "");
	return EthercatErrInfo;
}

void ec_master_period_data_out(void) {
	if (dorun || bEtherCatHandShake) {
		//for (int i = 0; i < AxisNum; i++) {
		Axis_Para* pAxis = &pSysShareData->AxisMc[0].Axis;
		pPdoCWData = ((PDO_Servo_Output*)ec_slave[1].outputs);// +0;
		pPdoCWData->CW = pAxis->Motor.LinkVar.CW;
		pPdoCWData->PosRef = pAxis->Motor.LinkVar.PosRef;
		pPdoCWData->TargetMode = pAxis->Motor.LinkVar.ModeOfOp;
		pPdoCWData->VelRef = pAxis->Motor.LinkVar.VelRef;
		pPdoCWData->TrqLmt = pAxis->Motor.LinkVar.TorqueRef;
		//}
		ec_send_processdata();
	}
}

bool bEthercatHadInit = false;
uint32 uCheckCounter = 0;
uint8 iEthercatStep = 0;
uint8 iLastEthercatStep = 0;
uint32 uTimeCounter = 0;

uint32 uRateCurrent;

ErrorInfo EthercatInitErrInfo;
ErrorInfo EthercatMasterInit(const char* ifname,bool *pbInitBusy)
{
	switch (iEthercatStep)
	{
	case 0:
	{
		memset(&EthercatInitErrInfo, 0, (uint64_t)sizeof(ErrorInfo));
		iLastEthercatStep = 0;
		*pbInitBusy = true;
		dorun = 0;
		bEtherCatHandShake = false;
		if (ec_init(ifname))
		{
			iEthercatStep = 1;
		}
		else
		{
			EthercatInitErrInfo.ErrCode = 8002;// 163;
			EthercatInitErrInfo.eErrLever = HwError;
			iEthercatStep = 100;
			dbgPrint(1, 0, "No socket connection on %s\nExcecute as root\n", ifname);
		}
	}
	break;
	case 1:
	{
		if (ec_config_init(TRUE) > 0)
		{
			iEthercatStep = 2;
		}
		else
		{
			EthercatInitErrInfo.ErrCode = 8003;// 164;
			EthercatInitErrInfo.eErrLever = HwError;
			iEthercatStep = 100;
			dbgPrint(1, 0, "No slaves found!\n");
		}
	}
	break;
	case 2:
	{
		if (ec_configdc()) //
		{
			iEthercatStep = 3;
		}
		else
		{
			dbgPrint(1, 0, "ec_configdc Error!\n");
			iEthercatStep = 100;
		}
	}
		break;
	case 3:
	{
		iCycleOffSet_ns = (int32)SYS_BASE_TIME_uS * 0.5 * 1000;
		
		for (int i = 0; i < AxisNum; i++) {
			ec_dcsync0(i + 1, TRUE,SYS_BASE_TIME_uS * 1000, iCycleOffSet_ns);
		}
		ec_slave[1].PO2SOconfig = &Servosetup;						

		ec_config_map(&IOmap);
		iEthercatStep = 4;
	}
		break;
	case 4:
	{
		uint16 curState = ec_statecheck(0, EC_STATE_SAFE_OP, EC_TIMEOUTSTATE * 2);
		if (curState == EC_STATE_SAFE_OP)
		{
			for (int i = 0; i < AxisNum; i++) {
				pSysShareData->AxisMc[i].Axis.Cfg.MotorPlusePerRevolution = 8388608;
				pSysShareData->AxisMc[i].Axis.Cfg.ShaftPlusePerRevolution = 1000000;

				uint32 u32val = pSysShareData->AxisMc[i].Axis.Cfg.MotorPlusePerRevolution;
				ec_SDOwrite(i + 1, 0x6091, 0x01, FALSE, 4, &u32val, EC_TIMEOUTRXM);
				u32val = pSysShareData->AxisMc[i].Axis.Cfg.ShaftPlusePerRevolution;																//1000000
				ec_SDOwrite(i + 1, 0x6091, 0x02, FALSE, 4, &u32val, EC_TIMEOUTRXM);
			}
			iEthercatStep = 5;
		}
		else if (curState == EC_STATE_ACK || curState == EC_STATE_ERROR)
		{
			EthercatInitErrInfo.ErrCode = 8004;// 165;
			EthercatInitErrInfo.eErrLever = HwError;
			dbgPrint(1, 0, "ec_statecheck  curState:%s\n", "EC_STATE_ERROR");
			iEthercatStep = 100;
		}
	}
	break;
	case 5:
	{
		ec_readstate();
		expectedWKC = (ec_group[0].outputsWKC * 2) + ec_group[0].inputsWKC;
		//dbgPrint(1, 0, "expectedWKC:%d outputsWKC:%d  inputsWKC:%d\n", expectedWKC, ec_group[0].outputsWKC, ec_group[0].inputsWKC);
		iEthercatStep = 6;
	}
	break;
	case 6:
	{
		ec_slave[0].state = EC_STATE_OPERATIONAL;
		ec_slave[1].state = EC_STATE_OPERATIONAL;
		ec_writestate(0);

		uint64 tmpLocalTime = readSlaveLocalTime();
		if (sync0StarTtime > (int64)tmpLocalTime)
		{
			double dWaitFirstSync0 = Tick2uS(sync0StarTtime - tmpLocalTime);
			if (dWaitFirstSync0 > double(2 * SYS_BASE_TIME_uS))						//等从站的第一个sync0
			{
				int sleepTime = int(dWaitFirstSync0 - double(2 * SYS_BASE_TIME_uS));
				Sleep_uS(sleepTime);
				//dbgPrint(1, 0, "FirstDcSync0:%lld tmpLocalTime:%lld  dWaitFirstSync0_us:%lf  sleepTime:%d\n",
				//	sync0StarTtime,
				//	tmpLocalTime,
				//	dWaitFirstSync0,
				//	sleepTime);
			}
		}

		uTimeCounter = 0;
		bEtherCatHandShake = true;
		iEthercatStep = 7;
	}
	break;
	case 7:
	{
		uTimeCounter++;
		if (uTimeCounter > 5000)
		{
			dbgPrint(1, 0, "Not all slaves reached operational state.\n");
			ec_readstate();
			for (int i = 1; i <= ec_slavecount; i++) {
				if (ec_slave[i].state != EC_STATE_OPERATIONAL) {
					dbgPrint(1, 0, "Slave %d State == 0x%2.2x StatusCode=0x%4.4x : %s\n",
						i,
						ec_slave[i].state,
						ec_slave[i].ALstatuscode,
						ec_ALstatuscode2string(ec_slave[i].ALstatuscode));
				}
			}
			dbgPrint(1, 0, "\nRequest init state for all slaves\n");
			ec_slave[0].state = EC_STATE_INIT;
			ec_writestate(0);
			dbgPrint(1, 0, "End simple test, close socket\n");
			ec_close();

			bEtherCatHandShake = false;
			*pbInitBusy = false;
			iEthercatStep = 0;
		}
		else
		{
			ec_readstate();
			if ((ec_slave[0].state == EC_STATE_OPERATIONAL && ec_slave[1].state == EC_STATE_OPERATIONAL)
				&& wkc == expectedWKC)
			{
				#if SYSTEM_DRIVE_TYPE == 2
					pPdoSWData = ((PDO_Servo_Input*)ec_slave[1].inputs);
					pSysShareData->AxisMc[0].Axis.Motor.fMotorRatedTorque = (float32)pPdoSWData->RatedTorque / 1000.0;
					pSysShareData->AxisMc[0].sMcSetPara.sTorqueLimRange.Maxinum = 3 * pSysShareData->AxisMc[0].Axis.Motor.fMotorRatedTorque;
					pSysShareData->AxisMc[0].sMcSetPara.sTorqueLimRange.Mininum = -3 * pSysShareData->AxisMc[0].Axis.Motor.fMotorRatedTorque;

					pSysShareData->AxisMc[0].Axis.Motor.fMotorRatedCurrent = (float32)pPdoSWData->RatedCurrent / 1000.0;
					pSysShareData->AxisMc[0].sMcSetPara.sCurrentLimRange.Maxinum = 3 * pSysShareData->AxisMc[0].Axis.Motor.fMotorRatedCurrent;
					pSysShareData->AxisMc[0].sMcSetPara.sCurrentLimRange.Mininum = -3 * pSysShareData->AxisMc[0].Axis.Motor.fMotorRatedCurrent;

					dbgPrint(1, 0, "<<<<<<<<<<<<<<<<<<<<<Operational state reached for all slaves >>>>>>>>>>>>>>>>>>>>\n\n");

					dbgPrint(1, 0, "<<<<<<<<<<<<<<<<<<<<<uTimeCounter:%d fMotorRatedTorque:%f  fMotorRatedCurrent:%f>>>>>>>>>>>>>>>>>>>>\n\n", 
						uTimeCounter,
						pSysShareData->AxisMc[0].Axis.Motor.fMotorRatedTorque,
						pSysShareData->AxisMc[0].Axis.Motor.fMotorRatedCurrent);
				#else
					dbgPrint(1, 0, "<<<<<<<<<<<<<<<<<<<<<Operational state reached for all slaves  uTimeCounter:%d  >>>>>>>>>>>>>>>>>>>>\n\n",uTimeCounter);
				#endif

				dorun = 1;
				uLostCounter = 0;
				*pbInitBusy = false;
				iEthercatStep = 0;
				bEtherCatHandShake = false;
			}
		}
	}
	break;
	case 100:
		bEtherCatHandShake = false;
		iEthercatStep = 0;
		*pbInitBusy = false;
	break;
	default:
		break;
	}

	if (iLastEthercatStep != iEthercatStep)
	{
		//dbgPrint(1, 0, "EthercatMasterInit[%d -> %d]\n", iLastEthercatStep, iEthercatStep);
		iLastEthercatStep = iEthercatStep;
	}
	ErrorInfoPack(&EthercatInitErrInfo, "EthercatMasterInit", "");
	return EthercatInitErrInfo;
}

int state = -1;
struct timespec tsEtherCat;
void EtherCat_Sleep(int TIME)
{
	clock_gettime(CLOCK_MONOTONIC, &tsEtherCat);
	tsEtherCat.tv_nsec += TIME * 1000;
	if (tsEtherCat.tv_nsec >= 1000000000) {//秒数判断
		tsEtherCat.tv_sec += 1;
		tsEtherCat.tv_nsec -= 1000000000;
	}
	clock_nanosleep(CLOCK_MONOTONIC, TIMER_ABSTIME, &tsEtherCat, NULL);
}

/* rt_timer_handle
* @param[in]               None
* @return                  None
*/
uint64 gPrintfCounter = 0;
uint64 OffSync0LocalTime;
void EtherCat_timer_handle(uint64* pgSysCounter, ThreadExecInfo* pThreadExec)
{
	if (dorun || bEtherCatHandShake)
	{
		readSlaveTime();

		if (sync0Time > slaveLocalTime)
		{
			pThreadExec->NextPeriod_uS = double(Tick2uS(sync0Time - slaveLocalTime));
			//dbgPrint(1, 0, "sync0Time:%ld   slaveLocalTime:%ld    slaveLocalTimeLast:%ld\n", sync0Time, slaveLocalTime, slaveLocalTimeLast);
		}
		else
		{
			pThreadExec->NextPeriod_uS = 0;
			//dbgPrint(1, 0, "sync0Time:%ld  < slaveLocalTime:%ld   LastSync0:%ld\n",sync0Time,slaveLocalTime, LastSync0);
		}
	}
	else
	{
		bFirstReadSync0 = true;
		pThreadExec->NextPeriod_uS = pThreadExec->RunPeriod_uS;
	}
	LastSync0 = sync0Time;
	slaveLocalTimeLast = slaveLocalTime;

	pThreadExec->start_tick = GetSysTick();
	if (pThreadExec->pFunc)
		pThreadExec->pFunc();

	pThreadExec->ExecTime_uS = Tick2uS(GetSysTick() - pThreadExec->start_tick);
	//pThreadExec->_ExecTimeAuxCalc.Sum += pThreadExec->ExecTime_uS;
	//pThreadExec->_ExecTimeAuxCalc.Num++;

	if (pThreadExec->ExecTime_uS > pThreadExec->MaxExecTime_uS)
		pThreadExec->MaxExecTime_uS = pThreadExec->ExecTime_uS;

	if (pThreadExec->ExecTime_uS < (float32)pThreadExec->MinExecTime_uS)
		pThreadExec->MinExecTime_uS = pThreadExec->ExecTime_uS;

	if (pThreadExec->bResetFlag)
	{
		pThreadExec->MaxJitter_uS = 0;
		pThreadExec->MinJitter_uS = 0;
		pThreadExec->MaxExecTime_uS = 0;
		pThreadExec->MinExecTime_uS = 0;
		pThreadExec->bResetFlag = false;
	}

	//if (dorun || bEtherCatHandShake)
	//{
	//	dbgPrint(1, 0, "sync0Time:%ld   slaveLocalTime:%ld NextPeriod_uS:%lf   SendTime:%lf\n", sync0Time, slaveLocalTime, pThreadExec->NextPeriod_uS, pThreadExec->ExecTime_uS);
	//}
	if (pThreadExec->NextPeriod_uS > pThreadExec->ExecTime_uS)
		pThreadExec->WaitTime_uS = pThreadExec->NextPeriod_uS - pThreadExec->ExecTime_uS;
	else
		pThreadExec->WaitTime_uS = 0;
	
	//pThreadExec->gSysTimestamp_uS = GetTimeStamp_uS();
	//pThreadExec->Jitter_uS = (*pgSysCounter < 10000) ? 0 : (Tick2uS(pThreadExec->start_tick - pThreadExec->last_tick) - (float64)pThreadExec->RunPeriod_uS);
	//pThreadExec->Jitter_uS = (Tick2uS(pThreadExec->start_tick - pThreadExec->last_tick) - (float64)pThreadExec->RunPeriod_uS);

	//if (pThreadExec->Jitter_uS > pThreadExec->MaxJitter_uS)
	//	pThreadExec->MaxJitter_uS = pThreadExec->Jitter_uS;

	//if (pThreadExec->Jitter_uS < pThreadExec->MinJitter_uS)
	//	pThreadExec->MinJitter_uS = pThreadExec->Jitter_uS;

	//pThreadExec->CPULoad = (float32)(pThreadExec->ExecTime_uS / (float64)pThreadExec->RunPeriod_uS);


	pThreadExec->gSysTimestamp_uS_Last = pThreadExec->gSysTimestamp_uS;
	pThreadExec->last_tick = pThreadExec->start_tick;
}




