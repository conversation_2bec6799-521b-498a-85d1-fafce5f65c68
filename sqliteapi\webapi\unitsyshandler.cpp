﻿#include "unitsyshandler.h"
#include "responsejson.h"
#include "nlohmann/json.hpp"

UnitSysHandler::UnitSysHandler(DBI* db) :ApiHandler(db)
{
	tableCheck();
}

UnitSysHandler::~UnitSysHandler()
{

}

void UnitSysHandler::tableCheck()
{
		std::string sql = R"(CREATE TABLE IF NOT EXISTS UnitSysConfig (Id INTEGER PRIMARY KEY  AUTOINCREMENT, Item VARCHAR (32) UNIQUE, Unit VARCHAR (32),Precision INTEGER , Author VARCHAR (32), UpdateTime DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')));)";
		auto rc = dbi->exec(sql, NULL, NULL);
		if (rc != SQLITE_OK)
		{
			std::cerr << "创建单位配置表失败,错误码 " << rc << std::endl;
		}
}



void UnitSysHandler::bindServe(httplib::Server& svr)
{
	svr.Get("/sys/uint/info", (httplib::Server::Handler)std::bind(&UnitSysHandler::handle, this, std::placeholders::_1, std::placeholders::_2));
	svr.Post("/sys/uint/set", (httplib::Server::Handler)std::bind(&UnitSysHandler::setUnit, this, std::placeholders::_1, std::placeholders::_2));
}

void UnitSysHandler::handle(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	sqlite3_stmt* stmt = nullptr;
	std::string sql = "SELECT item,unit,precision from UnitSysConfig ";
	auto rc = dbi->prepare(sql, stmt);
	if (rc == SQLITE_OK)
	{
		res_json.predata();
		char prefix = '[';
		int cnt = 0;
		while (sqlite3_step(stmt) == SQLITE_ROW)
		{
			auto item = dbi->dbStr(stmt, 0); //
			auto unit = dbi->dbStr(stmt, 1); //
			auto precision = dbi->dbInt(stmt, 2,3); //
			res_json << prefix << '{';
			res_json.fillKeyVal("item", item)<<',';
			res_json.fillKeyVal("unit", unit)<<',';
			res_json.fillKeyVal("precision", precision)<<'}';
			prefix = ',';
			cnt++;
		}
		res_json << (cnt > 0 ? "]}" : "[]}");
	}
	else
	{
		res_json.json(17528, u8"查找单位配置SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17528;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, "handle", "");
}

void UnitSysHandler::setUnit(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson resJson;
	nlohmann::json jsonPara = nlohmann::json::parse(req.body);
	std::string  _item = jsonPara["item"];
	std::string  unit = jsonPara["unit"];
	int  precision = jsonPara["precision"];
	std::string  author = jsonPara["author"];
	sqlite3_stmt* stmt = nullptr;
	std::string sql = "insert or replace into UnitSysConfig ( item,unit,precision ,author,UpdateTime) values( ?,?,?,?, strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime'));";
	auto rc = dbi->prepare(sql,stmt);
	rc += sqlite3_bind_text(stmt, 1, _item.c_str(), _item.length(), NULL);
	rc += sqlite3_bind_text(stmt,2, unit.c_str(), unit.length(), NULL);
	rc += sqlite3_bind_int(stmt, 3, precision);
	rc += sqlite3_bind_text(stmt,4, author.c_str(), author.length(), NULL);
	if (rc == SQLITE_OK)
	{
		if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() == 1)
		{
			resJson.json(0, u8"修改初始单位配置成功");
		}
		else
		{
			resJson.json(17064, u8"修改初始单位配置失败");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17064;
		}
	}
	else
	{
		resJson.json(17529, u8"设置单位配置SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17529;
	}
	res.set_content(resJson.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, "setUnit", "");
}


