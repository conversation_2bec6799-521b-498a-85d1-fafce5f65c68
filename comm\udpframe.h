/**
 * @file udpframe.h
 * <AUTHOR>
 * @brief 
 * @version 0.1.0
 * @date 2022-08-15
 * 
 * @copyright Copyright (c) 2022
 * 
 */
#ifndef UDPFRAME_H_
#define UDPFRAME_H_

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/epoll.h>
#include <netinet/in.h>
#include  "./core/RingBuffer.h"
#include "./core/base.h"


#define Max_Frame_Num				60			// = Max_Context_Size/RX_FRAME_FULL_SIZE
#define Max_Context_Size			84720		// = Max_Frame_Num * RX_FRAME_FULL_SIZE
#define Max_Context_Num				10
#define Max_Connection_Num			3			//3
#define Package_Head_Length			12
#define Max_Tx_Data_Size			84720
#define FRAME_FULL_SIZE				1412
#define FRAME_BODY_FULL_SIZE		1400

#define MAX_EVENTS					1024
#define Topic_Send_Buffer_Size		10240        // 65536*4;  // nasihs
#define MAX_TOPIC_NUM				4			//

typedef struct _sRxUdpPacket {
	uint16 ContentLen = 0;
	int32  subFrameIdx;
	uint32 ChannelID;
	union {
		uint16 FrameType;
		uint16 ErrCode;
	};
	byte data[FRAME_FULL_SIZE];
	int32 iFrameLength;
}__attribute__((packed)) sRxPacket;

typedef struct _sTxUdpPacket {
	uint16 ContentLen = 0;
	int32 subFrameIdx;
	uint32 ChannelID;
	union {
		uint16 FrameType;
		uint16 ErrCode;
	};
	byte data[Max_Tx_Data_Size];
	int32 iOverNum;
	int32 iFrameLength;
}__attribute__((packed)) sTxPacket;


#pragma pack(1)
typedef struct _sChannelContext {
	bool	bInUse;
	uint32	ChannelID;
	int32	iRxByteNum;
	uint64	LastUpdateTime;
	int32	IdxSum;
	int32	IdxInc;

	int32 FrameSum = 0;
	int32 aFrameIdx[Max_Frame_Num];

	struct 
	{
		byte Buf[Max_Context_Size];
	}Data;
}sChannelContext;



typedef struct _SubTopicData {
	str255              szName;
	bool                bEnable;
	SDOEntryDesc*		pSdoEntry;
	uint16              nSingleDataByteSize;
	byte                SendBuffer[Topic_Send_Buffer_Size];
	uint32*				pHead;
	EDataType*			pDataType;
	uint16              iSubscribeType;
	uint32*				pSampleCounter;
	int                 iInitBufferLength;
	int                 iBufferLength;
	void*				pLastData;

	uint32				gSysTopicCounter;
	uint64				start_Topic;
	sByteRingBuffer		gRtCurveFifo;
}SubTopicData;


typedef struct _SubTopic {
	bool                bUsed;
	int8                gSdoTopicNum;

	SubTopicData        sTopicData[MAX_TOPIC_NUM];
}SubTopic;

typedef struct _sConnection {
	bool			bTcpHandShakeOk;		//tcp�Ƿ�������		
	bool			bConnected;				//�Ƿ������ϣ�ͨ�ţ�
	int32			sockClient;				//��ǰ���ӵ�sock
	int32			epollClient;			//��ǰ��������շ���Ϣ��epoll
	
	epoll_event		event_Set;				//��ǰ�շ� epoll��event set
	epoll_event		Events[MAX_EVENTS];		//��ǰ�շ� epoll��

	int32			ConnectIndex;
	//uint32			Ip;
	//uint16			Port;
	sockaddr_in		sockAddr;				//��ǰ���ӵ�addr
	uint64			LastUpdateTime;			//unit is uS

	SubTopic		vSdoTopic;
	sChannelContext contexts[Max_Context_Num];
}sConnection;

#pragma pack()

#endif /* UDPFRAME_H_ */
