#include "Sequence.h"
#include <syslog.h>
#include <stdlib.h>
#include <string.h>

#include "motion.h"
#include "SequenceItemInit.h"

#include "SequenceItem.h"
#include "stdio.h"
#include "SeqItemMotion.h"
#include "rttimer.h"
#include "SysShareMemoryDefine.h"
#include "pid.h"

bool gIgnoreTimeoutError = 0;
TrigData rAbortItemTrig;

SPidData  ProfileForoceCtrlPid_csv = {.kp=0.8, .ki=15.0, .kd=0.0, .fIntegrationLimit=1.0, .fOutUpLimit=7, .fOutDownLimit=0};


ErrorInfo SeqErrInfo;
bool  bItemExeBusy;     //sequence婵炴垶鎼╅崢鎯р枔閹达箑绠ョ憸鎴︺�侀幋锕�鍙婃い鏍ㄧ矊濞堢娀鏌熼顒�鎳忛悾閬嶆煥濞戞瀚扮憸鎶婂拑鎷峰☉娅亜锕㈤鍫濈鐟滄垿銆侀幋锝冧汗闁规儳鍟块·鍛槈閹垮嫭瀚规繛鎴炴惄娴滅偛鈻撻幋锕�绠氶柛娑卞幖閺嬪矂鏌曢崱顓熷
//!!!All function must can reentry

ExecuteFeedback     sExecuteRequestFeedback{ .bExeDataHadNotReset = false };
RequestsAction      sRequestAction;


ErrorInfo InitItem(SeqItem* pSeqItem)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    if (pSeqItem)
    {
        //rt_dbgPrint(1, 1, "pSeqItem->pExeActor = %ld\n", pSeqItem->pExeActor);
        if (pSeqItem->pExeActor != NULL)
        {
            if (pSeqItem->pExeActor->Init != NULL)
            {
                errorInfo = pSeqItem->pExeActor->Init(pSeqItem);
            }
        }
        else if (pSeqItem->pFlowActor != NULL)
        {
            ;
        }
    }
    ErrorInfoPack(&errorInfo, "InitItem", "");
    return errorInfo;
}

ErrorInfo SequenceItemInit(SeqRef* pSeqRef, SeqItem* pSeqItem)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0,  (uint64)sizeof(ErrorInfo));

    if (pSeqItem == nullptr)
    {
        ErrorInfoPack(&errorInfo, "SequenceItemInit", "");
        return errorInfo;  //pSeqItem为空
    }

    errorInfo = InitItem(pSeqItem);
    if (errorInfo.ErrCode)
    {
        pSysShareData->SeqAux.iActiveRowIndex = pSeqItem->iRowIndex;
        ErrorInfoPack(&errorInfo, "SequenceItemInit", "");
        return errorInfo; // 如果初始化失败，立刻返回错误信息
    }

    pSeqItem->pSeqRef = pSeqRef;

    if (pSeqItem->pChild)
    {
        errorInfo = SequenceItemInit(pSeqRef, pSeqItem->pChild);
        if (errorInfo.ErrCode)  // 如果子节点初始化失败，直接返回错误
        {
            ErrorInfoPack(&errorInfo, "SequenceItemInit", "");
            return errorInfo;
        }
    }

    if (pSeqItem->pNext)
    {
        errorInfo = SequenceItemInit(pSeqRef, pSeqItem->pNext);
    }

    ErrorInfoPack(&errorInfo, "SequenceItemInit", "");
    return errorInfo;
}


ErrorInfo SeqInit(SeqRef *pSeqRef)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0,  (uint64)sizeof(ErrorInfo));
	for(int i=0;i<pSeqRef->nBranchNum;i++)
    {
		sSeqRef_Branch* pBranch = pSeqRef->vpBranch[i];
        if (pBranch->pFirstItem != NULL)
        {
            errorInfo = SequenceItemInit(pSeqRef, pBranch->pFirstItem);
            if (errorInfo.ErrCode)
            {
                break;
            }
        }
        pBranch->nRowCount = 0;
	}
    ErrorInfoPack(&errorInfo, "SeqInit", "");
    return errorInfo;
}



static SeqItem* getNextItem(SeqItem* pCurr)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0,  (uint64)sizeof(ErrorInfo));

    SeqItem* pNext;
   // printf("getNextItem Type: %s\n", pCurr->szType);
   // printf("getNextItem iRowIndex: %d\n", pCurr->iRowIndex);

    if (pCurr->pFlowActor != NULL)
    {
        errorInfo = pCurr->pFlowActor->CanGoInChild(pCurr);
    }

    if ((pCurr != NULL) && (!pCurr->bFunctionDisable) && pCurr->bCanGoInchild && (pCurr->pChild != NULL))
    {
       // pCurr->bFunctionDisable = true;
        if (pCurr->bCanGoInchild && (pCurr->pChild != NULL))
        {
            //GrounpItemsInit
            //pCurr->iExecuteCounter = 0;
            return pCurr->pChild;
        }
    }
    else
    {
        pCurr->iExecuteCounter= 0;
        pNext = pCurr->pNext;
        while (pNext != NULL && pNext->bFunctionDisable) {
            pNext = pNext->pNext;
        }
        if (pNext != NULL && !pNext->bFunctionDisable)
        {
            return pNext;
        }
        else if (pCurr->pParent != NULL)
        {
            return pCurr->pParent;
        }
        else
        {
            return 0;
        }
    }
	return NULL;
}


static inline ErrorInfo StartExeItem(SeqItem* pSeqItem)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0,  (uint64)sizeof(ErrorInfo));
    if (pSeqItem)
    {
        pSeqItem->pSeqRef->bHadAbortCurrentItem = false;
        if (pSeqItem->pExeActor != NULL) 
        {
            pSeqItem->pExeActor->Start(pSeqItem);
        } 
        //else if (pSeqItem->pFlowActor != NULL)
        //{
        //    ;
        //}
    }

    ErrorInfoPack(&errorInfo, "StartExeItem", "");
    return errorInfo;
}

static SeqItem* getNextItemForCheck(SeqItem* pCurr)
{
    if (pCurr->pChild) {
        return pCurr->pChild; 
    }
    return pCurr->pNext; 
}

/* BranchStart   CheckChildItems  // 递归检查子节点函数
* @param[in]     SeqItem* pItem
* @return        ErrorInfo
*/
ErrorInfo CheckChildItems(SeqItem* pItem)
{
    ErrorInfo errorInfo = { 0 };
    SeqItem* pChild = pItem;

    while (pChild) {
        if ((errorInfo = MotionItemInitCheck(pChild)).ErrCode) {
            return errorInfo;
        }
        //递归检查子节点
        if (pChild->pChild) {
            if ((errorInfo = CheckChildItems(pChild->pChild)).ErrCode) {
                return errorInfo;
            }
        }

        pChild = pChild->pNext;
    }
    return errorInfo;
}

/* BranchStart   BranchItemParaCheck  Branch闂佸憡鐟ラ崐褰掑汲閻旇偤娑㈡晸娴犲钃熼柨鐕傛嫹
* @param[in]     sSeqRef_Branch* pBranch
* @return        ErrorInfo
*/
ErrorInfo BranchItemParaCheck(sSeqRef_Branch* pBranch)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0,  (uint64)sizeof(ErrorInfo));

    pBranch->pCurrItem = pBranch->pFirstItem;
    pBranch->iActiveRowIndex = 0;
    fSeqAimPos = fHomePos;
    fForceAim = 0;

    bValidForceAimCheck = false;
    bValidAimPosCheck = true;

    const int MAX_ITERATIONS = 1000; // 根据实际节点数量调整
    int iterationCounter = 0;
    do
    {
        if (++iterationCounter > MAX_ITERATIONS) {
            errorInfo.ErrCode = 1001;
            break;
        }
        if (pBranch->pCurrItem != NULL)
        {
            errorInfo = MotionItemInitCheck(pBranch->pCurrItem);
            if (errorInfo.ErrCode)
            {
                pBranch->iActiveRowIndex = pBranch->pCurrItem->iRowIndex;
                break;
            }
            // 特殊处理循环节点
            if (strcmp(pBranch->pCurrItem->szType, "While") == 0)
            {
                // While节点处理策略：
                 //  1. 检查循环体子节点参数
                 //  2. 但不进入实际循环执行流程
                  // 3. 手动移动到下一个节点
                if (pBranch->pCurrItem->pChild) {
                    //递归检查子节点
                    errorInfo = CheckChildItems(pBranch->pCurrItem->pChild);
                    if (errorInfo.ErrCode)
                    {
                        break;
                    }

                }
                pBranch->pCurrItem = pBranch->pCurrItem->pNext;
            }
            else
            {
                pBranch->pCurrItem = getNextItemForCheck(pBranch->pCurrItem);
            }
        }
    } while (pBranch->pCurrItem != NULL);

    fSeqAimPos =  fHomePos;

    ErrorInfoPack(&errorInfo, "BranchItemParaCheck", "");
    return errorInfo;
}

/* BranchStart   //闂佸憡姊绘慨鎯归崲濡慳nch闂佸憡鐟ラ崐褰掑汲閿燂拷  濡ょ姷鍋犻崺鏍夐崨鏉戣摕闁靛鍎卞Λ姗�鏌℃笟濠冨
* @param[in]     sSeqRef_Branch* pBranch
* @return        ErrorInfo
*/
ErrorInfo BranchStart(SeqRef* pSeqRef)//(sSeqRef_Branch* pBranch) 
{
   // rt_dbgPrint(1, 1, "BranchStart...\n");
    ErrorInfo errorInfo;
    memset(&errorInfo, 0,  (uint64)sizeof(ErrorInfo));
    uint8 index = 0;
    for (int i = 0; i < pSeqRef->nBranchNum; i++)
    {
        sSeqRef_Branch* pBranch = pSeqRef->vpBranch[i];
        pSeqRef->BranchActiveRowIndex[index] = 0;

        //闂備緡鍋呭銊╁极閵堝鐓ユ繛鍡樺俯閸ゆ牕顭跨捄鍝勵伀闁诡喖锕弻鍛村及韫囨洖绔�
        pBranch->sSqErrAction.GeneralAction.Pause = true;
        pBranch->sSqErrAction.GeneralAction.ShowError = true;
        pBranch->sSqErrAction.GeneralAction.Stop = false;
        pBranch->sSqErrAction.GeneralAction.PowerDown = false;
        pBranch->sSqErrAction.GeneralAction.GoHome = false;


        errorInfo = BranchItemParaCheck(pBranch);

        if (errorInfo.ErrCode == 0)
        {
            pBranch->pCurrItem = pBranch->pFirstItem;
            pBranch->iActiveRowIndex = -1;
        }
        else
        {
            pSysShareData->SeqAux.iErrorRowIndex = pBranch->iActiveRowIndex;
            break;
        }

    }

    ErrorInfoPack(&errorInfo, "BranchStart", "");
    return errorInfo;
}

/* FindItemByRowIndex      
* @param[in]            SeqRef* pSeqRef
* @param[in]            int iTargetRow
* @return               ErrorInfo
*/
SeqItem* FindItemByRowIndex(sSeqRef_Branch* pBranch, int iTargetRow) 
{
    SeqItem* pCurrent = pBranch->pFirstItem;
    while (pCurrent != NULL) 
    {
        if (pCurrent->iRowIndex == iTargetRow)
        {
            return pCurrent;
        }
        pCurrent = pCurrent->pNext;
    }

    return NULL;
}

/* Motion_ItemsInit
* @param[in]            pTarget
* @return               void
*/
void Motion_ItemsInit(SeqItem* pTarget) {
    SeqItem* ptrSeq = pTarget;

    while (ptrSeq != NULL) {
        if (strcasecmp(ptrSeq->szType, "PosCntrl") == 0 ||
            strcasecmp(ptrSeq->szType, "ForceTrigCntrl") == 0 ||
            strcasecmp(ptrSeq->szType, "PosOrForceTrig") == 0 ||
            strcasecmp(ptrSeq->szType, "ForceLoopCntrl") == 0 ||
            strcasecmp(ptrSeq->szType, "SlopeTrigCntrl") == 0)
        {

            InitItem(ptrSeq);
            printf("ExitParentNestedStructures  0 InitItem Index:%d  szType:%s\n", ptrSeq->szType);
        }
        //ptrSeq->iExecuteCounter = 0;
        //ptrSeq->bFunctionDisable = 0;

        if (ptrSeq->pChild != NULL) {
            Motion_ItemsInit(ptrSeq->pChild);
        }
        ptrSeq = ptrSeq->pNext;
    }
}



/* IsItemInSubtree      
* @param[in]            SeqItem* pRoot
* @param[in]            SeqItem* pTarget
* @return               ErrorInfo
*/
int IsItemInSubtree(SeqItem* pRoot, SeqItem* pTarget) 
{
    SeqItem* pCurrent = pRoot;
    while (pCurrent != NULL) 
    {
        if (pCurrent == pTarget) 
            return 1; 
        if (pCurrent->pChild != NULL && IsItemInSubtree(pCurrent->pChild, pTarget)) 
        {
            return 1;
        }
        pCurrent = pCurrent->pNext;
    }
    return 0; 
}


/* ExitParentNestedStructures      
* @param[in]            SeqRef* pSeqRef
* @param[in]            SeqItem* pTargetItem
* @return               ErrorInfo
*/

void ExitParentNestedStructures(SeqItem* pJumpItem, SeqItem* pTargetItem) {
    SeqItem* pParent = pJumpItem->pParent;
    while (pParent != NULL) 
    {
        if (IsItemInSubtree(pParent, pTargetItem) == 0) 
        { 
            //标记父节点结束子流程
            if (strcmp(pParent->szType, "While") == 0 ||
                strcmp(pParent->szType, "If") == 0 ||
                strcmp(pParent->szType, "ElseIf") == 0 ||
                strcmp(pParent->szType, "Else") == 0)
            {
                pParent->bCanGoInchild = 0; 
                pParent->bFunctionDisable = 0;
                pParent->iExecuteCounter = 0;
            }
        }
        pParent = pParent->pParent;
    }
}

//static const char* BranchStepDesc[] = { "Branch_Step_Ready","Branch_Step_Body", "Branch_Step_Finished"};

/* SeqBranchUpdate      闂佸湱鐟抽崱鈺傛杸Branch
* @param[in]            SeqRef* pSeqRef
* @param[in]            sSeqRef_Branch* pBranch
* @return               ErrorInfo
*/
ErrorInfo SeqBranchUpdate(SeqRef* pSeqRef,sSeqRef_Branch* pBranch)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));
    if (pBranch->pCurrItem != NULL) 
    {
        if (pBranch->pCurrItem != pBranch->pLastItem)
        {
            //dbgPrint(1, 1, "111  iRowIndex:%d\n", pBranch->pCurrItem->iRowIndex);
            pSysShareData->SeqAux.iActiveRowIndex = pBranch->pCurrItem->iRowIndex;
            pBranch->pLastItem = pBranch->pCurrItem;
        }
 
        ExeActor* pExeActor = (ExeActor*)pBranch->pCurrItem->pExeActor;
        if (pExeActor != NULL) 
        {
            rAbortItemTrig.bTrigSignal = pSeqRef->bAbortCurrentItem;
            R_Trig(&rAbortItemTrig);
            if (rAbortItemTrig.bTrig)
            {
                if (pBranch->pCurrItem->eItemType == Motion)
                {
                    SeqItem_MotionCfg* pMotion = (SeqItem_MotionCfg*)pBranch->pCurrItem->pCfg;
                    MC_Stop(&pMotion->pAxisMc->Axis);
                }
                SeqExeItemExit(pBranch->pCurrItem->pExeActor, errorInfo);
                pSeqRef->bHadAbortCurrentItem = true;
            }
            else
            {
                errorInfo = pExeActor->Execute(pBranch->pCurrItem);
                if (errorInfo.ErrCode && !gIgnoreTimeoutError)
                {
                    pBranch->iActiveRowIndex = -2;
                }
                else
                {
                    if (!pExeActor->bBusy)
                    {
                        //rt_dbgPrint(1, 0, "pExeActor->bBusy..11111.\n");
                        //   pBranch->pCurrItem->iExecuteCounter = pBranch->pCurrItem->iExecuteCounter + 1;   //New闂佹眹鍔岀�氼厼顪冮崒鐐茬９婵炲棙甯炵粻鐑芥⒒閸ラ攱瀚归柣鐔告磻濞村洨绮婇幘顔肩９闂傚倻顥愮粈锟�0
                        if (pExeActor->iJumpTarget >= 0) {
                            //查找目标节点
                            SeqItem* pTargetItem = FindItemByRowIndex(pBranch, pExeActor->iJumpTarget);
                            if (!pTargetItem)
                            {
                                errorInfo.ErrCode = 25020;     //jump跳转目标工步异常
                                return errorInfo;
                            }

                            //如果有if while等嵌套结构 
                            ExitParentNestedStructures(pBranch->pCurrItem, pTargetItem);
                            Motion_ItemsInit(pTargetItem);  //这里简单的就从TargetItem开始向下全部都reset一下

                            pBranch->pCurrItem = pTargetItem;
                            pBranch->iActiveRowIndex = pTargetItem->iRowIndex;
                            //printf("getNextItem Type: %s\n", pBranch->pCurrItem->szType);
                            //printf("getNextItem iRowIndex: %d\n", pBranch->pCurrItem->iRowIndex);
                            //初始化新节点
                            errorInfo = StartExeItem(pTargetItem);
                        }
                        else if (pExeActor->iJumpTarget == -1)
                        {
                            //无跳转，正常执行到下一节点
                            pBranch->pCurrItem = getNextItem(pBranch->pCurrItem);
                            //rt_dbgPrint(1, 0, "getNextItem... pCurrItem:%d\n",pBranch->pCurrItem);
                            if (pBranch->pCurrItem) pBranch->iActiveRowIndex = pBranch->pCurrItem->iRowIndex;
                            if (pBranch->pCurrItem) pBranch->iQuitRowIndex = pBranch->pCurrItem->iRowIndex;
                            errorInfo = StartExeItem(pBranch->pCurrItem);
                        }
                        else
                        {
                            pBranch->iActiveRowIndex = -2;

                            errorInfo.ErrCode = 24073;
                            // return 0x40D3;
                        }
                    }
                }
            }
        } 
        else if (pBranch->pCurrItem->pFlowActor != NULL) 
        {
           // rt_dbgPrint(1, 1, "1112111\n");
            pBranch->pCurrItem = getNextItem((SeqItem*)pBranch->pCurrItem);
            errorInfo = StartExeItem(pBranch->pCurrItem);
            if(pBranch->pCurrItem)
            {
                pBranch->iActiveRowIndex = pBranch->pCurrItem->iRowIndex;                      //dbgPrint(1, 1, "iRowIndex:%d\n", pBranch->pCurrItem->iRowIndex);
                pBranch->iQuitRowIndex = pBranch->pCurrItem->iRowIndex;
            }
            else
                pBranch->iActiveRowIndex = -1;
        }
    } 
    else 
    {
        pBranch->iActiveRowIndex = -1;
    }

    ErrorInfoPack(&errorInfo, "SeqBranchUpdate", "");
    return errorInfo;
}


ErrorInfo SeqStart(SeqRef* pSeqRef)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0,  (uint64)sizeof(ErrorInfo));

    pSeqRef->bPauseRequest = false;
    bool bAllHasPowerUp = true;
    pSeqRef->bMeasurementStart = false;
    if (SYSTEM_MOTION_MODE)
    {
        for (int i = 0; i < AxisNum; i++) {
            bAllHasPowerUp &= MC_IsPowerUp(&pSeqRef->pAxisMc[i].Axis);

            pSeqRef->pAxisMc[i].CtrlPara.bLastCmdIsPos = false; //闂佹寧绋愮欢銈囨閹烘鏅☉鏃傤仺f, 婵炴垶鎸哥粔鎾箖閹剧粯鍎嶉柛鏇ㄥ亞鐎瑰鏌ｉ埀顒�濮�缂佺粯鐗犻弻灞界暆閿熻棄鈻撻幋锕�绀嗘繝闈涙－濞兼鏌涢弽銊уⅶ缂佹顦扮粙澶愭晸娴犲鍐�闁跨噦鎷�
            pSeqRef->pAxisMc[i].CtrlPara.dLastTargetPos = 0;
            pSeqRef->pAxisMc[i].CtrlPara.dLastTargetForce = 0;
        }
    }

    pSysShareData->SeqAux.iActiveRowIndex = -1;
    pSysShareData->SeqAux.iErrorRowIndex = -1;
    pSeqRef->bHadAbortCurrentItem = false;
    errorInfo = SeqInit(pSeqRef);
    ErrorInfoPack(&errorInfo, "SeqBranchUpdate", "");
    return errorInfo;
}


int16 CheckCmdResult(SeqRef* pSeqRef){
    int sum = 0;
    int ret = 0;
    for (int i = 0; i < AxisNum; i++) {
        ret = MC_CmdExeResult(&pSeqRef->pAxisMc[i].Axis);
        if (ret >= 0)
            sum += ret;
        else {
            sum = -1;
            break;
        }
    }
    return  sum;
}



bool AllServoArePowerOn(SeqRef* pSeqRef){
    bool bAllHasPowerUp = true;
    for(int i=0;i< AxisNum;i++){
        bAllHasPowerUp = bAllHasPowerUp && MC_IsPowerUp(&pSeqRef->pAxisMc[i].Axis);// pSeqRef->pAxisMc[i].Axis.PowerUpAlready;
    }
    return  bAllHasPowerUp;
}


bool IsAllServoMoveCmdFinished(SeqRef* pSeqRef){
    bool bAllFinised = true;
    for(int i=0;i< AxisNum;i++){
        bAllFinised = bAllFinised && MC_MoveCmdFinished(&pSeqRef->pAxisMc[i].Axis);// pSeqRef->pAxisMc[i].Axis.PowerUpAlready;
    }
    return  bAllFinised;
}

static const char* SeqStateDesc[] = {"Seq_Step_Init","Seq_Step_RefreshPara", "Seq_Step_Body", "Seq_Step_ExeRequest", "Seq_Step_Finished"};
static const char* SeqBodyDesc[] =  {"Seq_Body_Check","Seq_Body_Execute", "Seq_Body_Hold", "Seq_Body_GoHome", "Seq_Body_Finish"};


uint8 iStepGoHome = 0;
bool bFindGoHomeItem = false;
ExeActor* pHomeExeActor;
_SeqItem* pHomeItem;
WaitTimeContext     sGoHomeWaitTimeContext;
ErrorInfo RequestGoHomeExecute(SeqRef* pSeqRef,bool *pbGoHomeFinish)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0,  (uint64)sizeof(ErrorInfo));
    uint64 currTimestamp;
    switch (iStepGoHome)
    {
    case 0:
    {
        if (pSeqRef->bMeasurementStart)
            pSeqRef->bMeasurementStart = false;
        *pbGoHomeFinish = false;
        if (pSeqRef->pAxisMc[0].Axis.Busy)
        {
            MC_Stop(&pSeqRef->pAxisMc[0].Axis);// sSysAvaliablePara.fAccMax);
            iStepGoHome = 1;
        }
        else
        {
            iStepGoHome = 10;
        }
    }
        break;
    case 1:
    {
        if (!pSeqRef->pAxisMc[0].Axis.Busy)
        {
            iStepGoHome = 10;
        }
    }
    break;
    case 10:
    {
        sSeqRef_Branch* pBranch = pSeqRef->vpBranch[0];
        bFindGoHomeItem = false;
        pHomeItem = NULL;
        pHomeExeActor = NULL;
        do
        {
            if (pBranch->pCurrItem->eItemType == Motion)
            {
                SeqItem_MotionCfg* pMotion = (SeqItem_MotionCfg*)pBranch->pCurrItem->pCfg;
                if (pMotion->eMotion_Mode == Motion_GoHome)
                {
                    dbgPrint(1, 1, "*****************RequestGoHomeExecute  Find GoHome Item *****************\n");
                    pHomeItem = pBranch->pCurrItem;
                    bFindGoHomeItem = true;
                    break;
                }
 
            }
            pBranch->pCurrItem = getNextItem(pBranch->pCurrItem);
        }while(pBranch->pCurrItem != NULL);

        if (bFindGoHomeItem)
        {
            SeqItem_MotionCfg* pMotion = (SeqItem_MotionCfg*)pHomeItem->pCfg;
            if (abs(pSysShareData->sSysFbkVar.fPosFbk - pMotion->CmdCfg.GoHome.dHomePos.VarUnion.fVar) <= 0.001)
            {
                iStepGoHome = 100;
            }
            else
            {
                pHomeExeActor = (ExeActor*)pHomeItem->pExeActor;
                if (pHomeExeActor != NULL)
                {
                    iStepGoHome = 11;
                    errorInfo = StartExeItem(pHomeItem);
                }
            }
        }
    }
    break;
    case 11:
    {
        errorInfo = SeqItem_Motion_Execute(pHomeItem);//pHomeExeActor->Execute(pHomeItem);
        if (errorInfo.ErrCode)
        {
            if(pSeqRef->pAxisMc[0].Axis.Busy)
                MC_Stop(&pSeqRef->pAxisMc[0].Axis);// sSysAvaliablePara.fAccMax);
            iStepGoHome = 12;
        }
        else
        {
            if (!pHomeExeActor->bBusy)
                iStepGoHome = 100;
        }
           
    }
    break;
    case 12:
        if (!pSeqRef->pAxisMc[0].Axis.Busy)
        {
            sGoHomeWaitTimeContext.bHadInitTime = false;
            clock_gettime_nS(&sGoHomeWaitTimeContext.initTimestamp);
            iStepGoHome = 13;
        }
        break;
    case 13:
    {
        clock_gettime_nS(&sGoHomeWaitTimeContext.currTimestamp);
        if ((sGoHomeWaitTimeContext.currTimestamp - sGoHomeWaitTimeContext.initTimestamp) >= (1000000UL * 50))  //20ms
        {
            iStepGoHome = 100;
        }
    }
        break;
    case 100:
        *pbGoHomeFinish = true;
        break;
    default:
        break;
    }

    ErrorInfoPack(&errorInfo, "RequestGoHomeExecute", "");
    return errorInfo;
}

/* RequestExecute              //闁瑰吋娼欑换鎰板垂椤忓牆绠ョ憸鎴︺�侀敓锟�
* @param[in]     pSeqRef
* @return        ErrorCode
*/
ESeqRequestAction eStateRequest;
ESeqRequestAction eLastStateRequest;
bool bSendStopCmd;
bool bMotorStandStill = false;
bool bGoHomeFinish = false;
void RequestExecute(SeqRef* pSeqRef,RequestsAction* psRequestCmd , ExecuteFeedback *psExeResult)
{
    if (!psExeResult->bExeDataHadNotReset)
    {
        psExeResult->bExeDataHadNotReset = true;
        memset(&psExeResult->ExeErrInfo,0, (uint64)sizeof(ErrorInfo));
        psExeResult->bExecuteEnd = false;
        bSendStopCmd = false;
        bMotorStandStill = false;
        eStateRequest = Seq_RequestAction_PauseOrStop;
        eLastStateRequest = Seq_RequestAction_PauseOrStop;
    }
   
    switch (eStateRequest)
    {
    case  Seq_RequestAction_PauseOrStop:
        if (psRequestCmd->Pause || psRequestCmd->Stop)       //bEoTunnelStopMove  已经先停掉了
        {
            if (psRequestCmd->Stop && pSeqRef->bMeasurementStart)
                pSeqRef->bMeasurementStart = false;

            if (SYSTEM_MOTION_MODE)
            {
                if (MC_IsPowerUp(&pSeqRef->pAxisMc[0].Axis) && !MC_IsServoInError(&pSeqRef->pAxisMc[0].Axis))
                {
                    if (!pSeqRef->pAxisMc[0].Axis.Busy)      //缂備焦绋戦ˇ鍗烆焽閸愵喗鍋ㄩ柛顭戝亝缁ㄦ艾鈽夐幘宕囆㈠┑顔规櫆缁傚秹鏁撻敓锟�
                    {
                        if (psRequestCmd->GoHome)
                            eStateRequest = Seq_RequestAction_GoHome;
                        else if (psRequestCmd->PowerDown)
                            eStateRequest = Seq_RequestAction_PowerOff;
                        else if (psRequestCmd->ShowError)
                            eStateRequest = Seq_RequestAction_Error;
                        else
                            eStateRequest = Seq_RequestAction_Finish;
                    }
                }
                else
                {
                    if (psRequestCmd->ShowError)
                        eStateRequest = Seq_RequestAction_Error;
                    else
                        eStateRequest = Seq_RequestAction_Finish;
                }
            }
            else
            {
                if (psRequestCmd->ShowError)
                    eStateRequest = Seq_RequestAction_Error;
                else
                    eStateRequest = Seq_RequestAction_Finish;
            }
        }
        else
        {
            eStateRequest = Seq_RequestAction_GoHome;
        }
        bGoHomeFinish = false;
        iStepGoHome = 0;
        break;
    case  Seq_RequestAction_GoHome:
        if (SYSTEM_MOTION_MODE)
        {
            if (psRequestCmd->GoHome)
            {
                // 闂佸湱鐟抽崱鈺傛杸闂佹悶鍎抽崑娑氭暜椤愶附鍊烽柛锔诲幗閻ｉ亶鏌熺粙鎸庣煑闁硅鎷�
                RequestGoHomeExecute(pSeqRef, &bGoHomeFinish);
                if (bGoHomeFinish)        //闂佺儵鏅滈崹鍓佹暜閸洖鍙婃い鏍ㄧ閸庡﹪鏌涢妷褍浠滈柛鈺佹湰缁傚秹宕卞Δ锟介弬褔鏌ｉ幇顔筋仧缂佽鲸绻堥幆鍕箣閻愯弓绮梺鍦焾濞诧綁骞庨妶澶婂強妞ゆ牗纰嶉崕濠囨煕鐎ｎ偆鐭嗙紒顭掓嫹
                {
                    eStateRequest = Seq_RequestAction_PowerOff;
                }
            }
            else
            {
                eStateRequest = Seq_RequestAction_PowerOff;
            }
        }
        break;
    case  Seq_RequestAction_PowerOff:
        if (psRequestCmd->PowerDown)
        {
            MC_PowerDown(&pSeqRef->pAxisMc[0].Axis, &sPowerDownExefeed);
            if(sPowerDownExefeed.bExecuteEnd)
                eStateRequest = Seq_RequestAction_Error;
        }
        else
        {
            eStateRequest = Seq_RequestAction_Error;
        }
        break;
    case   Seq_RequestAction_Error:
        if (!psRequestCmd->ShowError)
        {
            memset((ErrorInfo*)&psRequestCmd->sErrInfo,0 ,  (uint64)sizeof(ErrorInfo));
            memset(&SeqErrInfo, 0,  (uint64)sizeof(ErrorInfo));
        }
        else
        {
            if(!SeqErrInfo.ErrCode)
                memcpy(&SeqErrInfo, (ErrorInfo*)&psRequestCmd->sErrInfo,  (uint64)sizeof(ErrorInfo));
        }
        eStateRequest = Seq_RequestAction_Finish;
      //  break;                    //闂佸湱绮崝娆撴偟椤曪拷瀵偊宕奸姀鐘哄У
    case  Seq_RequestAction_Finish:
        if (psRequestCmd->Stop)             
        {
            if (pSeqRef->bMeasurementStart)
                pSeqRef->bMeasurementStart = false;

            pSeqRef->bSequenceStoped = true;
            pSeqRef->bSequencePaused = false;
        }
        else if (psRequestCmd->Pause)
        {
            pSeqRef->bSequencePaused = true;
            pSeqRef->bSequenceStoped = false;
        }

        psExeResult->bExeDataHadNotReset = false;           //闂佸搫鍊块敓鑺ョ〒閳规帒鈽夐幘鎰佸剱妞ゆ劕銈搁獮宥堫樁妞ゃ垺鍨垮顕�骞嗛弶鎸庮唶闂佸憡鏌ｉ崝宀勊囬崣澶嬪鐎广儱鎳庡Λ姗�鏌℃笟濠冨
        psExeResult->bExecuteEnd = true;
        break;
    default:
        psExeResult->bExeDataHadNotReset = false;           //闂佸搫鍊块敓鑺ョ〒閳规帒鈽夐幘鎰佸剱妞ゆ劕銈搁獮宥堫樁妞ゃ垺鍨垮顕�骞嗛弶鎸庮唶闂佸憡鏌ｉ崝宀勊囬崣澶嬪鐎广儱鎳庡Λ姗�鏌℃笟濠冨
        psExeResult->bExecuteEnd = true;
        break;
    }

    if (eStateRequest != eLastStateRequest)
    {
        //dbgPrint(1, 1, "eLastStateRequest[%d]-> eStateRequest[%d]\n", eLastStateRequest,eStateRequest);
        eLastStateRequest = eStateRequest;
    }
}



/* SequenceRunCheck              //闁瑰吋娼欑换鎰板垂椤忓牆绠ョ憸鎴︺�侀敓锟�
* @param[in]     pSeqRef
* @return        ErrorCode
*/
void SequenceRunCheck(SeqRef* pSeqRef)
{
    if (SYSTEM_MOTION_MODE)
    {
        if (!MC_IsPowerUp(&pSeqRef->pAxisMc[0].Axis))
        {
            memset(&sRequestAction, 0,  (uint64)sizeof(sRequestAction));
            pSeqRef->eRunStep = Seq_Step_ExeRequest;
            sRequestAction.ShowError = true;
            if (!pSeqRef->bStopRequest)
            {
                //婵犵锟藉啿锟藉綊鎮樻径濞炬煢闁斥晛鍟粻鎺戔槈閹剧鍔熼柡浣规倐閹啴宕熼鐐枔闂傚倸娴勯幏鐑芥偡閺囨氨鍔嶅┑顔哄�濋弻銊モ枎韫囨艾鐝梻渚囧亾閹风兘鏌涢幋顖涘
                sRequestAction.Stop = false;
                sRequestAction.Pause = true;

            }
            else
            {
                sRequestAction.Stop = true;
                sRequestAction.Pause = false;
            }

        }
    }
}

/* SequenceReset              //闁瑰吋娼欑换鎰板垂椤忓牆鐭楅柛灞剧♁濞堣泛顭跨捄铏剐＄紓宥忔嫹
* @param[in]     pSeqRef
* @return        ErrorCode
*/
void SequenceReset(SeqRef* pSeqRef)
{
    pSysShareData->SeqAux.iErrorRowIndex = -1;
    pSeqRef->eRunStep = Seq_Step_RefreshPara;
    pSeqRef->bPauseRequest = false;
    pSeqRef->bSequenceStoped = false;
    pSeqRef->bSequencePaused = false;
    pSeqRef->WaitSingal = 0;
    pSeqRef->ContinueSingal = 0;
}

void SequenceInit(SeqRef* pSeqRef)
{
    pSysShareData->SeqAux.iErrorRowIndex = -1;
    pSeqRef->eRunStep = Seq_Step_Init;
    pSeqRef->bPauseRequest = false;
    pSeqRef->bSequenceStoped = false;
    pSeqRef->bSequencePaused = false;
    pSeqRef->WaitSingal = 0;
    pSeqRef->ContinueSingal = 0;
    pSeqRef->bMeasurementStart = false;
}

void GroupItemsInit(SeqItem* pOriginSeqRef) {
    SeqItem* ptrSeq = pOriginSeqRef;

    while (ptrSeq != NULL) {
        // 闂佸憡甯楃换鍌烇綖閹版澘绀岄柡宓懐歇闂佸憡鎸哥粔閿嬩繆椤撱垺鍊烽柨鐕傛嫹
        ptrSeq->iExecuteCounter = 0;
        ptrSeq->bFunctionDisable = 0;

        // 闂備緡鍋呯敮鎺旂礊婵犲洤绀嗘繝闈涙－濞兼鏌涢弽銊уⅹ闁烩剝鐟╅幊鐐哄磼濠婂啩鍖�
        if (ptrSeq->pChild != NULL) {
            GroupItemsInit(ptrSeq->pChild);
        }
        // 缂備礁顦抽褎鎱ㄩ埡鍛濡鑳堕悷鎾斥槈閹垮嫭瀚规繛鎴炴惄娴滐絾淇婇銏″�烽柨鐕傛嫹
        ptrSeq = ptrSeq->pNext;
    }
}

/* SequenceExecute              //闁瑰吋娼欑换鎰板垂椤忓牆绠ョ憸鎴︺�侀敓锟�
* @param[in]     pSeqRef
* @return        ErrorInfo
*/
TrigData   rExeRequest;
TrigData   rExeStop;
//TrigData   rEoTunnelStop;
ErrorInfo SequenceExecute(SeqRef* pSeqRef) 
{
    static bool Seq_Run = 0;
    bool bNodeFinished, bAllBranchFinished = false;
    switch (pSeqRef->eRunStep)
    {
    case Seq_Step_Init:     //  一个工艺运行过程中 只Init一次
    {
        // dbgPrint(1, 1, "*****************\ Seq_Step_Init *****************\n");
        memset(&SeqErrInfo, 0, (uint64) sizeof(ErrorInfo));
        SeqErrInfo = SeqStart(pSeqRef);
        if (!SeqErrInfo.ErrCode)
        {
            pSysShareData->SeqAux.iErrorRowIndex = -1;

            SeqErrInfo = BranchStart(pSeqRef);
            if (!SeqErrInfo.ErrCode)
            {
                pSeqRef->bBusy = true;
                pSeqRef->eRunStep = Seq_Step_RefreshPara;
                pSeqRef->eRunStepInBody = Seq_Body_Check;
            }
            else
            {
                pSeqRef->bBusy = false;
                pSeqRef->eRunStep = Seq_Step_Finished;
            }
        }
        else
        {
            pSeqRef->bBusy = false;
            pSeqRef->eRunStep = Seq_Step_Finished;
        }
    }
        break;
    case Seq_Step_RefreshPara:
    {
        memset(&SeqErrInfo, 0,  (uint64)sizeof(ErrorInfo));
        memset(&sRequestAction, 0,  (uint64)sizeof(sRequestAction));
        GroupItemsInit(pSeqRef->vpBranch[0]->pFirstItem);
        ResetBranchErrorInfo(pSeqRef->vpBranch[0]);
        pSeqRef->bBusy = true;
        pSeqRef->eRunStepInBody = Seq_Body_Check;
        pSeqRef->eRunStep = Seq_Step_Body;
    }
        break;
    case Seq_Step_Body:
    {
        SequenceRunCheck(pSeqRef);              //
        switch (pSeqRef->eRunStepInBody)
        {
            case Seq_Body_Check:     // when restore from pause, need jump here first.
            {
                if (pSeqRef->bStopRequest)
                {
                    sRequestAction.Stop = true;
                    sRequestAction.Pause = false;
                    pSeqRef->eRunStep = Seq_Step_ExeRequest;
                    if (SYSTEM_MOTION_MODE)
                    {
                        dbgPrint(1, 0, "!! bStopRequest Seq_Body_Check \n\n");
                        MC_Stop(&pSeqRef->pAxisMc[0].Axis);   //优先暂停防止过冲
                    }
                }
                else
                {
                    sSeqRef_Branch* pBranch = pSeqRef->vpBranch[0];
                    SeqErrInfo = StartExeItem(pBranch->pCurrItem);
                    if (SeqErrInfo.ErrCode == 0)
                    {
                        pSeqRef->eLastRunStepInBody = Seq_Body_Check;
                        pSeqRef->eRunStepInBody = Seq_Body_Execute;
                    }
                    else
                    {
                        pSysShareData->SeqAux.iErrorRowIndex = pBranch->pCurrItem->iRowIndex;
                       // dbgPrint(1, 1, "3 iErrorRowIndex:%d\n", pSysShareData->SeqAux.iErrorRowIndex);
                        sRequestAction.Pause = true;
                        sRequestAction.Stop = false;
                        sRequestAction.ShowError = true;
                        sRequestAction.sErrInfo = SeqErrInfo;
                        pSeqRef->eRunStep = Seq_Step_ExeRequest;
                        pSeqRef->eRunStepInBody = Seq_Body_Hold;
                    }
                }
            }
                break;
            case Seq_Body_Execute:
            {
                if (pSeqRef->bStopRequest)
                {
                    dbgPrint(1, 0, "!! bStopRequest \n\n");
                    sRequestAction.Stop = true;
                    sRequestAction.Pause = false;
                    pSeqRef->eRunStep = Seq_Step_ExeRequest;
                    if(SYSTEM_MOTION_MODE)
                        MC_Stop(&pSeqRef->pAxisMc[0].Axis);   //优先暂停防止过冲
                }
                else if (pSeqRef->bPauseRequest || (SYSTEM_MOTION_MODE && pSeqRef->pAxisMc->Axis.Motor.LinkVar.ErrCode))         //闂佸搫妫楅崐鍛婄閻樺灚瀚氶梺鍨儑濠�鎾煙鐎涙澧遍柡浣规倐瀵潧煤椤忓懏娅撻柣鐘叉祫閹凤拷
                {
                    sRequestAction.Pause = true;
                    sRequestAction.Stop = false;
                    dbgPrint(1, 0, "!! bPauseRequest \n\n");
                    pSeqRef->eRunStep = Seq_Step_ExeRequest;
                    if (SYSTEM_MOTION_MODE)
                    {
                        MC_Stop(&pSeqRef->pAxisMc[0].Axis);   //优先暂停防止过冲
                        if (pSeqRef->pAxisMc->Axis.Motor.LinkVar.ErrCode != 0)
                        {
                            sRequestAction.ShowError = true;
                            sRequestAction.sErrInfo = MC_ServoErrorCode(&pSeqRef->pAxisMc->Axis);
                            dbgPrint(1, 0, "!! Run Sequence Motor Error!ErrorCode:%d\n", pSeqRef->pAxisMc->Axis.Motor.LinkVar.ErrCode);
                        }
                    }
                }
                else if (pSeqRef->bEoTunnelTrig)
                {
                    //printf("bEoTunnelTrig\n\n");
                    sRequestAction = pSeqRef->vpBranch[0]->sSqErrAction.EOTrigAction;
                    sRequestAction.sErrInfo.ErrCode = 5650;// 210;
                    sRequestAction.sErrInfo.eErrLever = Error;
                    pSeqRef->eRunStep = Seq_Step_ExeRequest;
                    pSeqRef->bEoTunnelTrig = false;
                }
                else
                {
                    for (int i = 0; i < pSeqRef->nBranchNum; i++)
                    {
                        sSeqRef_Branch* pBranch = pSeqRef->vpBranch[i];
                        MotionLimitRtCheck(pBranch->pCurrItem, &sRequestAction);
                        if (!sRequestAction.sErrInfo.ErrCode)
                        {
                            SeqErrInfo = SeqBranchUpdate(pSeqRef, pBranch);
                            if (SeqErrInfo.ErrCode)
                            {
                                pSysShareData->SeqAux.iErrorRowIndex = pBranch->pCurrItem->iRowIndex;
                                if (SYSTEM_MOTION_MODE)
                                {
                                    sRequestAction.Pause = true;
                                    sRequestAction.Stop = false;
                                }
                                else
                                {
                                    sRequestAction.Pause = false;
                                    sRequestAction.Stop = true;
                                }
                                sRequestAction.ShowError = true;
                                sRequestAction.sErrInfo = SeqErrInfo;
                                pSeqRef->eRunStep = Seq_Step_ExeRequest;
                                pSeqRef->eRunStepInBody = Seq_Body_Hold;
                                break;
                            }
                        }
                        else
                        {
                            pSysShareData->SeqAux.iActiveRowIndex = pBranch->pCurrItem->iRowIndex;
                            pSysShareData->SeqAux.iErrorRowIndex = pBranch->pCurrItem->iRowIndex;

                            dbgPrint(1, 1, "2 gSysCounter:%lld iErrorRowIndex:%d\n", 
                                pSysShareData->gSysCounter,
                                pSysShareData->SeqAux.iErrorRowIndex);
                            pSeqRef->eRunStep = Seq_Step_ExeRequest;
                            pSeqRef->eRunStepInBody = Seq_Body_Hold;
                            break;
                        }

                        pSeqRef->BranchActiveRowIndex[i] = pBranch->iQuitRowIndex;//wcl
                        if (pBranch->pCurrItem == NULL)
                        {
                            pSeqRef->eRunStepInBody = Seq_Body_GoHome;
                        }
                    }
                }
            }
                break;
            case  Seq_Body_Hold:
                break;
            case  Seq_Body_GoHome:
            {
                if (1)//(bHome)
                {
                    pSeqRef->eRunStepInBody = Seq_Body_Finish;
                }
            }
                break;
            case Seq_Body_Finish:
                pSeqRef->eRunStep = Seq_Step_Finished;
                break;
            default:
                break;
        }

        //if (pSeqRef->eRunStepInBody != pSeqRef->eLastRunStepInBody)
        //{
        //   dbgPrint(1, 1, "SeqBodyState:[%s ->> %s] ErrCode:%d\n", 
        //       SeqBodyDesc[pSeqRef->eLastRunStepInBody -0x30], 
        //       SeqBodyDesc[pSeqRef->eRunStepInBody - 0x30],
        //       SeqErrInfo.ErrCode);

        //    pSeqRef->eLastRunStepInBody = pSeqRef->eRunStepInBody;
        //}
    }
        break;
    case  Seq_Step_ExeRequest:        // Action闂佸搫瀚烽崹鍗烇耿娓氾拷楠炲秷顦舵い銏″灦閵囧嫰宕崟顓狀暡闂佹椿鐓夐幏锟�      1闂佹寧绋戦悧濠傗枔韫囨稑纾绘繝褍灏呴幏鐑芥晸閿燂拷 缂傚倷鐒﹂幐璇差焽椤愶附鏅鑸电〒楠烇拷2 闂佹悶鍎抽崑娑氭暜椤愶附鍊烽柨鐕傛嫹 3 婵炴垶鎸搁鍥极閿燂拷  4闂佺缈伴崕鐢稿极閿燂拷   闂佸湱鐟抽崱鈺傛杸婵炴垶鎼幏宄扳槈閹垮啩绨奸柛銊ｅ姂閹虫捇鏁撻敓锟�4婵炴垶鎼╂禍婊勬叏閳哄倹濯撮柨鐕傛嫹
    {
        RequestExecute(pSeqRef, &sRequestAction, &sExecuteRequestFeedback);
        if (sExecuteRequestFeedback.bExecuteEnd)
        {
            pSeqRef->eRunStep = Seq_Step_Finished;
            sExecuteRequestFeedback.bExeDataHadNotReset = false;
        }
    }
        break;
    case Seq_Step_Finished:
    {
        if (SYSTEM_MOTION_MODE)
        {
            if (!pSeqRef->pAxisMc->Axis.Busy)
            {
                if (!pSeqRef->bSequencePaused)
                {
                    pSeqRef->bBusy = false;
                }
            }
        }
        else
        {
            if (!pSeqRef->bSequencePaused)
            {
                pSeqRef->bBusy = false;
            }
        }

        if (pSeqRef->bStopRequest)
            pSeqRef->bStopRequest = false;
    }
        break;
    default:
        break;
    }

    if (pSeqRef->eRunStep != pSeqRef->eLastState)
    {
        //dbgPrint(1, 0, "SeqState:[%s ->> %s]\n", SeqStateDesc[pSeqRef->eLastState], SeqStateDesc[pSeqRef->eRunStep]);
        pSeqRef->eLastState = pSeqRef->eRunStep;
    }

    rExeRequest.bTrigSignal = sRequestAction.Pause;// (pSeqRef->eRunStep == Seq_Step_ExeRequest ? true : false);
    R_Trig(&rExeRequest);

    rExeStop.bTrigSignal = sRequestAction.Stop;// (pSeqRef->eRunStep == Seq_Step_ExeRequest ? true : false);
    R_Trig(&rExeStop);
    if (rExeRequest.bTrig || rExeStop.bTrig)
    {
        dbgPrint(1, 0, "!! rExeRequest:%s  rExeStop:%s \n\n",
            rExeRequest.bTrig?"true":"false",
            rExeStop.bTrig ? "true" : "false");

        if (MC_IsPowerUp(&pSeqRef->pAxisMc[0].Axis) && !MC_IsServoInError(&pSeqRef->pAxisMc[0].Axis) && pSeqRef->pAxisMc[0].Axis.Busy)
        {
            MC_Stop(&pSeqRef->pAxisMc[0].Axis);   //优先暂停防止过冲
            //printf("\nSeq_Body_Execute MC_Stop Counter:%lld  Pos:%f Force:%f Vel:%f\n\n",
            //    pSysShareData->gSysCounter,
            //    pSysShareData->sSysFbkVar.fPosFbk,
            //    pSysShareData->sSysFbkVar.fSenosrVarFbk,
            //    pSysShareData->sSysFbkVar.fVelFbk);
        }
    }

    ErrorInfoPack(&SeqErrInfo, "SequenceExecute", "");
    return  SeqErrInfo;
}


void SeqCopy(SeqRef* pDst, SeqRef* pSrc){//!!!!pxf
    if(pDst != 0 && pSrc!= 0){
    	memcpy(pDst, pSrc,  (uint64)sizeof(SeqRef));
    }
}


