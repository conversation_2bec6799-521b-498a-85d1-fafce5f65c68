﻿#include "MainWork.h"
#include "FSM.h"
#include "SystemProtect.h"
#include "ChannelAndChart.h"
#include "FieldbusThread.h"
#include "FieldbusAux.h"
#include "SysShareMemoryDefine.h"
#include "SysVarDefine.h"
#include "SeqItemMotion.h"
#include "SysTest.h"
#include "ServerComm.h"
#include "base.h"
#include "SetPara.h"

/* ChangeWord       字的 顺序转换
* @param[in]        byte* pWordData
* @return           void
*/
byte TmpByte = 0;
byte* pTmpByte = NULL;
void ChangeWord(byte* pWordData)
{
	TmpByte = *pWordData;
	*pWordData = *(pWordData + 1);

	pTmpByte = pWordData + 1;
	*pTmpByte = TmpByte;
}

//sSFCW					SCCW;       //客户端的控制CW数据
//sSFCW					SFCW;       //总线的的控制CW数据



uint32 uCounter = 0;
void IOStatus()
{
	pSysShareData->SISW.sSiSW.IoRemoted = pSysShareData->sExSW.bFieldBusRemoted;
	pSysShareData->SISW.sSiSW.FaultState = pSysShareData->sExSW.bSystemFault;
	pSysShareData->SISW.sSiSW.PowerState = pSysShareData->sExSW.bPowerState;
	pSysShareData->SISW.sSiSW.ResultOK = pSysShareData->sExSW.bResultOK;
	pSysShareData->SISW.sSiSW.ResultNOK = pSysShareData->sExSW.bResultNOK;
	pSysShareData->SISW.sSiSW.SequenceEnd = pSysShareData->sExSW.bSequenceEnd;
	pSysShareData->SISW.sSiSW.SequenceRunning = pSysShareData->sExSW.bSequenceBusy;
	pSysShareData->SISW.sSiSW.Wait = pSysShareData->sExSW.uWaits.sWaitBits.bWait0;
	pSysShareData->SISW.sSiSW.HomePosReached = pSysShareData->sExSW.bHomePosReached;
	pSysShareData->SISW.sSiSW.reserve1_2 = 0;
	pSysShareData->SISW.sSiSW.IsStandStill = pSysShareData->sExSW.bMotorStandStill;

	SpiSend_frame.Io_Out = 0;
	for (uint8 i = 0; i < 12; i++)
	{
		if (pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[i].bActive)
		{
			if (pSysShareData->SISW.uSiSW & (1 << pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[i].uIoAddr))
			{
				SpiSend_frame.Io_Out |= 1 << pSysShareData->sSysIoSetInfo.SIoSetInfo_Out[i].uIoIndex;
			}
		}
	}
}


/** Update Control World
*
* @param[in]     pInControlWord
* @param[out]    None
* @return		 void*/
void UpdateCw(const sSFCW* pInControlWord)
{
	pSysShareData->sExCW.bFielsbusRemote = pInControlWord->FielsbusRemote;	//总线控制权限
	pSysShareData->sExCW.bPower = pInControlWord->Power;					//设备上电(上升沿触发)、下降沿触发下电
	pSysShareData->sExCW.bMPSiwch = pInControlWord->MPSiwch;				//工艺切换(上升沿触发)
	pSysShareData->sExCW.bFaultRest = pInControlWord->FaultReset;			//错误清除(上升沿触发)

	//Byte1
	pSysShareData->sExCW.bRunSequence = pInControlWord->RunSequence;
	pSysShareData->sExCW.bStopSequence = pInControlWord->StopSequence;
	pSysShareData->sExCW.bUsePartId = pInControlWord->UsePartId;

	//Byte2
	pSysShareData->sExCW.bMoveToHomePos = pInControlWord->MoveToHomePos;	//移动到Home点
	// pSysShareData->sExCW.bMoveToRefPos = pInControlWord->MoveToRefPos;	//移动到参考点
	pSysShareData->sExCW.bJogForword = pInControlWord->JogForword;		//正向移动
	pSysShareData->sExCW.bJogBackword = pInControlWord->JogBackword;		//负向移动

	//Byte4
	pSysShareData->sExCW.bTareYChannel = pInControlWord->TareYChannel;		//去皮(上升沿触发)

	if (pInControlWord->Continue0 != pSysShareData->sExCW.uContinues.sContinueBits.bContinue0)
	{
		rt_dbgPrint(1, 0, "sContinueBits.bContinue0[%s] pInControlWord->Continue0[%s]\n",
			pSysShareData->sExCW.uContinues.sContinueBits.bContinue0 ? "true" : "false",
			pInControlWord->Continue0 ? "true" : "false");

	}
	//Byte6
	pSysShareData->sExCW.uContinues.sContinueBits.bContinue0 = pInControlWord->Continue0;			//继续运行0
	pSysShareData->sExCW.uContinues.sContinueBits.bContinue1 = pInControlWord->Continue1;			//继续运行1
	pSysShareData->sExCW.uContinues.sContinueBits.bContinue2 = pInControlWord->Continue2;			//继续运行2
	pSysShareData->sExCW.uContinues.sContinueBits.bContinue3 = pInControlWord->Continue3;			//继续运行3
	pSysShareData->sExCW.uContinues.sContinueBits.bContinue4 = pInControlWord->Continue4;			//继续运行4
	pSysShareData->sExCW.uContinues.sContinueBits.bContinue5 = pInControlWord->Continue5;			//继续运行5
	pSysShareData->sExCW.uContinues.sContinueBits.bContinue6 = pInControlWord->Continue6;			//继续运行6
	pSysShareData->sExCW.uContinues.sContinueBits.bContinue7 = pInControlWord->Continue7;			//继续运行7


	//Byte7
	pSysShareData->sExCW.JumpToNextItem = pInControlWord->JumpToNextItem;		//跳转到下一步

	//Byte8-Byte13 保留

	//Byte14
	//pInControlWord->SelectedMP = 66;
	pSysShareData->sExCW.SelectedMP = pInControlWord->SelectedMP;

	//Byte16
	pSysShareData->sExCW.PagePlcIn = pInControlWord->PagePlcIn;

	//Byte18
	pSysShareData->sExCW.PagePressOut = pInControlWord->PagePressOut;
}

TrigData rTestItemJump;
/** Update State World
* @param[in]     None
* @param[out]    None
* @return        Void*/
void UpdateSw()
{
	//Byte0
	if (pSysShareData->sExSW.eControlType != Explorer)
		pSysShareData->sExSW.bFieldBusRemoted = (pSysShareData->sFbRec.sFbCw.FielsbusRemote || pSysShareData->SICW.sSiCw.IoRemote);	//已被总线控制 FieldBusRemot
	else
		pSysShareData->sExSW.bFieldBusRemoted = false;

	if (pSysShareData->sExSW.eSysState == Sys_State_Ready)
		pSysShareData->sExSW.bSystemReady = true; 	//压装准备就绪
	else
		pSysShareData->sExSW.bSystemReady = false;	//压装准备就绪

	if (pSysShareData->sExSW.SysFaultCode == 0)
	{
		pSysShareData->sExSW.bSystemFault = false;	//系统错误
		pSysShareData->sExSW.bSystemWarm = false;	//系统警告
	}
	else
	{
		if (pSysShareData->sExSW.eSysErrLever != Warm)
		{
			pSysShareData->sExSW.bSystemFault = true;
			pSysShareData->sExSW.bSystemWarm = false;
		}
		else
		{
			pSysShareData->sExSW.bSystemFault = false;
			pSysShareData->sExSW.bSystemWarm = true;
		}
	}

	if (SYSTEM_MOTION_MODE)
	{
		//伺服上电状态(TRUE上电、FALSE下电)
		if (MC_IsPowerUp(&pSysShareData->AxisMc[0].Axis))
			pSysShareData->sExSW.bPowerState = true;
		else
			pSysShareData->sExSW.bPowerState = false;

		if (!pSysShareData->sExSW.bPowerState || (abs(pSysShareData->sSysFbkVar.fVelFbk) < 0.1))	//压机处在静止状态
		{
			pSysShareData->sExSW.bMotorStandStill = true;
		}
		else
			pSysShareData->sExSW.bMotorStandStill = false;

		if (pSysShareData->sExSW.bMotorStandStill &&
			pSysShareData->sExSW.ActiveMPId != 0 &&
			(abs(pSysShareData->sSysFbkVar.fPosFbk - fHomePos) < 0.01))	//达到home点
		{
			pSysShareData->sExSW.bHomePosReached = true;
		}
		else
			pSysShareData->sExSW.bHomePosReached = false;

		if (pSysShareData->sExSW.eSysState > Sys_State_Init)
		{
			if ((pSysShareData->sSysFbkVar.fPosFbk < pSysShareData->sSysFbkVar.fPosMin) ||
				abs(pSysShareData->sSysFbkVar.fPosFbk - pSysShareData->sSysFbkVar.fPosMin) < 0.01)	//达到压机最小位置
			{
				pSysShareData->sExSW.bMinPosReached = true;
			}
			else
			{
				pSysShareData->sExSW.bMinPosReached = false;
			}

			if ((pSysShareData->sSysFbkVar.fPosMax < pSysShareData->sSysFbkVar.fPosFbk) ||
				(abs(pSysShareData->sSysFbkVar.fPosFbk - pSysShareData->sSysFbkVar.fPosMax) < 0.01))	//达到压机最大位置
				pSysShareData->sExSW.bMaxPosReached = true;
			else
				pSysShareData->sExSW.bMaxPosReached = false;
		}
	}
	else
	{
		pSysShareData->sExSW.bMotorStandStill = false;
		pSysShareData->sExSW.bHomePosReached = false;
		pSysShareData->sExSW.bMinPosReached = false;
		pSysShareData->sExSW.bMaxPosReached = false;
	}

	if (pSysShareData->sExSW.bSequencePaused && pSysShareData->sExSW.bSystemFault)
		pSysShareData->sExSW.bSequenceAbnormal = true;


	// pSysShareData->SCSW  的信息只能在此处刷新，这样利于可维护。
	//memset(& pSysShareData->SCSW, 0, 20);
	pSysShareData->SCSW.FieldBusRemoted = pSysShareData->sExSW.bFieldBusRemoted;				//已被总线控制 FieldBusRemot
	pSysShareData->SCSW.PowerState = pSysShareData->sExSW.bPowerState;					//伺服上电状态(TRUE上电、FALSE下电)
	pSysShareData->SCSW.MPSiwched = pSysShareData->sExSW.bMPSiwched;						//工艺切换状态（TRUE切换成功）
	pSysShareData->SCSW.SystemReady = pSysShareData->sExSW.bSystemReady;					//压装准备就绪
	pSysShareData->SCSW.SystemFault = pSysShareData->sExSW.bSystemFault;					//系统错误
	pSysShareData->SCSW.SystemWarm = pSysShareData->sExSW.bSystemWarm;					//系统警告
	pSysShareData->SCSW.eStopTrig = pSysShareData->sExSW.bEStopTrig;						//急停触发
	pSysShareData->SCSW.gStopTrig = pSysShareData->sExSW.bGStopTrig;						//光栅触发

	pSysShareData->SCSW.SequenceBusy = pSysShareData->sExSW.bSequenceBusy;					//	压装运行中
	pSysShareData->SCSW.SequencePaused = pSysShareData->sExSW.bSequencePaused;				//	压装暂停
	pSysShareData->SCSW.SequenceEnd = pSysShareData->sExSW.bSequenceEnd;					//	压装结束
	pSysShareData->SCSW.SequenceAbnormal = pSysShareData->sExSW.bSequenceAbnormal;				//	压装异常中止
	pSysShareData->SCSW.PartIdUsed = pSysShareData->sExSW.bPartIdUsed;					//	Id号已绑定
	pSysShareData->SCSW.ResultOK = pSysShareData->sExSW.bResultOK;						//	结果合格
	pSysShareData->SCSW.ResultNOK = pSysShareData->sExSW.bResultNOK;						//	结果不合格

	pSysShareData->SCSW.HomePosReached = pSysShareData->sExSW.bHomePosReached;				//		达到home点
	// pSysShareData->SCSW.RefPosReached		=  pSysShareData->sExSW.bRefPosReached;				//		达到参考点
	pSysShareData->SCSW.MotorStandStill = pSysShareData->sExSW.bMotorStandStill;				//		压机处在静止状态
	pSysShareData->SCSW.MinPosReached = pSysShareData->sExSW.bMinPosReached;					//		达到压机最小位置
	pSysShareData->SCSW.MaxPosReached = pSysShareData->sExSW.bMaxPosReached;					//		达到压机最大位置

	pSysShareData->SCSW.TareYChanneled = pSysShareData->sExSW.bTareYChanneled;
	pSysShareData->SCSW.Wait0 = pSysShareData->sExSW.uWaits.sWaitBits.bWait0;		//	等待0
	pSysShareData->SCSW.Wait1 = pSysShareData->sExSW.uWaits.sWaitBits.bWait1;		//	等待1
	pSysShareData->SCSW.Wait2 = pSysShareData->sExSW.uWaits.sWaitBits.bWait2;		//	等待2
	pSysShareData->SCSW.Wait3 = pSysShareData->sExSW.uWaits.sWaitBits.bWait3;		//	等待3
	pSysShareData->SCSW.Wait4 = pSysShareData->sExSW.uWaits.sWaitBits.bWait4;		//	等待4
	pSysShareData->SCSW.Wait5 = pSysShareData->sExSW.uWaits.sWaitBits.bWait5;		//	等待5
	pSysShareData->SCSW.Wait6 = pSysShareData->sExSW.uWaits.sWaitBits.bWait6;		//	等待6
	pSysShareData->SCSW.Wait7 = pSysShareData->sExSW.uWaits.sWaitBits.bWait7;		//	等待7

	//Byte7
	pSysShareData->SCSW.HadJumpToNextItem = pSysShareData->sExSW.bHadJumpToNextItem;			//	跳转成功

	rTestItemJump.bTrigSignal = pSysShareData->sExSW.bHadJumpToNextItem;
	R_Trig(&rTestItemJump);
	if (rTestItemJump.bTrig)
		dbgPrint(1, 0, "\n bHadJumpToNextItem  trig\n");

	pSysShareData->SCSW.SysFaultCode = pSysShareData->sExSW.SysFaultCode;
	pSysShareData->SCSW.ActiveMPId = pSysShareData->sExSW.ActiveMPId;
	pSysShareData->SCSW.Mirror_PagePlcIn = pSysShareData->sExSW.Mirror_PagePlcIn;
	pSysShareData->SCSW.Mirror_PagePressOut = pSysShareData->sExSW.Mirror_PagePressOut;

	if (pSysShareData->sExSW.eSysState > Sys_State_Init)
	{
		IOStatus();		//IO

		//Fieldbus
		if (pSysShareData->bActiveFieldbus && pSysShareData->bFiledBusOk)
		{
			memcpy(&pSysShareData->sFbSend.sFbSw, &pSysShareData->SCSW, 20);

			if (pSysShareData->eFieldbusType == Profinet)			//PN 需要翻转  CC_Link不需要翻转   EtherCat不需要翻转
				ChangeWord((byte*)&pSysShareData->sFbSend.sFbSw.SysFaultCode);

			if (!bLoadGlobalOrFieldbus)
				PackFbData();
		}
	}
	//这个一定要在后面
	pSysShareData->sExSW.eLastControlType = pSysShareData->sExSW.eControlType;
}

void ResetCw()
{
	if (pSysShareData->sExSW.eControlType != pSysShareData->sExSW.eLastControlType)
	{
		if (pSysShareData->sExSW.eLastControlType == IO)
		{
			memset(&pSysShareData->SICW.uSiCw, 0, 2);
			//dbgPrint(1, 0, "ResetCw  IO\n");
		}
		else if (pSysShareData->sExSW.eLastControlType == Explorer)
		{
			//dbgPrint(1, 0, "ResetCw  Explorer\n");
			memset(&pSysShareData->SCCW, 0, 20);
		}
		else if (pSysShareData->sExSW.eLastControlType == Fieldbus)
		{
			memset(&pSysShareData->sFbRec, 0, 220);
			memset(&pSysShareData->SFCW, 0, 20);
			dbgPrint(1, 0, "ResetCw  pSysShareData->sFbRec\n");
			ResolveFbData();
		}
	}
}

/** FieldbusTypeSwitch   根据IO和总线的数据切换到对应的控制状态
* @param[in]     None
* @param[out]    None
* @return		 void*/

uint8 FieldbusIn = 0;
uint8 uFieldbusControl;
uint8 uIoControl;
EControlType eControledFbType;
EControlType FieldbusTypeSwitch()
{
	if (pSysShareData->sSysIoSetInfo.bEnable)
	{
		uIoControl = 0;
		//pSysShareData->SICW.uSiCw = 0;
		for (uint8 i = 0; i < IO_NUMBER; i++)
		{
			if (pSysShareData->sSysIoSetInfo.SIoSetInfo_In[i].bActive)
			{
				if (SpiRecv_frame.Io_In & (1 << pSysShareData->sSysIoSetInfo.SIoSetInfo_In[i].uIoIndex))
				{
					pSysShareData->SICW.uSiCw |= 1 << pSysShareData->sSysIoSetInfo.SIoSetInfo_In[i].uIoAddr;
				}
				else
				{
					pSysShareData->SICW.uSiCw &= (~(1 << pSysShareData->sSysIoSetInfo.SIoSetInfo_In[i].uIoAddr));
				}
			}
		}

		if (pSysShareData->SICW.sSiCw.IoRemote)
			uIoControl = 1;
		else
			pSysShareData->SICW.uSiCw = 0;
	}
	else
	{
		uIoControl = 0;
	}

	if (pSysShareData->bActiveFieldbus && pSysShareData->bFiledBusOk)		 //总线运行周期是1ms  所以当周期为500us时需要对刷新周期进行处理
	{
		if (pSysShareData->gSysCounter % 2 == 0)
		{
			uFieldbusControl = 0;
			Anybus2spi_GetAndSetBusData((uint8_t*)&pSysShareData->sFbSend, (uint8_t*)&pSysShareData->sFbRec);
			if (pSysShareData->sFbRec.sFbCw.FielsbusRemote)
				uFieldbusControl = 2;
		}
	}
	else
	{
		uFieldbusControl = 0;
	}

	switch (uFieldbusControl + uIoControl)
	{
	case 0:
		eControledFbType = Explorer;
		break;
	case 1:
		eControledFbType = IO;
		break;
	case 2:
		eControledFbType = Fieldbus;
		break;
	case 3:
		eControledFbType = Fieldbus;		//都给信号  总线优先
		break;
	default:
		eControledFbType = Explorer;
		break;
	}
	return eControledFbType;
}


/** ControlBusManager   根据IO和总线的数据切换到对应的控制状态
* @param[in]     None
* @param[out]    None
* @return		 void*/
void IOControl()
{
	SpiSend_frame.Io_Out = 0;
	pSysShareData->sExCW.bFielsbusRemote = pSysShareData->SICW.sSiCw.IoRemote;
	pSysShareData->sExCW.bFaultRest = pSysShareData->SICW.sSiCw.FaultReset;
	pSysShareData->sExCW.bPower = pSysShareData->SICW.sSiCw.Power;
	pSysShareData->sExCW.bStopSequence = pSysShareData->SICW.sSiCw.StopSequence;
	pSysShareData->sExCW.bRunSequence = pSysShareData->SICW.sSiCw.RunSequence;
	pSysShareData->sExCW.uContinues.sContinueBits.bContinue0 = pSysShareData->SICW.sSiCw.WaitContinue;
	pSysShareData->sExCW.bMoveToHomePos = pSysShareData->SICW.sSiCw.MoveToHomePos;
	pSysShareData->sExCW.bJogForword = pSysShareData->SICW.sSiCw.JogForward;
	pSysShareData->sExCW.bJogBackword = pSysShareData->SICW.sSiCw.JogBackward;
}


/** ControlBusManager   根据IO和总线的数据切换到对应的控制状态
* @param[in]     None
* @param[out]    None
* @return		 void*/

const sSFCW* pActivedCW;
void ControlBusManager()
{
	pSysShareData->sExSW.eControlType = FieldbusTypeSwitch();
	ResetCw();
	switch (pSysShareData->sExSW.eControlType)
	{
	case Explorer:
		pActivedCW = &pSysShareData->SCCW;
		UpdateCw(pActivedCW);
		break;
	case IO:
		IOControl();
		break;
	case Fieldbus:
		memcpy(&pSysShareData->SFCW, &pSysShareData->sFbRec.sFbCw, 20);
		pActivedCW = &pSysShareData->sFbRec.sFbCw;
		UpdateCw(pActivedCW);

		if (pSysShareData->bActiveFieldbus && pSysShareData->bFiledBusOk && pSysShareData->sExSW.eSysState > Sys_State_Init)
			ResolveFbData();
		break;
	default:
		break;
	}
	//刷新触发器

	RefreshTrigSingal();
}
/** rt_calc   需要实时刷新得部分；  控制模式、控制字 状态字 、系统保护参数、状态机、
* @param[in]     None
* @param[out]    None
* @return  ESystemControlMode: Explorer/Fieldbus/Io/Fieldbus_Io
* */
TrigData	MotorTareTrig[AxisNum];
void rt_calc(int* piSpiUpdateResult)
{
	if (pSysShareData->sExSW.eSysState > Sys_State_Init)
	{
		SensorsDataUpdate(piSpiUpdateResult);

		SystemProtect();

		//总线切换管理及控制源数据管理
		ControlBusManager();								//CW

		//循环测试
		if (pSysShareData->bCycleRun)
			CycleTest();
	}

	//系统状态
	FSM(&pSysShareData->sExCW, &pSysShareData->sExSW);

	if (pSysShareData->sExSW.eSysState > Sys_State_Init)
	{
		if (SYSTEM_MOTION_MODE)
		{
			MC_Tare(&pSysShareData->AxisMc[0], &MotorTareTrig[0]);

			MC_ServoCaculation(&pSysShareData->AxisMc[0], &bJogMode);
		}

		//运行时采集数据
		AppendRunSeqData();

		//订阅数据
		rt_PackDatagram();
	}

	UpdateSw();											//SW
}

void UpdateFactoryPrara()
{
	//printSeparatorString(1, 114, "-", "Spindle Infomation");
	//dbgPrint(1, 0, "%8s%15s%12s%12s%12s%12s%12s%12s%12s\n", "", "name", "trans", "InPluse", "OutPluse", "Trq_NorCur", "TrqLmt", "Acc", "StopAcc");
	for (int8 i = 0; i < AxisNum; i++)
	{
		pSysShareData->AxisMc[i].sMcSetPara.bActived = true;
		pSysShareData->AxisMc[i].Axis.SystemPeriod = (float32)SYS_BASE_TIME_S;
		pSysShareData->AxisMc[i].Axis.Cfg.LogicPositionOffset = 0;  //need reload from config file
		pSysShareData->AxisMc[i].Axis.Cfg.MotorPlusePerRevolution = 8388608;// SysCfg.Motor[i].iMotorPlusePerRevolution;// 131072;//8388608;
		pSysShareData->AxisMc[i].Axis.Cfg.ShaftPlusePerRevolution = 1000000;// SysCfg.Motor[i].iShaftPlusePerRevolution;// 131072;//1000000;

		pSysShareData->AxisMc[i].Axis.Cfg.LogicStopAcc = 2000;

		pSysShareData->AxisMc[i].Axis.Motor.bHasReset = false;
		pSysShareData->AxisMc[i].Axis.Motor.LinkVar.ModeOfOp = 9;
		pSysShareData->AxisMc[i].Axis.Context.posPidCtrl.kp = 10;

		pSysShareData->AxisMc[i].CtrlPara.Acc = 100;
		pSysShareData->AxisMc[i].CtrlPara.Jerk = 10000;
		pSysShareData->AxisMc[i].CtrlPara.HomeAcc = pSysShareData->AxisMc[i].CtrlPara.Acc;
		pSysShareData->AxisMc[i].CtrlPara.HomeJerk = pSysShareData->AxisMc[i].CtrlPara.Jerk;
		pSysShareData->AxisMc[i].CtrlPara.StopAcc = 2000;
		pSysShareData->AxisMc[i].CtrlPara.StopJerk = 6000;
		pSysShareData->AxisMc[i].CtrlPara.TrqRampRate = 0.6;

		////dbgPrint(1, 0, "%8d%15s%12.3f%12d%12d%12.3f%12.2f%12.3f%12.3f\n",
		//	i,  pSysShareData->AxisMc[i].sMcSetPara.SensorName,
		//	 pSysShareData->AxisMc[i].Axis.Cfg.Tranlatepara,
		//	 pSysShareData->AxisMc[i].Axis.Cfg.MotorPlusePerRevolution,
		//	 pSysShareData->AxisMc[i].Axis.Cfg.ShaftPlusePerRevolution,
		//	 pSysShareData->AxisMc[i].Axis.Cfg.CalibTrqWithNormalCurr,
		//	 pSysShareData->AxisMc[i].Axis.Cfg.TorqueLimitTimes,
		//	 pSysShareData->AxisMc[i].CtrlPara.Acc,
		//	 pSysShareData->AxisMc[i].Axis.Cfg.LogicStopAcc
		//);
	}
}


int ServoParaInit() {
	driveType = (eDriveType)SYSTEM_DRIVE_TYPE;
	UpdateFactoryPrara();
	Seq.pAxisMc = &pSysShareData->AxisMc[0];
	//dbgPrint(1, 0, " pSysShareData->Seq.pAxisMc add = %p, pSysShareData->AxisMc addr = %p\n", pSysShareData->Seq.pAxisMc, pSysShareData->AxisMc);
	return 0;
}


int MainWorkLoop() { //int argc, char* argv[]
	SYS_BASE_TIME_S = (((double)SYS_BASE_TIME_uS) / 1000000);
	gMcInScpMode = false;

	time_t timestamp;
	struct tm* p;

	if (SYSTEM_MOTION_MODE)
	{
		ServoParaInit();
	}
	return 0;
}



