#include <string>
#include "SqliteParaRw.h"
#include "sqliteapi/sqliteOp/SqliteOp.h"
#include "core/core.h"
#include "core/base.h"
#include <iostream>
#include <fstream>
#include "PushResult.h"
#include "EoThread.h"
#include "Statistics.h"
#include "SysVarDefine.h"
#include "Sn.h"
#include "PressThread.h"
#include "SysShareMemoryDefine.h"
#include "SysVarDefine.h"
#include "rapidjson/document.h"
#include "rttimer.h"

using namespace rapidjson;
#define SDOPARA_BUFF_SIZE  102400
char aSdoParaBuff[SDOPARA_BUFF_SIZE];


/*SaveSdoParaToDataBaseNewTable           
* @param[in]       None
* @param[out]      None
* @return          ErrorCode         = 0x2001 打开文件失败
*/

uint16 indexCount = 0;
ErrorInfo SaveSdoParaToDataBaseNewTable()
{
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    if (pSysShareData->bDataBaseHadInit)
    {
        if (WriteSdoCount > 0)
        {
            for (uint16 i = 0; i < WriteSdoCount; i++)
            {
                //memset(&aSdoData[i].sVar, 0, 255);
                FormatSdoValue(pEntrySdo[i], (char*)&aSdoData[i].sVar, 255);
                if (strlen(aSdoData[i].sVar) < 1)
                    strcpy(aSdoData[i].sVar, " ");
            }
            errorInfo = WriteSdoFromDataBase(&WriteSdoCount, &aSdoData[0]);
        }
        else
        {
            dbgPrint(1, 0, "SaveSdoParaToDataBaseNewTable WriteSdoCount == 0 \n");
        }
    }
    else
    {
        errorInfo.ErrCode = 5403;
        errorInfo.eErrLever = Error;
    }

    ErrorInfoPack(&errorInfo, "SaveSdoParaToDataBase", "");
    return errorInfo;
}


/*SaveSdoParaToDataBase             加载文件  leetx_user.json 中数据保存到Sdo中
* @param[in]       None 
* @param[out]      None
* @return          ErrorCode         = 0x2001 打开文件失败
*/
ErrorInfo SaveSdoParaToDataBase()
{
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    char tmpStr[256];
    char tmpVarStr[256];
    uint8 tmpStrLen = 0;
    uint16 iSaveParaCount = 0;
    uint16 iSdoLen = 0;
    PtrConvert pSdoPtr;
    pSdoPtr.pChar = &aSdoParaBuff[0];

    char* sItemName = "Sdo"; 
    char* sSdoVersion = "V1.0.0";
 
    uint32 uKey;
    if (pSysShareData->bDataBaseHadInit)
    {
        pFistEntry = HashTable_First();
        SDOEntryDesc* pEntry = pFistEntry;//HashTable_First();
        while (pEntry)
        {
            if (pEntry->SaveFlag == Save_User)//&& 1)
            {
                const char* szKey = HashTable_CurrentKey();
                // memcpy(&sTmpKeyIndex, szKey, 6);
                if (sscanf(szKey, "0x%6X", &uKey) == 1)
                {
                    if (uKey < 0xB00100 || uKey > 0xB002FF)
                    {
                        iSaveParaCount++;

                        if (uKey == 0x900118)
                        {
                            bool bTest = false;
                        }
                        memset(&tmpVarStr, 0, 255);
                        FormatSdoValue(pEntry, tmpVarStr, 255);
                        if (strlen(tmpVarStr) < 1)
                            strcpy(tmpVarStr, " ");

                        sprintf(tmpStr, "%s,%s,%s\n", szKey, pEntry->Name, tmpVarStr);
                        tmpStrLen = strlen(tmpStr);

                       // dbgPrint(1, 0, "iSaveParaCount:%d szKey:%s Name:%-30s Var:%s\n", iSaveParaCount, szKey, pEntry->Name, tmpVarStr);
                        if (iSdoLen + tmpStrLen <= SDOPARA_BUFF_SIZE)
                        {
                            strcpy(pSdoPtr.pChar, tmpStr);
                            iSdoLen += tmpStrLen;
                            pSdoPtr.pChar += tmpStrLen;
                        }
                        else
                        {
                            errorInfo.ErrCode = 5400;// 115;
                            errorInfo.eErrLever = Error;
                            dbgPrint(1, 0, "iSdoLen >SDOPARA_BUFF_SIZE  iSdoLen:%d\n", iSdoLen, SDOPARA_BUFF_SIZE);
                            break;
                        }
                    }
                }
                else
                {
                    errorInfo.eErrLever = Error;
                    errorInfo.ErrCode = 5401;//116;
                    break;
                }

            }
            pEntry = HashTable_Next();
        }

        if (!errorInfo.ErrCode)
        {
            errorInfo = WriteJsonOp(sItemName, aSdoParaBuff, sSdoVersion);
            if (errorInfo.ErrCode)
            {
                errorInfo.ErrCode = 5402;
                errorInfo.eErrLever = Error;
            }
        }
    }
    else
    {
        errorInfo.ErrCode = 5403;
        errorInfo.eErrLever = Error;
    }

    ErrorInfoPack(&errorInfo, "SaveSdoParaToDataBase","");
    return errorInfo;
}


ErrorInfo SaveSdoParaToDataBase1()
{
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    char tmpStr[256];
    char tmpVarStr[256];
    uint8 tmpStrLen = 0;
    uint16 iSaveParaCount = 0;
    uint16 iSdoLen = 0;
    PtrConvert pSdoPtr;
    pSdoPtr.pChar = &aSdoParaBuff[0];

    char* sItemName = "Sdo";
    char* sSdoVersion = "V1.0.0";

    uint32 uKey;
    if (pSysShareData->bDataBaseHadInit)
    {
        if (WriteSdoCount > 0)
        {
            for (uint16 i = 0; i < WriteSdoCount; i++)
            {
                FormatSdoValue(pEntrySdo[i], (char*)&aSdoData[i].sVar, 255);
                if (strlen(aSdoData[i].sVar) < 1)
                    strcpy(aSdoData[i].sVar, " ");

                sprintf(tmpStr, "%s,%s,%s\n", aSdoData[i].sKey, aSdoData[i].sName, aSdoData[i].sVar);
                tmpStrLen = strlen(tmpStr);

                //  dbgPrint(1, 0, "iSaveParaCount:%d szKey:%s Name:%20s Var:%s\n", i, aSdoData[i].sKey, aSdoData[i].sName, aSdoData[i].sVar);
                if (iSdoLen + tmpStrLen <= SDOPARA_BUFF_SIZE)
                {
                    strcpy(pSdoPtr.pChar, tmpStr);
                    iSdoLen += tmpStrLen;
                    pSdoPtr.pChar += tmpStrLen;
                }
                else
                {
                    errorInfo.ErrCode = 5400;// 115;
                    errorInfo.eErrLever = Error;
                    dbgPrint(1, 0, "iSdoLen >SDOPARA_BUFF_SIZE  iSdoLen:%d\n", iSdoLen, SDOPARA_BUFF_SIZE);
                    break;
                }
            }
            //errorInfo = WriteSdoFromDataBase(&WriteSdoCount, &aSdoData[0]);
        }
        else
        {
            dbgPrint(1, 0, "SaveSdoParaToDataBaseNewTable WriteSdoCount == 0 \n");
        }

        if (!errorInfo.ErrCode)
        {
            errorInfo = WriteJsonOp(sItemName, aSdoParaBuff, sSdoVersion);
            if (errorInfo.ErrCode)
            {
                errorInfo.ErrCode = 5402;
                errorInfo.eErrLever = Error;
            }
        }
    }
    else
    {
        errorInfo.ErrCode = 5403;
        errorInfo.eErrLever = Error;
    }

    ErrorInfoPack(&errorInfo, "SaveSdoParaToDataBase", "");
    return errorInfo;
}


/*LoadSdoParaFromDataBase           从数据库中获取sdo数据
* @param[in]       None
* @param[out]      None
* @return          ErrorCode         = 0x2001 打开文件失败
*/

ErrorInfo LoadSdoParaFromDataBaseNewTable()
{
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
;   uint32 uKey;
    uint8 indexRead = 0;
    uint16 ReadCount = 0;
    uint16 indexCount = 0;

    pFistEntry = HashTable_First();
    SDOEntryDesc* pEntry = pFistEntry;
    while (pEntry)
    {
        if (pEntry->SaveFlag == Save_User)//&& 1)
        {
            const char* szKey = HashTable_CurrentKey();
            if (sscanf(szKey, "0x%6X", &uKey) == 1)
            {
                if (uKey < 0xB00100 || uKey > 0xB002FF)
                {
                    // 根据注册的sdo  查找数据库中的sdo数据
                    strcpy(aSdoData[indexRead].sKey, szKey);
                    aSdoData[indexRead].iKey = uKey;
                    strcpy(aSdoData[indexRead].sName, pEntry->Name);
                    pEntrySdo[indexRead] = pEntry;

                    indexRead++;
                    if (indexRead == SDO_MAX_WR_COUNT)
                    {
                        ReadCount = SDO_MAX_WR_COUNT;
                        errorInfo =ReadSdoFromDataBase(&ReadCount, &aSdoData[0]);
                        if (errorInfo.ErrCode)
                            break;

                        for (uint8 i = 0; i < ReadCount; i++)
                        {
                            pEntry = pEntrySdo[i];         //pEntry = HashTable_Find(aSdoData[i].sKey);
                            if (pEntry!= NULL)
                            {
                                ErrorInfo tmpErrorInfo = ScanfSdoValue(pEntry, aSdoData[i].sVar);
                                if (tmpErrorInfo.ErrCode)
                                {
                                    dbgPrint(1, 0, "Keydata:%s\n", aSdoData[i].sKey);
                                    //break;
                                }
                            }

                            dbgPrint(1, 0, "ReadCounter:%3d iKey:%6X sKey:%s sName:%s sVar:%s\n",
                                indexCount,
                                aSdoData[i].iKey,
                                aSdoData[i].sKey,
                                aSdoData[i].sName,
                                aSdoData[i].sVar);
                            indexCount++;
                        }
                        indexRead = 0;
                    }
                }
            }
        }
        pEntry = HashTable_Next();
    }

    if (indexRead > 0 && indexRead < SDO_MAX_WR_COUNT)
    {
        ReadCount = indexRead + 1;
        errorInfo = ReadSdoFromDataBase(&ReadCount, &aSdoData[0]);
        if (!errorInfo.ErrCode)
        {
            for (uint8 i = 0; i < ReadCount; i++)
            {
                pEntry = pEntrySdo[i];         //pEntry = HashTable_Find(aSdoData[i].sKey);
                if (pEntry != NULL)
                {
                    ErrorInfo tmpErrorInfo = ScanfSdoValue(pEntry, aSdoData[i].sVar);
                    if (tmpErrorInfo.ErrCode)
                    {
                        dbgPrint(1, 0, "Keydata:%s\n", aSdoData[i].sKey);
                    }
                }

                dbgPrint(1, 0, "ReadCounter:%3d iKey:%6X sKey:%s sName:%s sVar:%s\n",
                    indexCount,
                    aSdoData[i].iKey,
                    aSdoData[i].sKey,
                    aSdoData[i].sName,
                    aSdoData[i].sVar);
                indexCount++;
            }
            indexRead = 0;
        }
    }

    ErrorInfoPack(&errorInfo, "LoadSdoParaFromDataBaseNewTable", "");
    return errorInfo;
}

/*LoadSdoParaFromDataBase           从数据库中获取sdo数据
* @param[in]       None
* @param[out]      None
* @return          ErrorCode         = 0x2001 打开文件失败
*/
ErrorInfo LoadSdoParaFromDataBase()
{
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    char keydata[50], namedata[128], valuedata[128];
    uint16 iScanfIndex = 0;
    uint32  iScanParaLen = 0;
    char* sItemName = "Sdo";
    char* sSdoVersion = "V1.0.0";
    char* pSdoPara = 0;
    char sTmpkeydata[50];
    SDOEntryDesc* pEntry;
    std::string sSdoPara;
    uint32 uKey;
    errorInfo = ReadJsonOp(sItemName, sSdoVersion, &sSdoPara);
    if (!errorInfo.ErrCode)
    {
        if (strcmp(sSdoPara.c_str(), "0") != 0 && strlen((char*)sSdoPara.c_str()) > 0)
        {
             //dbgPrint(1, 0, "sSdoParaLen:%d\n", strlen((char*)sSdoPara.c_str()));
            //dbgPrint(1, 0, "---------------------------------------- Load Sdo Para From DataBase ------------------------------------------------\n");
            const char* pSdoBuff = sSdoPara.c_str();
            while (sscanf(pSdoBuff, "%[^,],%[^,],%[^\n]\n", keydata, namedata, valuedata) == 3)
            {
                iScanfIndex++;
                pEntry = HashTable_Find(keydata);

                if (strcmp(keydata, "0xA00024") == 0)
                    bool bTest = true;

                if (pEntry && pEntry->SaveFlag == Save_User)
                {
                    if (sscanf(keydata, "0x%6X", &uKey) == 1)
                    {
                        if (uKey < 0xB001 || uKey > 0xB002)
                        {
                            ErrorInfo tmpErrorInfo = ScanfSdoValue(pEntry, valuedata);
                            if (tmpErrorInfo.ErrCode)
                            {
                                errorInfo = tmpErrorInfo;   //发现错误不要跳过，继续load参数
                                memcpy(&sTmpkeydata[0], &keydata, 50);
                                dbgPrint(1, 0, "Keydata:%s\n", keydata);
                                //break;
                            }
                        }
                    }
                }
                pSdoBuff += strlen(keydata) + strlen(namedata) + strlen(valuedata) + 3;

               // dbgPrint(1, 0, "Keydata:%s   namedata:%-30s  valuedata:%s\n", keydata, namedata, valuedata);
            }
            
           // dbgPrint(1, 0, "-------------------------------------------------------------------------------------------------------------\n");
            iScanParaLen = pSdoBuff - sSdoPara.c_str();
            if (iScanParaLen != strlen((char*)sSdoPara.c_str()))
            {
                dbgPrint(1, 0, "sSdoPara  Resolver Length Error!! sSdoParaLen:%d  ScanParaLen:%d\n", strlen((char*)sSdoPara.c_str()), iScanParaLen);
                errorInfo.eErrLever = Error;
                errorInfo.ErrCode = 5801;// 2;
            }
        }
        else
        {
            dbgPrint(1, 0, "sSdoPara  NULL\n");
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 5800;// 1;
        }
    }
    ErrorInfoPack(&errorInfo, "LoadSdoParaFromDataBase", "SdoLen:%d iScanParaLen:%d ", strlen((char*)sSdoPara.c_str()), iScanParaLen);
    return errorInfo;
}

/*PackOverviewInfo               将运行结果存储到数据库中
* @param[in]       bool bStartSaveResult
* @param[in]       int iProfileId
* @param[out]      None
* @return          ErrorCode         = 0x2001 打开文件失败
*/
const char* SysMode[] = {"Measure","Motion Controller"};
const char* MoveMode[] = { "EcoMove", "AccuMove"};
std::string OverViewInfo;
void PackOverviewInfo()         //下面组包的数据中存在空格
{
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();

    //压力传感器的状态    spi 是否正常
   // str255 sTmp;
    document.AddMember("FileType",             "Result", allocator);
    document.AddMember("DeviceName",            Value(sSysDeviceSetInfo.sDeviveName, allocator), allocator);
    document.AddMember("sw_version",            Value(SysInfo.ControlSoftwareVersion, allocator), allocator);//下位机版本
    document.AddMember("hd_version",            Value(SysInfo.HardwareVersion, allocator), allocator);
    document.AddMember("config_file_version",   Value(SysInfo.ProfileVersion, allocator), allocator);

    document.AddMember("PLCPartID ",            Value(gSysSnData.RecPlcSn, allocator), allocator);
    document.AddMember("PartID ",               Value(gSysSnData.sSn, allocator), allocator);
    document.AddMember("NcID",                  Value("", allocator), allocator);
    document.AddMember("NcIP ",                 Value(sSysDeviceSetInfo.ControlIp, allocator), allocator);
    document.AddMember("NcModel ",              Value(SysMode[uSystemMode], allocator), allocator);
    if ((uSystemMode == 0) && (uEcoMoveMode == 1))
    {
        document.AddMember("MoveModel ", Value("", allocator), allocator);
    }else
        document.AddMember("MoveModel ", Value(MoveMode[uEcoMoveMode], allocator), allocator);

    //刷新一下时间  避免异常   在某些场景下不刷新时间直接使用会出现异常。
    GetSystemTime();			
    GetTimeStr((char*)&sSysDeviceSetInfo.DispTime);     //这个必须要一起刷  GetSystemTime();

    document.AddMember("TimeStamp",             Value(sSysDeviceSetInfo.DispTime, allocator), allocator);

    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    OverViewInfo = buffer.GetString();
   // std::cout << OverViewInfo << std::endl;
}

/*SaveRunResultToDataBase               将运行结果存储到数据库中
* @param[in]       bool bStartSaveResult
* @param[in]       int iProfileId
* @param[out]      None
* @return          ErrorCode         = 0x2001 打开文件失败
*/
uint8 iSaveResultStep = 0;
uint8 iLastSaveResultStep = 0;
ErrorInfo errorInfo_SaveResult;
ErrorInfo SaveRunResultToDataBase(bool* pbStartSaveResult, int iProfileId,char* pResultVersion)
{
    if (*pbStartSaveResult)// || *pbPointsFinish)
    {
        switch (iSaveResultStep)
        {
        case 0:
        {
            memset(&errorInfo_SaveResult, 0, sizeof(ErrorInfo));
            if (psChartsData->uPointsCount > 0)
            {
                iSaveResultStep = 1;
            }
            else
            {
                if (SYSTEM_MOVE_SERIES)
                {
                    iSaveResultStep = 1;
                }
                else
                {
                    iSaveResultStep = 10;
                }
            }
        }
            break;
        case 1:
            if (*pbStartSaveResult)
            {
                if (PushEoResultFrame(pEoResultDataAdr, EoResultDataLen))
                {
                    iSaveResultStep = 10;
                }
            }
            break;
        case 10:
        {
            if (pSysShareData->bDataBaseHadInit)
            {
                PackOverviewInfo();

                errorInfo_SaveResult = WriteJsonResultOp(iProfileId, pEoResultDataAdr, "{}", (char*)OverViewInfo.c_str(), "{}", "{}", "{}", "{}", "{}",
                    &psChartsData->sChart[0].Points[0], 12 * psChartsData->uPointsCount, pResultVersion);
            }
            else
            {
                errorInfo_SaveResult.ErrCode = 5501;
                errorInfo_SaveResult.eErrLever = Error;
            }

            if (errorInfo_SaveResult.ErrCode)
            {
                rt_dbgPrint(1, 0, "WriteJsonResultOp Error:%d\n", errorInfo_SaveResult.ErrCode);
                iSaveResultStep = 100;
            }
            else
            {
                iSaveResultStep = 20;
            }
        }
            break;
        case 20:
        {
            //更新统计数据、存储到数据库中
            if (pSysShareData->bDataBaseHadInit)
            {
                errorInfo_SaveResult = UpdateStatisticsData(psChartsData->bChartEvalOK, iProfileId);
                iSaveResultStep = 0;
                iLastSaveResultStep = 0;
                *pbStartSaveResult = false;
            }
            else
            {
                errorInfo_SaveResult.ErrCode = 5502;
                errorInfo_SaveResult.eErrLever = Error;
                iSaveResultStep = 100;
            }
        }
            break;
        case 100:
            iSaveResultStep = 0;
            iLastSaveResultStep = 0;
            *pbStartSaveResult = false;
            break;
        default:
            break;
        }

        if (iLastSaveResultStep != iSaveResultStep)
        {
            //dbgPrint(1, 0, "iLastSaveResultStep[%d]-> iSaveResultStep[%d]\n ", iLastSaveResultStep, iSaveResultStep);
            iLastSaveResultStep = iSaveResultStep;
        }
    }

    if (errorInfo_SaveResult.ErrCode)
    {
        iSaveResultStep = 0;
        *pbStartSaveResult = false;
        ErrorInfoPack(&errorInfo_SaveResult, "SaveRunResultToDataBase", "");
    }

    return errorInfo_SaveResult;
}

//是否上电  位置 速度  加速度  电流 压力(滤波前 滤波后)  外置位移    工艺号  工艺的工步  温度
std::string stringErrInfo;
void PackSeriousErrorInfo(ErrorInfo* pErrInfo)
{
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
    document.AddMember("PowerUp", pSysShareData->sExSW.bPowerState, allocator);
    if (SYSTEM_MOTION_MODE)
    {
        document.AddMember("Position", pSysShareData->sSysFbkVar.fPosFbk, allocator);
        document.AddMember("Position No Filter", pSysShareData->sSysFbkVar.fPosFbk_NoFilter, allocator);
        document.AddMember("Velocity ", pSysShareData->sSysFbkVar.fVelFbk, allocator);
        document.AddMember("Velocity No Filter", pSysShareData->sSysFbkVar.fVelFbk_NoFilter, allocator);
        document.AddMember("Acc ", pSysShareData->sSysFbkVar.fAccFbk, allocator);
        document.AddMember("Acc No Filter", pSysShareData->sSysFbkVar.fAccFbk_NoFilter, allocator);
        document.AddMember("Current ", pSysShareData->sSysFbkVar.fCurrentFbk, allocator);
    }

    //压力传感器的状态    spi 是否正常
    document.AddMember("Sensor communication ", pSysShareData->bSpiSensConnected, allocator);
    document.AddMember("Force Actived ", pSysShareData->sSysFbkVar.bSensorForceActived, allocator);
    document.AddMember("Force ", pSysShareData->sSysFbkVar.fSenosrVarFbk, allocator);
    document.AddMember("Force No Filter", pSysShareData->sSysFbkVar.fSenosrVarFbk_NoFilter, allocator);

    document.AddMember("ExtPos Actived", pSysShareData->sSysFbkVar.bSensorExtPosActived, allocator);
    document.AddMember("ExtPos ", pSysShareData->sSysFbkVar.fExtPosFbk, allocator);
    document.AddMember("ExtPos  No Filter", pSysShareData->sSysFbkVar.fExtPosFbk_NoFilter, allocator);

    document.AddMember("Activedd Profile Id ", pSysShareData->sExSW.ActiveMPId, allocator);
   // document.AddMember("Sequence Active Row Index ", pSysShareData->Seq.iActiveRowIndex, allocator);
   // document.AddMember("Sequence Error Row Index ", pSysShareData->Seq.iErrorRowIndex, allocator);

    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    stringErrInfo = buffer.GetString();

   // WriteErrorToSqlite(*pErrInfo, true, json_string, pbFinishWrite);
    //std::cout << json_string << std::endl;
}