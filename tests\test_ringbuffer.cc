#include <gtest/gtest.h>
#include "RingBuffer.h"
#include <vector>

TEST(<PERSON><PERSON><PERSON><PERSON>, InitAndEmpty) {
  sByteRingBuffer buf; std::vector<byte> mem(32);
  ByteRingBuf_Init(&buf, mem.data(), (int)mem.size());
  EXPECT_TRUE(ByteRingBuf_IsEmpty(&buf));
  EXPECT_FALSE(ByteRingBuf_IsFull(&buf));
  EXPECT_EQ(ByteRingBuf_ActualSize(&buf), 0);
}

TEST(RingBuffer, WriteReadBasic) {
  sByteRingBuffer buf; std::vector<byte> mem(64);
  ByteRingBuf_Init(&buf, mem.data(), (int)mem.size());
  std::vector<byte> in = {1,2,3,4,5,0}, out(6,0xFF);
  ASSERT_EQ(ByteRingBuf_Write(&buf, in.data(), (int)in.size()), 6);
  EXPECT_EQ(ByteRingBuf_ActualSize(&buf), 6);
  ASSERT_EQ(ByteRingBuf_Read(&buf, out.data(), (int)out.size()), 6);
  EXPECT_EQ(out, in);
}

TEST(RingBuffer, WrapAndFrame) {
  sByteRingBuffer buf; std::vector<byte> mem(32);
  ByteRingBuf_Init(&buf, mem.data(), (int)mem.size());
  std::vector<byte> junk(20,0xAA);
  ByteRingBuf_Write(&buf, junk.data(), (int)junk.size());
  std::vector<byte> frame = {9,8,7,6,5};
  ASSERT_EQ(ByteRingBuf_WriteFrame(&buf, frame.data(), (int)frame.size()), (int)frame.size());
  std::vector<byte> out(8,0);
  ASSERT_EQ(ByteRingBuf_ReadFrame(&buf, out.data(), (int)out.size()), (int)frame.size());
  out.resize(frame.size());
  EXPECT_EQ(out, frame);
}