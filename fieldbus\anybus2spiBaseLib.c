/**************************************************************
 * File: anybus2spiBaseLib.c
 * Author: Gloss Tsai (<EMAIL>)
 * Created on: January 26, 2024
 * Description:
 *      提供host与slave通用的函数
 * Note:
 *      https://alidocs.dingtalk.com/i/nodes/a9E05BDRVQvv6GQyCBqzPQ0DJ63zgkYA
 * Copyright (C) 2024 Leetx. All rights reserved.
 *************************************************************/
#include "anybus2spiBaseLib.h"
#include <string.h>
#include <stdio.h>
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
#include "cr_section_macros.h"
#endif

#define CRC16_CCITT_SEED       0xFFFF
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
void CRC16CCITT_MemsetBufferWithCRC16(uint8_t* src, uint8_t val, uint16_t len)
{
    memset(src, val, len);
    CRC16CCITT_AppendCalc(src, len);
}
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__DATA(SRAM_DTC_cm7)
#endif
const static uint16_t CRC16CCITT_Table[256] =
{
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50A5, 0x60C6, 0x70E7,
    0x8108, 0x9129, 0xA14A, 0xB16B, 0xC18C, 0xD1AD, 0xE1CE, 0xF1EF,
    0x1231, 0x0210, 0x3273, 0x2252, 0x52B5, 0x4294, 0x72F7, 0x62D6,
    0x9339, 0x8318, 0xB37B, 0xA35A, 0xD3BD, 0xC39C, 0xF3FF, 0xE3DE,
    0x2462, 0x3443, 0x0420, 0x1401, 0x64E6, 0x74C7, 0x44A4, 0x5485,
    0xA56A, 0xB54B, 0x8528, 0x9509, 0xE5EE, 0xF5CF, 0xC5AC, 0xD58D,
    0x3653, 0x2672, 0x1611, 0x0630, 0x76D7, 0x66F6, 0x5695, 0x46B4,
    0xB75B, 0xA77A, 0x9719, 0x8738, 0xF7DF, 0xE7FE, 0xD79D, 0xC7BC,
    0x48C4, 0x58E5, 0x6886, 0x78A7, 0x0840, 0x1861, 0x2802, 0x3823,
    0xC9CC, 0xD9ED, 0xE98E, 0xF9AF, 0x8948, 0x9969, 0xA90A, 0xB92B,
    0x5AF5, 0x4AD4, 0x7AB7, 0x6A96, 0x1A71, 0x0A50, 0x3A33, 0x2A12,
    0xDBFD, 0xCBDC, 0xFBBF, 0xEB9E, 0x9B79, 0x8B58, 0xBB3B, 0xAB1A,
    0x6CA6, 0x7C87, 0x4CE4, 0x5CC5, 0x2C22, 0x3C03, 0x0C60, 0x1C41,
    0xEDAE, 0xFD8F, 0xCDEC, 0xDDCD, 0xAD2A, 0xBD0B, 0x8D68, 0x9D49,
    0x7E97, 0x6EB6, 0x5ED5, 0x4EF4, 0x3E13, 0x2E32, 0x1E51, 0x0E70,
    0xFF9F, 0xEFBE, 0xDFDD, 0xCFFC, 0xBF1B, 0xAF3A, 0x9F59, 0x8F78,
    0x9188, 0x81A9, 0xB1CA, 0xA1EB, 0xD10C, 0xC12D, 0xF14E, 0xE16F,
    0x1080, 0x00A1, 0x30C2, 0x20E3, 0x5004, 0x4025, 0x7046, 0x6067,
    0x83B9, 0x9398, 0xA3FB, 0xB3DA, 0xC33D, 0xD31C, 0xE37F, 0xF35E,
    0x02B1, 0x1290, 0x22F3, 0x32D2, 0x4235, 0x5214, 0x6277, 0x7256,
    0xB5EA, 0xA5CB, 0x95A8, 0x8589, 0xF56E, 0xE54F, 0xD52C, 0xC50D,
    0x34E2, 0x24C3, 0x14A0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
    0xA7DB, 0xB7FA, 0x8799, 0x97B8, 0xE75F, 0xF77E, 0xC71D, 0xD73C,
    0x26D3, 0x36F2, 0x0691, 0x16B0, 0x6657, 0x7676, 0x4615, 0x5634,
    0xD94C, 0xC96D, 0xF90E, 0xE92F, 0x99C8, 0x89E9, 0xB98A, 0xA9AB,
    0x5844, 0x4865, 0x7806, 0x6827, 0x18C0, 0x08E1, 0x3882, 0x28A3,
    0xCB7D, 0xDB5C, 0xEB3F, 0xFB1E, 0x8BF9, 0x9BD8, 0xABBB, 0xBB9A,
    0x4A75, 0x5A54, 0x6A37, 0x7A16, 0x0AF1, 0x1AD0, 0x2AB3, 0x3A92,
    0xFD2E, 0xED0F, 0xDD6C, 0xCD4D, 0xBDAA, 0xAD8B, 0x9DE8, 0x8DC9,
    0x7C26, 0x6C07, 0x5C64, 0x4C45, 0x3CA2, 0x2C83, 0x1CE0, 0x0CC1,
    0xEF1F, 0xFF3E, 0xCF5D, 0xDF7C, 0xAF9B, 0xBFBA, 0x8FD9, 0x9FF8,
    0x6E17, 0x7E36, 0x4E55, 0x5E74, 0x2E93, 0x3EB2, 0x0ED1, 0x1EF0
};
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
uint16_t CRC16CCITT_Calc(const uint8_t* data, uint8_t length)
{
    uint16_t crc = CRC16_CCITT_SEED;
    for (size_t i = 0; i < length; i++) {
        uint8_t index = (crc >> 8) ^ data[i];
        crc = (crc << 8) ^ CRC16CCITT_Table[index];
    }
    return crc;
}
/**
 * @brief CRC16-CCITT计算，将CRC16放置于数组末尾2字节
 */
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
void CRC16CCITT_AppendCalc(uint8_t* data, uint8_t length)
{
    uint16_t crc = CRC16CCITT_Calc(data, length - 2);
    data[length - 2] = (crc >> 8) & 0xFF;   /* 大端序 */
    data[length - 1] = crc & 0xFF;
}
/**
 * @brief CRC16-CCITT计算，对比数组末尾的CRC16与数组的CRC值
 */
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
bool CRC16CCITT_Check(const uint8_t* data, uint8_t length)
{
    uint16_t crc = CRC16CCITT_Calc(data, length - 2);
    if (crc == ((data[length - 2] << 8) | data[length - 1]))
    {
        return true;   /* 大端序 */
    }
    else
    {
        return false;
    }
}
/** @todo 4站8倍为256字节，当前最大220字节 */
uint16_t CCL_WordLenTable[4][8] =
{   /*          Extension Cycle:    1       2      x       4       x       x       x       8 */
    /* occupied station 1*/ {       4,      8,     0,     16,      0,      0,      0,     32,},
    /* occupied station 2*/ {       8,     16,     0,     32,      0,      0,      0,     64,},
    /* occupied station 3*/ {      12,     24,     0,     48,      0,      0,      0,     96,},
    /* occupied station 4*/ {      16,     32,     0,     64,      0,      0,      0,    128,},
};
uint16_t CCL_PointLenTable[4][8] =
{   /*          Extension Cycle:    1       2      x       4       x       x       x       8 */
    /* occupied station 1*/ {      32,     32,     0,     64,      0,      0,      0,    128,},
    /* occupied station 2*/ {      64,     96,     0,    192,      0,      0,      0,    384,},
    /* occupied station 3*/ {      96,    160,     0,    320,      0,      0,      0,    640,},
    /* occupied station 4*/ {     128,    224,     0,    448,      0,      0,      0,    896,},
};
uint16_t AB2S_GetCCLByteLen(uint8_t OccupiedStation, uint8_t ExtensionCycle)
{
    return CCL_WordLenTable[OccupiedStation - 1][ExtensionCycle - 1] * 2;
}
uint32_t AB2S_GetVersionCode()
{
    return (((uint8_t)AB2S_VERSION_M << 24) | ((uint8_t)AB2S_VERSION_S << 16) | ((uint8_t)AB2S_VERSION_P << 8));
}
/**
 * @brief 将版本号转换为字符串
 * @note versionStr有效长度应大于10
 */
void AB2S_DecodeVersion(uint32_t versionCode, char* versionStr)
{
    if (versionStr == NULL)
    {
        return;
    }
    uint8_t major = (versionCode >> 24) & 0xFF;
    uint8_t minor = (versionCode >> 16) & 0xFF;
    uint8_t patch = (versionCode >> 8) & 0xFF;
    sprintf(versionStr, "V%d.%d.%d", major, minor, patch);
}
/*******************************************************************************
 * File: AB2S_LIFO.c
 * Author: Gloss Tsai
 * Created on: Sep 30, 2024
 * Commit ID: 430c533b3a8b6a2d7ccae9cac6096e2193ff2247
 * Description:
 *  C语言的简单线程间通信，使用两个缓存区，实现一个缓存区写入另一个缓存区读取。
 *  @warning 每个句柄(AB2S_LIFOHandle_t 类型变量)的读取或写入函数各自禁止嵌套使用
 *  @warning 当前索引采用递增形式，为了防止溢出使用64位无符号整型，这会导致运算效率降低。并且
 *          运行时间过长(每毫秒index递增1，则584,942,417年溢出)或index出错可能会引起溢出。
 * Copyright (C) 2024 Leetx. All rights reserved.
 *******************************************************************************/
 /* Includes ------------------------------------------------------------------ */
 // #include "AB2S_LIFO.h"
#include <stdlib.h>
#include <string.h>
/* Private macro ------------------------------------------------------------- */
#define AB2S_LIFO_MASSAGE_LEN_MAX  WORKING_MODE_MAX_DATA_LENGTH
/* Private typedef ----------------------------------------------------------- */
/* Private variables --------------------------------------------------------- */
/* Private function prototypes ----------------------------------------------- */
inline static uint8_t LIFO_GetWritableIndex(AB2S_LIFOHandle_t* ptHandle);
inline static uint8_t LIFO_GetReadableIndex(AB2S_LIFOHandle_t* ptHandle);
/* Private functions --------------------------------------------------------- */
bool LIFO_Init(AB2S_LIFOHandle_t* ptHandle, uint32_t size)
{
    if (size > AB2S_LIFO_MASSAGE_LEN_MAX || size == 0)
    {
        return false;
    }
    memset(ptHandle, 0, sizeof(AB2S_LIFOHandle_t));
    ptHandle->tLIFOMessage[0].pBuffer = (uint8_t*)malloc(size);
    ptHandle->tLIFOMessage[1].pBuffer = (uint8_t*)malloc(size);
    if (ptHandle->tLIFOMessage[0].pBuffer == NULL || ptHandle->tLIFOMessage[1].pBuffer == NULL)
    {
        return false;
    }
    memset(ptHandle->tLIFOMessage[0].pBuffer, 0, size);
    memset(ptHandle->tLIFOMessage[1].pBuffer, 0, size);

    ptHandle->maxSize = size;
    return true;
}
/**
 * @todo 初始化及反初始化的资源竞争
 */
void LIFO_Deinit(AB2S_LIFOHandle_t* ptHandle)
{
    free(ptHandle->tLIFOMessage[0].pBuffer);
    free(ptHandle->tLIFOMessage[1].pBuffer);
}
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
inline static uint8_t LIFO_GetWritableIndex(AB2S_LIFOHandle_t* ptHandle)
{
    uint8_t tempIndex = 0;
    if (ptHandle->tLIFOMessage[0].MsgIndex <= ptHandle->tLIFOMessage[1].MsgIndex)
    {
        tempIndex = 0;
    }
    else
    {
        tempIndex = 1;
    }
    if (ptHandle->tLIFOMessage[tempIndex].readingFlag == 1)
    {
        tempIndex = !tempIndex;
    }
    return tempIndex;
}
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
inline static uint8_t LIFO_GetReadableIndex(AB2S_LIFOHandle_t* ptHandle)
{
    /* 当MsgIndex均为0时，可读返回tLIFOMessage[1]。与此同时，可写返回tLIFOMessage[0]。*/
    return (ptHandle->tLIFOMessage[0].MsgIndex > ptHandle->tLIFOMessage[1].MsgIndex) ? 0 : 1;
}
/**
 * @brief 写入缓存区
 * @warning 请勿在多个线程中异步调用本函数
 */
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
bool LIFO_WriteMessage(AB2S_LIFOHandle_t* ptHandle, const uint8_t* pBuffer, uint16_t size)
{
    if (ptHandle == NULL || pBuffer == NULL)
    {
        return false;
    }
    if (size > ptHandle->maxSize)
    {
        return false;
    }
    uint8_t tempIndex = LIFO_GetWritableIndex(ptHandle);
    ptHandle->tLIFOMessage[tempIndex].size = size;
    memcpy(ptHandle->tLIFOMessage[tempIndex].pBuffer, pBuffer, size);
    ptHandle->WriteMsgIndex++;
    ptHandle->tLIFOMessage[tempIndex].BeenReadFlag = 0;
    ptHandle->tLIFOMessage[tempIndex].MsgIndex = ptHandle->WriteMsgIndex;
    return true;
}
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
bool LIFO_ReadMessage(AB2S_LIFOHandle_t* ptHandle, uint8_t* pBuffer, uint16_t size, uint16_t* RealReadSize)
{
    if (ptHandle == NULL || pBuffer == NULL || size == 0)
    {
        return false;
    }
    if (size > ptHandle->maxSize)
    {
        return false;
    }
    uint8_t tempIndex = LIFO_GetReadableIndex(ptHandle);
    if (ptHandle->tLIFOMessage[tempIndex].BeenReadFlag == 0)
    {
        /* 未读 */
        *RealReadSize = size > ptHandle->tLIFOMessage[tempIndex].size ? ptHandle->tLIFOMessage[tempIndex].size : size;
        ptHandle->tLIFOMessage[tempIndex].readingFlag = 1;
        memcpy(pBuffer, ptHandle->tLIFOMessage[tempIndex].pBuffer, *RealReadSize);
        ptHandle->tLIFOMessage[tempIndex].BeenReadFlag = 1;
        ptHandle->tLIFOMessage[tempIndex].readingFlag = 0;
        return true;
    }
    else
    {
        *RealReadSize = 0;
        return false;
    }
}
/**
 * @brief 返回当前索引最大值是否已经被读取
 */
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
bool LIFO_IsMessageBeenRead(AB2S_LIFOHandle_t* ptHandle)
{
    return ptHandle->tLIFOMessage[LIFO_GetReadableIndex(ptHandle)].BeenReadFlag ? true : false;
}
