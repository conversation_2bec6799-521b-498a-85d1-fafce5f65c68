#include "pid.h"
#include "base.h"


 void PidReset(SPidData* pPidHandle, float IntegrationLimit, float fOutDownLimit, float fOutUpLimit, float InitOutput) {
    pPidHandle->fIntegrationLimit = IntegrationLimit;
    pPidHandle->fOutUpLimit = fOutUpLimit;
    pPidHandle->fOutDownLimit = fOutDownLimit;
    pPidHandle->_fIntegration = InitOutput;
	pPidHandle->_bFirst = true;
}


 float PidCycleUpdate(SPidData* pPidHandle, float fRef, float fFbk, float forwardOut) {
	pPidHandle->_fPID_Error = fRef - fFbk;
    pPidHandle->fOutput = pPidHandle->kp * pPidHandle->_fPID_Error;
    if (pPidHandle->ki > 0) {
        pPidHandle->_fIntegration +=  pPidHandle->ki * pPidHandle->_fPID_Error * SYS_BASE_TIME_S;
        pPidHandle->_fIntegration = Min(Max(pPidHandle->_fIntegration, -pPidHandle->fIntegrationLimit), pPidHandle->fIntegrationLimit);
        pPidHandle->fOutput += pPidHandle->_fIntegration;
	}

	if (pPidHandle->kd > 0) {
		if (pPidHandle->_bFirst) {
			pPidHandle->_bFirst = false;
		}
		else
		{
			pPidHandle->fOutput += pPidHandle->kd * (pPidHandle->_fPID_Error - pPidHandle->_fLastPID_Error) / SYS_BASE_TIME_S;
		}
		pPidHandle->_fLastPID_Error = pPidHandle->_fPID_Error;
	}
	pPidHandle->fOutput += forwardOut;
    return Min(Max(pPidHandle->fOutput, pPidHandle->fOutDownLimit),pPidHandle->fOutUpLimit);
}
