#include "Global&LocalVar.h"
#include "sqliteapi/sqliteOp/SqliteOp.h"
#include "SysVarDefine.h"
#include "rapidjson/document.h"
#include "SysShareMemoryDefine.h"

using namespace rapidjson;

bool bAutoResetGlobalVar = false;
SVariableVar      sSysGlobalVar;
SVariableVar      sLocalVar;

std::string Configjson;

/* FindGlobalLocalVar       查找全局变量和局部变量
* @param[in]                SFieldbusConfigUnit* pMatchVar
* @return                   bool
*/
str16 sSdoGroup;
bool FindGlobalLocalVar(SFieldbusConfigUnit* pMatchVar, SFieldbusConfigUnit* pTargetVar)
{
	bool bMatched = false;
	if (pMatchVar != NULL && pMatchVar->bGlobalOrLocalVar && strlen(pMatchVar->sSdoKey) > 6)
	{
		memset(&sSdoGroup, 0, sizeof(sSdoGroup));
		strncpy(sSdoGroup, pMatchVar->sSdo<PERSON>ey, 6);
		if (strcmp(sSdoGroup, "0xB001") == 0 && sSysGlobalVar.uVarCount > 0)
		{
			for (uint8 uIndexMatch = 0; uIndexMatch < Min(sSysGlobalVar.uVarCount, GLOBALVAR_MAX_NUMBER); uIndexMatch++)
			{
				if (strcmp(pMatchVar->sName, sSysGlobalVar.aSVariableVar[uIndexMatch].sName) == 0 &&
					pMatchVar->eDataType == sSysGlobalVar.aSVariableVar[uIndexMatch].eDataType)
				{
					pTargetVar->bGlobalOrLocalVar = true;
					pTargetVar->AddrBit = pMatchVar->AddrBit;
					pTargetVar->AddrByte = pMatchVar->AddrByte;
					pTargetVar->eDataType = sSysGlobalVar.aSVariableVar[uIndexMatch].eDataType;
					pTargetVar->Size = DATATYPE_SIZE[sSysGlobalVar.aSVariableVar[uIndexMatch].eDataType];
					strcpy(pTargetVar->sName, pMatchVar->sName); //pTargetVar->sName;
					strcpy(pTargetVar->sSdoKey, sSysGlobalVar.aSVariableVar[uIndexMatch].sKey); //pTargetVar->sSdoKey;
					bMatched = true;
					break;
				}
			}
		}
		else if (strcmp(sSdoGroup, "0xB002") == 0)
		{
			for (uint8 uIndexMatch = 0; uIndexMatch < Min(sLocalVar.uVarCount, GLOBALVAR_MAX_NUMBER); uIndexMatch++)
			{
				if (strcmp(pMatchVar->sName, sLocalVar.aSVariableVar[uIndexMatch].sName) == 0 &&
					pMatchVar->eDataType == sLocalVar.aSVariableVar[uIndexMatch].eDataType)
				{
					pTargetVar->bGlobalOrLocalVar = pMatchVar->bGlobalOrLocalVar;
					pTargetVar->AddrBit = pMatchVar->AddrBit;
					pTargetVar->AddrByte = pMatchVar->AddrByte;
					pTargetVar->eDataType = sLocalVar.aSVariableVar[uIndexMatch].eDataType;

					pTargetVar->Size = DATATYPE_SIZE[sLocalVar.aSVariableVar[uIndexMatch].eDataType];//pTargetVar->Size = pMatchVar->Size;
					strcpy(pTargetVar->sName, pMatchVar->sName); //pTargetVar->sName;
					strcpy(pTargetVar->sSdoKey, sLocalVar.aSVariableVar[uIndexMatch].sKey); //pTargetVar->sSdoKey;
					bMatched = true;
					break;
				}
			}
		}
	}

	return bMatched;
}


/* TypeSwitchAndGetVar                  将Json中的字符串数据类型转化成 U8
* @param[in]                            char* psDataType
* @return                               EDataType
*/
uint32      uNewMemory = 0;
uint32      uFreeMemory = 0;
bool        bPrint = false;
ErrorInfo TypeSwitchAndGetVar(char* psDataType, Value& jVar, SVarUnit* pVarUnit, bool bGlobal)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	if (pVarUnit->pVar != NULL)
	{
		free(pVarUnit->pVar);
		pVarUnit->pVar = NULL;
	}

	if (pVarUnit->pInitVar != NULL)
	{
		free(pVarUnit->pInitVar);
		pVarUnit->pInitVar = NULL;
	}

	if (strcmp(psDataType, "NONE") == 0)
	{
		pVarUnit->eDataType = DT_None;
	}
	else if (strcmp(psDataType, "Bool") == 0 && jVar["Var"].IsBool())
	{
		pVarUnit->eDataType = DT_Bool;
		pVarUnit->uInitValLen = sizeof(bool);

		pVarUnit->pVar = (bool*)malloc(pVarUnit->uInitValLen);
		uNewMemory += pVarUnit->uInitValLen;

		*(bool*)pVarUnit->pVar = jVar["Var"].GetBool();

		pVarUnit->pInitVar = (bool*)malloc(pVarUnit->uInitValLen);
		uNewMemory += pVarUnit->uInitValLen;
		*(bool*)pVarUnit->pInitVar = *(bool*)pVarUnit->pVar;

		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%s\n",
				pVarUnit->sName,
				psDataType,
				*(bool*)pVarUnit->pVar == true ? "true" : "false");
	}
	else if (strcmp(psDataType, "I8") == 0 && (jVar["Var"].IsNumber() || jVar["Var"].IsBool()))
	{
		pVarUnit->eDataType = DT_I8;
		pVarUnit->uInitValLen = sizeof(int8);

		pVarUnit->pVar = (int8*)malloc(sizeof(int8));
		uNewMemory += sizeof(int8);

		if (jVar["Var"].IsInt())
			*(int8*)pVarUnit->pVar = (int8)jVar["Var"].GetInt();
		else if (jVar["Var"].IsUint())
			*(int8*)pVarUnit->pVar = (int8)jVar["Var"].GetUint();
		else if (jVar["Var"].IsInt64())
			*(int8*)pVarUnit->pVar = (int8)jVar["Var"].GetInt64();
		else if (jVar["Var"].IsFloat())
			*(int8*)pVarUnit->pVar = (int8)jVar["Var"].GetFloat();
		else  if (jVar["Var"].IsBool())
			*(int8*)pVarUnit->pVar = (int8)jVar["Var"].GetBool();
		else  if (jVar["Var"].IsDouble())
			*(int8*)pVarUnit->pVar = (int8)jVar["Var"].GetDouble();

		pVarUnit->pInitVar = (int8*)malloc(sizeof(int8));
		uNewMemory += sizeof(int8);
		*(int8*)pVarUnit->pInitVar = *(int8*)pVarUnit->pVar;

		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%d\n",
				pVarUnit->sName,
				psDataType,
				*(int8*)pVarUnit->pVar);

	}
	else if (strcmp(psDataType, "U8") == 0 && (jVar["Var"].IsNumber() || jVar["Var"].IsBool()))
	{
		pVarUnit->eDataType = DT_U8;
		pVarUnit->uInitValLen = sizeof(uint8);

		pVarUnit->pVar = (uint8*)malloc(sizeof(uint8));
		uNewMemory += sizeof(uint8);

		if (jVar["Var"].IsUint())
			*(uint8*)pVarUnit->pVar = (uint8)jVar["Var"].GetUint();
		else if (jVar["Var"].IsInt64())
			*(uint8*)pVarUnit->pVar = (uint8)jVar["Var"].GetInt64();
		else if (jVar["Var"].IsFloat())
			*(uint8*)pVarUnit->pVar = (uint8)jVar["Var"].GetFloat();
		else  if (jVar["Var"].IsBool())
			*(uint8*)pVarUnit->pVar = (uint8)jVar["Var"].GetBool();
		else  if (jVar["Var"].IsDouble())
			*(uint8*)pVarUnit->pVar = (uint8)jVar["Var"].GetDouble();

		pVarUnit->pInitVar = (uint8*)malloc(sizeof(uint8));
		uNewMemory += sizeof(uint8);
		*(uint8*)pVarUnit->pInitVar = *(uint8*)pVarUnit->pVar;

		//dbgPrint(1, 0, "malloc:Var:%ld\n", pVarUnit->pVar);

		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%d\n",
				pVarUnit->sName,
				psDataType,
				*(uint8*)pVarUnit->pVar);
	}
	else if (strcmp(psDataType, "I16") == 0 && (jVar["Var"].IsNumber() || jVar["Var"].IsBool()))
	{
		pVarUnit->eDataType = DT_I16;
		pVarUnit->uInitValLen = sizeof(int16);

		pVarUnit->pVar = (int16*)malloc(sizeof(int16));
		uNewMemory += sizeof(int16);

		if (jVar["Var"].IsInt())
			*(int16*)pVarUnit->pVar = (int16)jVar["Var"].GetInt();
		else if (jVar["Var"].IsUint())
			*(int16*)pVarUnit->pVar = (int16)jVar["Var"].GetUint();
		else if (jVar["Var"].IsInt64())
			*(int16*)pVarUnit->pVar = (int16)jVar["Var"].GetInt64();
		else if (jVar["Var"].IsFloat())
			*(int16*)pVarUnit->pVar = (int16)jVar["Var"].GetFloat();
		else  if (jVar["Var"].IsBool())
			*(int16*)pVarUnit->pVar = (int16)jVar["Var"].GetBool();
		else  if (jVar["Var"].IsDouble())
			*(int16*)pVarUnit->pVar = (int16)jVar["Var"].GetDouble();


		pVarUnit->pInitVar = (int16*)malloc(sizeof(int16));
		uNewMemory += sizeof(int16);
		*(int16*)pVarUnit->pInitVar = *(int16*)pVarUnit->pVar;

		// dbgPrint(1, 0, "malloc:Var:%ld\n", pVarUnit->pVar);
		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%d\n",
				pVarUnit->sName,
				psDataType,
				*(int16*)pVarUnit->pVar);
	}
	else if (strcmp(psDataType, "U16") == 0 && (jVar["Var"].IsNumber() || jVar["Var"].IsBool()))
	{
		pVarUnit->eDataType = DT_U16;
		pVarUnit->uInitValLen = sizeof(uint16);

		pVarUnit->pVar = (uint16*)malloc(sizeof(uint16));
		uNewMemory += sizeof(uint16);

		if (jVar["Var"].IsUint())
			*(uint16*)pVarUnit->pVar = (uint16)jVar["Var"].GetUint();
		else if (jVar["Var"].IsInt())
			*(uint16*)pVarUnit->pVar = (uint16)jVar["Var"].GetInt();
		else if (jVar["Var"].IsInt64())
			*(uint16*)pVarUnit->pVar = (uint16)jVar["Var"].GetInt64();
		else if (jVar["Var"].IsFloat())
			*(uint16*)pVarUnit->pVar = (uint16)jVar["Var"].GetFloat();
		else  if (jVar["Var"].IsBool())
			*(uint16*)pVarUnit->pVar = (uint16)jVar["Var"].GetBool();
		else  if (jVar["Var"].IsDouble())
			*(uint16*)pVarUnit->pVar = (uint16)jVar["Var"].GetDouble();

		pVarUnit->pInitVar = (uint16*)malloc(sizeof(uint16));
		uNewMemory += sizeof(uint16);
		*(uint16*)pVarUnit->pInitVar = *(uint16*)pVarUnit->pVar;

		//  dbgPrint(1, 0, "malloc:Var:%ld\n", pVarUnit->pVar);
		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%d\n",
				pVarUnit->sName,
				psDataType,
				*(uint16*)pVarUnit->pVar);
	}
	else if (strcmp(psDataType, "I32") == 0 && (jVar["Var"].IsNumber() || jVar["Var"].IsBool()))
	{
		pVarUnit->eDataType = DT_I32;
		pVarUnit->uInitValLen = sizeof(int32);

		pVarUnit->pVar = (int32*)malloc(sizeof(int32));
		uNewMemory += sizeof(int32);

		if (jVar["Var"].IsInt())
			*(int32*)pVarUnit->pVar = (int32)jVar["Var"].GetInt();
		else if (jVar["Var"].IsUint())
			*(int32*)pVarUnit->pVar = (int32)jVar["Var"].GetUint();
		else if (jVar["Var"].IsInt64())
			*(int32*)pVarUnit->pVar = (int32)jVar["Var"].GetInt64();
		else if (jVar["Var"].IsFloat())
			*(int32*)pVarUnit->pVar = (int32)jVar["Var"].GetFloat();
		else  if (jVar["Var"].IsBool())
			*(int32*)pVarUnit->pVar = (int32)jVar["Var"].GetBool();
		else  if (jVar["Var"].IsDouble())
			*(int32*)pVarUnit->pVar = (int32)jVar["Var"].GetDouble();

		pVarUnit->pInitVar = (int32*)malloc(sizeof(int32));
		uNewMemory += sizeof(int32);
		*(int32*)pVarUnit->pInitVar = *(int32*)pVarUnit->pVar;

		//  dbgPrint(1, 0, "malloc:Var:%ld\n", pVarUnit->pVar);
		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%d\n",
				pVarUnit->sName,
				psDataType,
				*(int32*)pVarUnit->pVar);
	}
	else if (strcmp(psDataType, "U32") == 0 && (jVar["Var"].IsNumber() || jVar["Var"].IsBool()))
	{
		pVarUnit->eDataType = DT_U32;
		pVarUnit->uInitValLen = sizeof(uint32);

		pVarUnit->pVar = (uint32*)malloc(sizeof(uint32));
		uNewMemory += sizeof(uint32);

		if (jVar["Var"].IsUint())
			*(uint32*)pVarUnit->pVar = (uint32)jVar["Var"].GetUint();
		else if (jVar["Var"].IsInt())
			*(uint32*)pVarUnit->pVar = (uint32)jVar["Var"].GetInt();
		else if (jVar["Var"].IsInt64())
			*(uint32*)pVarUnit->pVar = (uint32)jVar["Var"].GetInt64();
		else if (jVar["Var"].IsFloat())
			*(uint32*)pVarUnit->pVar = (uint32)jVar["Var"].GetFloat();
		else  if (jVar["Var"].IsBool())
			*(uint32*)pVarUnit->pVar = (uint32)jVar["Var"].GetBool();
		else  if (jVar["Var"].IsDouble())
			*(uint32*)pVarUnit->pVar = (uint32)jVar["Var"].GetDouble();

		pVarUnit->pInitVar = (uint32*)malloc(sizeof(uint32));
		uNewMemory += sizeof(uint32);
		*(uint32*)pVarUnit->pInitVar = *(uint32*)pVarUnit->pVar;

		//  dbgPrint(1, 0, "malloc:Var:%ld\n", pVarUnit->pVar);
		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%d\n",
				pVarUnit->sName,
				psDataType,
				*(uint32*)pVarUnit->pVar);
	}
	else if (strcmp(psDataType, "I64") == 0 && (jVar["Var"].IsNumber() || jVar["Var"].IsBool()))
	{
		pVarUnit->eDataType = DT_I64;
		pVarUnit->uInitValLen = sizeof(int64);

		pVarUnit->pVar = (int64*)malloc(sizeof(int64));
		uNewMemory += sizeof(int64);

		if (jVar["Var"].IsInt64())
			*(int64*)pVarUnit->pVar = (int64)jVar["Var"].GetInt64();
		else if (jVar["Var"].IsUint())
			*(int64*)pVarUnit->pVar = (int64)jVar["Var"].GetUint();
		else if (jVar["Var"].IsInt())
			*(int64*)pVarUnit->pVar = (int64)jVar["Var"].GetInt();
		else if (jVar["Var"].IsFloat())
			*(int64*)pVarUnit->pVar = (int64)jVar["Var"].GetFloat();
		else  if (jVar["Var"].IsBool())
			*(int64*)pVarUnit->pVar = (int64)jVar["Var"].GetBool();
		else  if (jVar["Var"].IsDouble())
			*(int64*)pVarUnit->pVar = (int64)jVar["Var"].GetDouble();


		pVarUnit->pInitVar = (int64*)malloc(sizeof(int64));
		uNewMemory += sizeof(int64);
		*(int64*)pVarUnit->pInitVar = *(int64*)pVarUnit->pVar;// jVar["Var"].GetInt64();

		// dbgPrint(1, 0, "malloc:Var:%ld\n", pVarUnit->pVar);
		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%d\n",
				pVarUnit->sName,
				psDataType,
				*(int64*)pVarUnit->pVar);
	}
	else if (strcmp(psDataType, "U64") == 0 && (jVar["Var"].IsNumber() || jVar["Var"].IsBool()))
	{
		pVarUnit->eDataType = DT_U64;
		pVarUnit->uInitValLen = sizeof(uint64);

		pVarUnit->pVar = (int64*)malloc(sizeof(uint64));
		uNewMemory += sizeof(uint64);

		if (jVar["Var"].IsInt64())
			*(uint64*)pVarUnit->pVar = (uint64)jVar["Var"].GetInt64();
		else if (jVar["Var"].IsFloat())
			*(uint64*)pVarUnit->pVar = (uint64)jVar["Var"].GetFloat();
		else  if (jVar["Var"].IsInt())
			*(uint64*)pVarUnit->pVar = (uint64)jVar["Var"].GetInt();
		else  if (jVar["Var"].IsUint())
			*(uint64*)pVarUnit->pVar = (uint64)jVar["Var"].GetUint();
		else  if (jVar["Var"].IsBool())
			*(uint64*)pVarUnit->pVar = (uint64)jVar["Var"].GetBool();
		else  if (jVar["Var"].IsDouble())
			*(uint64*)pVarUnit->pVar = (uint64)jVar["Var"].GetDouble();

		pVarUnit->pInitVar = (int64*)malloc(sizeof(uint64));
		uNewMemory += sizeof(uint64);
		*(uint64*)pVarUnit->pInitVar = *(uint64*)pVarUnit->pVar;

		//dbgPrint(1, 0, "malloc:Var:%ld\n", pVarUnit->pVar);
		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%d\n",
				pVarUnit->sName,
				psDataType,
				*(uint64*)pVarUnit->pVar);
	}
	else if (strcmp(psDataType, "F32") == 0 && (jVar["Var"].IsNumber() || jVar["Var"].IsBool()))
	{
		pVarUnit->eDataType = DT_F32;
		pVarUnit->uInitValLen = sizeof(float32);

		pVarUnit->pVar = (float32*)malloc(sizeof(float32));
		uNewMemory += sizeof(float32);

		if (jVar["Var"].IsFloat())
			*(float32*)pVarUnit->pVar = (float32)jVar["Var"].GetFloat();
		else  if (jVar["Var"].IsInt())
			*(float32*)pVarUnit->pVar = (float32)jVar["Var"].GetInt();
		else  if (jVar["Var"].IsUint())
			*(float32*)pVarUnit->pVar = (float32)jVar["Var"].GetUint();
		else if (jVar["Var"].IsDouble())
			*(float32*)pVarUnit->pVar = (float32)jVar["Var"].GetDouble();
		else  if (jVar["Var"].IsBool())
			*(float32*)pVarUnit->pVar = (float32)jVar["Var"].GetBool();
		else  if (jVar["Var"].IsInt64())
			*(float32*)pVarUnit->pVar = (float32)jVar["Var"].GetInt64();

		pVarUnit->pInitVar = (float32*)malloc(sizeof(float32));
		uNewMemory += sizeof(float32);
		*(float32*)pVarUnit->pInitVar = *(float32*)pVarUnit->pVar;

		//dbgPrint(1, 0, "malloc:Var:%ld\n", pVarUnit->pVar);
		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%f\n",
				pVarUnit->sName,
				psDataType,
				*(float32*)pVarUnit->pVar);
	}
	else if (strcmp(psDataType, "F64") == 0 && (jVar["Var"].IsNumber() || jVar["Var"].IsBool()))
	{
		pVarUnit->eDataType = DT_F64;
		pVarUnit->uInitValLen = sizeof(float64);
		pVarUnit->pVar = (float64*)malloc(sizeof(float64));
		uNewMemory += sizeof(float64);

		if (jVar["Var"].IsDouble())
			*(float64*)pVarUnit->pVar = (float64)jVar["Var"].GetDouble();
		else  if (jVar["Var"].IsInt())
			*(float64*)pVarUnit->pVar = (float64)jVar["Var"].GetInt();
		else  if (jVar["Var"].IsUint())
			*(float64*)pVarUnit->pVar = (float64)jVar["Var"].GetUint();
		else  if (jVar["Var"].IsBool())
			*(float64*)pVarUnit->pVar = (float64)jVar["Var"].GetBool();
		else  if (jVar["Var"].IsFloat())
			*(float64*)pVarUnit->pVar = (float64)jVar["Var"].GetFloat();
		else  if (jVar["Var"].IsInt64())
			*(float64*)pVarUnit->pVar = (float64)jVar["Var"].GetInt64();

		pVarUnit->pInitVar = (float64*)malloc(sizeof(float64));
		uNewMemory += sizeof(float64);
		*(float64*)pVarUnit->pInitVar = *(float64*)pVarUnit->pVar;

		//dbgPrint(1, 0, "malloc:Var:%ld\n", pVarUnit->pVar);
		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%f\n",
				pVarUnit->sName,
				psDataType,
				*(float64*)pVarUnit->pVar);
	}
	else if (strcmp(psDataType, "Str") == 0 && jVar["Var"].IsString())
	{
		pVarUnit->eDataType = DT_Str;
		pVarUnit->pVar = (str255*)malloc(sizeof(str255));
		uNewMemory += sizeof(str255);
		strcpy((char*)pVarUnit->pVar, jVar["Var"].GetString());
		pVarUnit->uInitValLen = sizeof(str255);//strlen((char*)pVarUnit->pVar);

		pVarUnit->pInitVar = (str255*)malloc(sizeof(str255));
		uNewMemory += sizeof(str255);
		strcpy((char*)pVarUnit->pInitVar, (char*)pVarUnit->pVar);

		//dbgPrint(1, 0, "malloc:Var:%ld\n", pVarUnit->pVar);
		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%s\n",
				pVarUnit->sName,
				psDataType,
				pVarUnit->pVar);
	}
	else if (strcmp(psDataType, "WStr") == 0 && jVar["Var"].IsString())
	{
		pVarUnit->eDataType = DT_WStr;
		pVarUnit->pVar = (str255*)malloc(sizeof(str255));
		uNewMemory += sizeof(str255);
		strcpy((char*)pVarUnit->pVar, jVar["Var"].GetString());

		pVarUnit->uInitValLen = sizeof(str255);// strlen((char*)pVarUnit->pVar);

		pVarUnit->pInitVar = (str255*)malloc(sizeof(str255));
		uNewMemory += sizeof(str255);
		strcpy((char*)pVarUnit->pInitVar, (char*)pVarUnit->pVar);

		//dbgPrint(1, 0, "malloc:Var:%ld\n", pVarUnit->pVar);
		if (bPrint)
			dbgPrint(1, 0, "Name:%-16s Type:%-10s Var:%s\n",
				pVarUnit->sName,
				psDataType,
				pVarUnit->pVar);
	}
	else
	{

		if (bGlobal)
			errorInfo.ErrCode = 6;
		else
			errorInfo.ErrCode = 1301;// 45;

		errorInfo.eErrLever = Error;
	}

	ErrorInfoPack(&errorInfo, "TypeSwitchAndGetVar", "psDataType:%s", psDataType);
	return errorInfo;
}


void ResetLocalVar()
{
	if (pSysShareData->bAutoResetLocalVar)//sLocalVar.bAutoReset)
	{
		for (uint i = 0; i < sLocalVar.uVarCount; i++)
		{
			memset(sLocalVar.aSVariableVar[i].pVar, 0, sLocalVar.aSVariableVar[i].uInitValLen);
			memcpy(sLocalVar.aSVariableVar[i].pVar, sLocalVar.aSVariableVar[i].pInitVar, sLocalVar.aSVariableVar[i].uInitValLen);
			//if (sLocalVar.aSVariableVar[i].eDataType == DT_F32)
			//    dbgPrint(1, 0, "ResetLocalVar Name:%-16s pVar:%-10f ->pInitVar:%f\n",
			//        sLocalVar.aSVariableVar[i].sName,
			//        *(float32*)sLocalVar.aSVariableVar[i].pVar,
			//        *(float32*)sLocalVar.aSVariableVar[i].pInitVar);
		}
	}
}

/* ResolverLoaclVar              //从Json文本中load Motion参数
* @param[in]     jFileInfo
* @return        ErrorInfo
*/
ErrorInfo ResolverLocalVar(char* psLoaclVar)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	rapidjson::Document docLoaclVar;
	docLoaclVar.Parse(psLoaclVar);

	if (docLoaclVar.IsArray())
	{
		str255 sTmpType;
		sLocalVar.uVarCount = 0;
		for (auto& jLocalVar : docLoaclVar.GetArray())
		{
			if (jLocalVar.IsNull())
			{
				if (!SYSTEM_MOVE_SERIES)
				{
					errorInfo.eErrLever = Error;
					errorInfo.ErrCode = 1305;// 132;
				}
			}
			else
			{
				if ((jLocalVar.HasMember("Name") && jLocalVar["Name"].IsString()) &&
					(jLocalVar.HasMember("DataType") && jLocalVar["DataType"].IsString())
					&& jLocalVar.HasMember("Var"))
				{
					if (sLocalVar.uVarCount < GLOBALVAR_MAX_NUMBER)
					{
						memset(sLocalVar.aSVariableVar[sLocalVar.uVarCount].sKey, 0, sizeof(str16));
						if (strlen(jLocalVar["SdoKey"].GetString()) < 255 && strlen(jLocalVar["SdoKey"].GetString()) < 255)
						{
							strcpy(sLocalVar.aSVariableVar[sLocalVar.uVarCount].sKey, jLocalVar["SdoKey"].GetString());
						}

						memset(sTmpType, 0, sizeof(str255));
						memset(sLocalVar.aSVariableVar[sLocalVar.uVarCount].sName, 0, sizeof(str255));
						if (strlen(jLocalVar["Name"].GetString()) < 255 && strlen(jLocalVar["DataType"].GetString()) < 255)
						{
							strcpy(sLocalVar.aSVariableVar[sLocalVar.uVarCount].sName, jLocalVar["Name"].GetString());
							strcpy(sTmpType, jLocalVar["DataType"].GetString());
							errorInfo = TypeSwitchAndGetVar(sTmpType, jLocalVar, &sLocalVar.aSVariableVar[sLocalVar.uVarCount], false);
							sLocalVar.uVarCount++;
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 1304;
						}
						if (errorInfo.ErrCode)
							break;
						//需要根据
					}
					else
					{
						errorInfo.eErrLever = Error;
						errorInfo.ErrCode = 1302; //溢出
					}
				}
				//else
				//{
				//    if (!SYSTEM_MOVE_SERIES)
				//    {
				//        errorInfo.eErrLever = Error;
				//        errorInfo.ErrCode = 1303;// 48;
				//    }
				//}
			}
		}

		if (!errorInfo.ErrCode)
		{
			UpdateLocalVarSdoObject();
		}
	}
	//else
	//{
	//    if (!SYSTEM_MOVE_SERIES)
	//    {
	//        errorInfo.ErrCode = 1303;
	//        errorInfo.eErrLever = Error;
	//    }
	//}

	ErrorInfoPack(&errorInfo, "ResolverLocalVar", "");
	return  errorInfo;
}

void ResetGlobalVar()
{
	if (bAutoResetGlobalVar)//sSysGlobalVar.bAutoReset)
	{
		for (uint i = 0; i < sSysGlobalVar.uVarCount; i++)
			memcpy(sSysGlobalVar.aSVariableVar[i].pVar, sSysGlobalVar.aSVariableVar[i].pInitVar, sSysGlobalVar.aSVariableVar[i].uInitValLen);
	}
}

ErrorInfo UpdateLocalVarSdoObject()
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));
	memset(&entryLocalVar[1], 0, 50 * sizeof(SDOEntryDesc));

	uint32 uIndex = 0;
	uint8 iSubIndex = 0;
	if (sLocalVar.uVarCount > 0)
	{
		for (uint i = 0; i < sLocalVar.uVarCount; i++)
		{
			if (sscanf(sLocalVar.aSVariableVar[i].sKey, "0x%6X", &uIndex) < 0)
			{
				errorInfo.ErrCode = 1307;
				errorInfo.eErrLever = Error;
			}
			else
			{
				iSubIndex = (uint8)(uIndex & 0x0000FF);
				//rt_dbgPrint(1, 0, "sKey:%s uIndex:%x iSubIndex:%x\n", sLocalVar.aSVariableVar[i].sKey, uIndex, iSubIndex);
				if (iSubIndex >= 1 && iSubIndex <= 50)
				{
					entryLocalVar[iSubIndex].Name = (char*)&sLocalVar.aSVariableVar[i].sName;
					entryLocalVar[iSubIndex].Alias = "";
					entryLocalVar[iSubIndex].DataType = sLocalVar.aSVariableVar[i].eDataType;                       //EDataType DataType;
					entryLocalVar[iSubIndex].ObjAccess = ACCESS_RW;                                                 //EAccessType ObjAccess;
					entryLocalVar[iSubIndex].SaveFlag = Save_None;                                                  //ESaveFlag SaveFlag;
					entryLocalVar[iSubIndex].pVar = sLocalVar.aSVariableVar[i].pVar;                                //void* pVar;

					GetDataMaxMin(sLocalVar.aSVariableVar[i].eDataType, &entryLocalVar[iSubIndex].Maximum, &entryLocalVar[iSubIndex].Minimum);

					entryLocalVar[iSubIndex].Desc = "";                                                             //char* Desc;
					entryLocalVar[iSubIndex].ExtString = "";                                                        //char* ExtString;
					entryLocalVar[iSubIndex].BitOffset = 0;                                                         //uint8	BitOffset;
					entryLocalVar[iSubIndex].BitLength = GetDataLength(sLocalVar.aSVariableVar[i].eDataType) * 8;
				}
				else
				{
					errorInfo.ErrCode = 1306;
					errorInfo.eErrLever = Error;
				}
			}

			//rt_dbgPrint(1, 0, "\n entryLocalVar_%d[%s] DataType:%d\n",
			//    i + 1,
			//    entryLocalVar[i+1].Name,
			//    entryLocalVar[i + 1].DataType);
		}
	}

	ErrorInfoPack(&errorInfo, "UpdateLocalVarSdoObject", "");
	return errorInfo;
}


ErrorInfo UpdateGlobalVarSdoObject()
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	memset(&entryGlobalVar[1], 0, 50 * sizeof(SDOEntryDesc));
	uint32 uIndex;
	uint8 iSubIndex = 0;
	int iScanResult = 0;
	if (sSysGlobalVar.uVarCount > 0)
	{
		for (uint i = 0; i < sSysGlobalVar.uVarCount; i++)
		{
			iScanResult = sscanf((char*)&sSysGlobalVar.aSVariableVar[i].sKey, "0x%6x", &uIndex);
			iSubIndex = (uint8)(uIndex & 0x0000FF);
			// rt_dbgPrint(1, 0, "sKey:%s uIndex:%x iSubIndex:%x iScanResult:%d\n", sSysGlobalVar.aSVariableVar[i].sKey, uIndex, iSubIndex, iScanResult);
			if (iScanResult < 0)
			{
				errorInfo.ErrCode = 7;
				errorInfo.eErrLever = Error;
			}
			else
			{
				//rt_dbgPrint(1, 0, "iSubIndex:%d \n", iSubIndex);
				if (iSubIndex >= 1 && iSubIndex <= 50)
				{
					entryGlobalVar[iSubIndex].uSubIndex = iSubIndex;
					entryGlobalVar[iSubIndex].Name = (char*)&sSysGlobalVar.aSVariableVar[i].sName;
					entryGlobalVar[iSubIndex].Alias = "";
					entryGlobalVar[iSubIndex].DataType = sSysGlobalVar.aSVariableVar[i].eDataType;                       //EDataType DataType;
					entryGlobalVar[iSubIndex].ObjAccess = ACCESS_RW;                   //EAccessType ObjAccess;
					entryGlobalVar[iSubIndex].SaveFlag = Save_None;                    //ESaveFlag SaveFlag;
					entryGlobalVar[iSubIndex].pVar = sSysGlobalVar.aSVariableVar[i].pVar;    //void* pVar;

					GetDataMaxMin(sSysGlobalVar.aSVariableVar[i].eDataType, &entryGlobalVar[iSubIndex].Maximum, &entryGlobalVar[iSubIndex].Minimum);

					entryGlobalVar[iSubIndex].Desc = "";                               //char* Desc;
					entryGlobalVar[iSubIndex].ExtString = "";                          //char* ExtString;
					entryGlobalVar[iSubIndex].BitOffset = 0;                           //uint8	BitOffset;
					entryGlobalVar[iSubIndex].BitLength = GetDataLength(sSysGlobalVar.aSVariableVar[i].eDataType) * 8;
				}
				else
				{
					errorInfo.ErrCode = 8;
					errorInfo.eErrLever = Error;
				}
			}

			//if(entryGlobalVar[i + 1].DataType == 10)
			//    rt_dbgPrint(1, 0, "Update entryGlobalVar %d[%s] pVar:%f \n",
			//        entryGlobalVar[i + 1].uSubIndex,
			//        entryGlobalVar[i + 1].Name,
			//        (float32*)(entryGlobalVar[i + 1].pVar));
			//  PrintfVarBaseDataType(entryGlobalVar[i + 1].DataType, entryGlobalVar[i + 1].pVar);
		}
	}

	ErrorInfoPack(&errorInfo, "UpdateGlobalVarSdoObject", "");
	return errorInfo;
}

ErrorInfo UpdateGlobalVarSetInfo(bool bUpdateTrig)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	//rt_dbgPrint(1, 0, "\n Update GlobalVar Info From DataBase \n\n");
	char* test_version = "V1.0.0";
	char* spFieldbus = "GlobalVar";
	// 调用被测函数
	errorInfo = ReadDeviceConfigJsonOp(spFieldbus, test_version, &Configjson);
	if (!errorInfo.ErrCode)
	{
		//从数据库中获取 整个IO Json数据内容
		if (strcmp(Configjson.c_str(), "0") != 0 && strlen((char*)Configjson.c_str()) > 0)
		{
			sSysGlobalVar.uVarCount = 0;
			//开始解析 数据
			//char* psIoSet;
			rapidjson::Document docGlobalSet;
			docGlobalSet.Parse((char*)Configjson.c_str());

			str255 sTmpType;
			if (docGlobalSet.IsArray() && !docGlobalSet.IsNull())
			{
				for (auto& jGlobalVar : docGlobalSet.GetArray())
				{
					if (jGlobalVar.IsNull())
					{
						errorInfo.eErrLever = Error;
						errorInfo.ErrCode = 1;
					}
					else
					{
						if ((jGlobalVar.HasMember("Name") && jGlobalVar["Name"].IsString()) &&
							(jGlobalVar.HasMember("DataType") && jGlobalVar["DataType"].IsString())
							&& jGlobalVar.HasMember("Var"))
						{
							if (sSysGlobalVar.uVarCount < GLOBALVAR_MAX_NUMBER)
							{
								memset(sSysGlobalVar.aSVariableVar[sSysGlobalVar.uVarCount].sKey, 0, sizeof(str16));
								if (strlen(jGlobalVar["SdoKey"].GetString()) < 255 && strlen(jGlobalVar["SdoKey"].GetString()) < 255)
								{
									strcpy(sSysGlobalVar.aSVariableVar[sSysGlobalVar.uVarCount].sKey, jGlobalVar["SdoKey"].GetString());
								}

								memset(sTmpType, 0, sizeof(str255));
								memset(sSysGlobalVar.aSVariableVar[sSysGlobalVar.uVarCount].sName, 0, sizeof(str255));
								if (strlen(jGlobalVar["Name"].GetString()) < 255 && strlen(jGlobalVar["DataType"].GetString()) < 255)
								{
									strcpy(sSysGlobalVar.aSVariableVar[sSysGlobalVar.uVarCount].sName, jGlobalVar["Name"].GetString());
									strcpy(sTmpType, jGlobalVar["DataType"].GetString());
									errorInfo = TypeSwitchAndGetVar(sTmpType, jGlobalVar, &sSysGlobalVar.aSVariableVar[sSysGlobalVar.uVarCount], true);
									if (errorInfo.ErrCode)
									{
										errorInfo.eErrLever = Error;
										errorInfo.ErrCode = 5;
									}
									sSysGlobalVar.uVarCount++;
								}
								else
								{
									errorInfo.eErrLever = Error;
									errorInfo.ErrCode = 4;
								}
								if (errorInfo.ErrCode)
									break;
								//需要根据
							}
							else
							{
								errorInfo.eErrLever = Error;
								errorInfo.ErrCode = 2; //溢出
							}
						}
						else
						{
							errorInfo.eErrLever = Error;
							errorInfo.ErrCode = 3;
						}
					}
				}
			}

			//rt_dbgPrint(1, 0, "GlobalVar Count:%d\n", sSysGlobalVar.uVarCount);
			//更新 0xB001 sdo组
			errorInfo = UpdateGlobalVarSdoObject();
		}
		else
		{
			rt_dbgPrint(1, 0, "\n GlobalVar Info Is NULL \n\n");
		}
	}

	ErrorInfoPack(&errorInfo, "UpdateGlobalVarSetInfo", "");


	return errorInfo;
}