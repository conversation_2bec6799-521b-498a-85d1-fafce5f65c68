#ifndef __Measurement_H
#define __Measurement_H
#include "base.h"
#include "SequenceItem.h"
#pragma pack(1)

typedef enum
{
	Auto = 0,
	ManualOp = 1,
	PLC_IO,
	xTrigger,
	yTrigger,
	TimeMode,
	CurveReturn
}EMeasure_Mode;

typedef enum
{
	LeftToRight = 1,
	RightToLeft,
	UpToDown,
	DownToUp,
}ETriggerDirection;

typedef struct
{
	DynaVar					dTriggerVar;
	ETriggerDirection		eDirection;
}SMeasureStart_TriggerCfg;

typedef struct
{
	DynaVar					dTriggerVar;
	ETriggerDirection		eDirection;
	DynaVar					dTimeOut;
}SMeasureStop_TriggerCfg;

typedef struct
{
	DynaVar					dTime;
}SMeasure_TimeCfg;


typedef enum
{
	Null = 0,
	Left_Reutrn = 1,
	Right_Return,
}EReturnMode;

typedef enum
{
	null = 0,
	Xmin = 1,
	Xmax,
	Ymin,
	Ymax,
}EReturnType;

typedef enum
{
	None_Processing,
	Forward_Processing,
	Backward_Processing
}ECurveProcessing;

typedef struct
{
	EReturnMode				eReturnMode;
	EReturnType				eReturnType;
	ECurveProcessing		eCurveProcess;
}SMeasure_ReturnCfg;

typedef struct
{
	bool bTrig;
}SMeasure_ManualOptext;

typedef struct
{
	bool		bHadInitTime;	//是否初始化时间；
	uint64		initTimestamp;	//初始化时间戳
	uint64		currTimestamp;	//当前时间戳
	DynaVar		dVarWaitTime;	//= 当前时间戳- 初始化时间戳
}SMeasure_TimeContext;

typedef struct
{
	float32 fXmax;
	float32 fYmax;
	float32 fXmin;
	float32 fYmin;
	float32 fXLast;
	float32 fYLast;
}SMeasure_Returntext;
typedef struct
{
	union
	{
		SMeasure_ManualOptext	sPlcIo;
		SMeasure_ManualOptext	sxTrigger;
		SMeasure_ManualOptext	syTrigger;
	}uMeasureContex;
}MeasurementStart_Context;
typedef struct
{
	bool						bPauseRequest;
	bool						bStopRequest;
	union
	{
		SMeasure_ManualOptext	sManualOp;
		SMeasure_ManualOptext	sPlcIo;
		SMeasure_TimeContext	sxTrigger;
		SMeasure_TimeContext	syTrigger;
		SMeasure_TimeContext	sfTime;
		SMeasure_Returntext		sReturn;
	}uMeasureContex;
}MeasurementStop_Context;

typedef struct _SeqItem_MeasurementStart
{
	EMeasure_Mode			eMeasureMode;
	union
	{
		SMeasure_TimeCfg				sfTimePara;
		SMeasureStart_TriggerCfg		syTriggerPara;
		SMeasureStart_TriggerCfg		sxTriggerPara;
		SMeasure_ReturnCfg				sReturnPara;
	}uMeasurePara;

	MeasurementStart_Context StartContext;
}SeqItem_MeasurementStart;

typedef struct _SeqItem_MeasurementStop
{
	EMeasure_Mode			eMeasureMode;
	union
	{
		SMeasure_TimeCfg			sfTimePara;
		SMeasureStop_TriggerCfg		syTriggerPara;
		SMeasureStop_TriggerCfg		sxTriggerPara;
		SMeasure_ReturnCfg			sReturnPara;
	}uMeasurePara;
	MeasurementStop_Context		StopContext;
}SeqItem_MeasurementStop;

//Measure开始结束类型
typedef struct
{
	EMeasure_Mode MeasureStart;
	EMeasure_Mode MeasureStop;
}MeasureType;

//折返点
typedef struct
{
	bool ReturnState;			//是否采用折返
	EReturnMode	ReturnMode;		//折返模式
	EReturnType	ReturnType;		//折返类型
	SMeasure_Returntext* ReturnValue;			//折返点极值
	uint32 ReturnIndex;			//折返点索引
	bool ReturnStop;				//折返结束标识
}ReturnPoint;

#pragma pack()

extern ErrorInfo SeqItem_MeasurementStart_Start(SeqItem* pSeqItem);
extern ErrorInfo SeqItem_MeasurementStart_Init(SeqItem* pSeqItem);
extern ErrorInfo SeqItem_MeasurementStart_Execute(SeqItem* pSeqItem);

extern ErrorInfo SeqItem_MeasurementStop_Start(SeqItem* pSeqItem);
extern ErrorInfo SeqItem_MeasurementStop_Init(SeqItem* pSeqItem);
extern ErrorInfo SeqItem_MeasurementStop_Execute(SeqItem* pSeqItem);

ErrorInfo SeqItem_MeasurementStart_Judge(bool* pbFirstJudgePoint, bool* pbManualFlag, bool* pbPlcIO, float32* pxValue, float32* pyValue, bool* pbStartRunSeq);
extern SeqItem_MeasurementStart* pMeasurementStart;
extern SeqItem_MeasurementStop* pMeasurementStop;
extern ReturnPoint pReturnPoint;
extern SMeasure_Returntext pSMeasure_Returntext;
extern MeasureType pMeasureType;
#endif
