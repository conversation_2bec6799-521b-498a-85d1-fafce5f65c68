/********************************************************************************
 * File: anybus2spiHost.c
 * Author: Gloss Tsai
 * Created on: January 26, 2024
 * Description:
 *      Used to transfer data from bus to 1176 via SPI.
 *      Slave stands for busboard while Host stands for 1176.
 * Note:
 *      https://alidocs.dingtalk.com/i/nodes/a9E05BDRVQvv6GQyCBqzPQ0DJ63zgkYA
 * Copyright (C) 2024 Leetx. All rights reserved.
 *******************************************************************************/
 /* Includes ------------------------------------------------------------------ */
#include "anybus2spiHost.h"
#include <string.h>
#include "bsp_anybus2spi.h"

#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
#include "cr_section_macros.h"
#endif
/* Private typedef ----------------------------------------------------------- */
/* Private macro ------------------------------------------------------------- */
/* CONFIG MACRO */
#ifndef USE_BLOKING_TRANSFER_MODE
#error "Macro 'USE_BLOKING_TRANSFER_MODE' must be defined in bsp_anybus2spi.h"
#endif
#ifndef IMXRT1176_USE_ITCM
/* 使用RT1176时，需要使用该宏来确定是否将循环时调用的函数置入ITCM内存区域 */
#warning "Macro 'IMXRT1176_USE_ITCM' should be defined in bsp_anybus2spi.h"
#endif
#ifndef IMXRT1176_USE_SPIEDMA
/* 使用RT1176的SPI-EMDA方式通信时，定义该宏为1，来将数据交换用的数据放置在非缓存区 */
#warning "Macro 'IMXRT1176_USE_SPIEDMA' should be defined in bsp_anybus2spi.h"
#endif
#ifndef FSM_SCHEDULE_PERIOD_US
#error "Macro 'FSM_SCHEDULE_PERIOD_US' must be defined in bsp_anybus2spi.h"
#else
#if ((FSM_SCHEDULE_PERIOD_US < 500)||(FSM_SCHEDULE_PERIOD_US > 5000))
#error "Macro 'FSM_SCHEDULE_PERIOD_US' should be between 500 and 5000"
#endif
#endif
/* Private variables --------------------------------------------------------- */
/* static */Anybus_HostStatus_t tHostStatus = { 0 };
#if defined(IMXRT1176_USE_SPIEDMA) && (IMXRT1176_USE_SPIEDMA == 1)
/* 直接用于SPI通信的数组，EDMA将数据存放在此变量中 */
AT_NONCACHEABLE_SECTION_INIT(uint8_t ucaSlave2Host[WORKING_MODE_MAX_FRAME_LENGTH]) = { 0 };
AT_NONCACHEABLE_SECTION_INIT(uint8_t ucaHost2Slave[WORKING_MODE_MAX_FRAME_LENGTH]) = { 0 };
#else
uint8_t ucaSlave2Host[WORKING_MODE_MAX_FRAME_LENGTH] = { 0 };	/* SPI通信SLAVE->HOST数组 */
uint8_t ucaHost2Slave[WORKING_MODE_MAX_FRAME_LENGTH] = { 0 };	/* SPI通信HOST->SLAVE数组 */
#endif
AB2S_LIFOHandle_t tDataSwapLIFO2Slave = { 0 };
AB2S_LIFOHandle_t tDataSwapLIFO2Host = { 0 };
/* Private function prototypes ----------------------------------------------- */
static void HandleConfigState(void);
static void HandleWorkingState(void);
static void HandleWaitResponseState(void);
static void HandleCRCCheckState(void);
static void HandleTimeout(uint16_t heartBeat);

static void ProcessCRCCheckCorrectState(void);

static void SendCMD_Config_GetAnybusType(void);
static void SendCMD_Config_SetWorkingDataLen(void);
static void SendCMD_Config_GetWorkingDataLen(void);
static void SendCMD_Config_EnterWorkingMode(void);
static void SendCMD_Working_KeepTransmiting(void);
static void SendCMD_Working_EnterConfigMode(void);
static void SendCMD_Config_SetCCLinkConfigInfo(void);
static void SendCMD_Config_SetProfibusConfigInfo(void);
static void SendCMD_Config_SetNetX90ConfigInfo(void);

static void Anybus2spi_SPITransmitReceive(void);
static AB2SErrorCode_e Anybus2spi_SetWorkingDataLen(uint16_t host2busLen, uint16_t bus2hostLen);
/* Private functions --------------------------------------------------------- */
/**
 * @brief 调用该函数，将会往SPI发送ucaHost2Slave中的数据，且将MISO数据读取至ucaSlave2Host
*/
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
static void Anybus2spi_SPITransmitReceive()
{
    tHostStatus.runState.SPIComTxTotalCnt++;
    Bsp_SPITransmitReceive(ucaHost2Slave, ucaSlave2Host, tHostStatus.dataLen.TxRxLen);
#if defined(USE_BLOKING_TRANSFER_MODE) && (USE_BLOKING_TRANSFER_MODE == 1)
    /* 使用阻塞方式的SPI发送，发送完成后，需要立刻进行CRC计算 */
    if (tHostStatus.FSMState == CRC_CHECK_STATE)
    {
        HandleCRCCheckState();
    }
#endif
}
/**
 * @brief 初始化状态机等内容
 * @note 需要首先调用的函数
 */
void Anybus2spi_Init()
{
    tHostStatus.BusType = AnybusNone;
    tHostStatus.dataLen.ToBusLen = 0;
    tHostStatus.dataLen.FromBusLen = 0;
    tHostStatus.dataLen.TxRxLen = INSTRUCTION_MODE_FRAME_LENGTH;
    AB2S_DecodeVersion(AB2S_GetVersionCode(), tHostStatus.tVersion.AB2SLibVersion_Host);

    Anybus2spi_CleanRunStateRecordParam();
}
void Anybus2spi_Deinit()
{
    /**
     * @todo 资源竞争问题
     * 由于tHostStatus变量的资源竞争问题，
     * 清空tHostStatus变量的内容可能无法暂停FSM的执行
     * 例如：执行HandleConfigState()函数时，若调用Anybus2spi_Deinit()，
     *     会导致tHostStatus.FSMEvent再次被赋值。
     */
    memset(&tHostStatus, 0x0u, sizeof(tHostStatus));
}
/**
 * @brief 配置工作模式的数据长度，配置完成后自动进入工作模式
 * @param host2busLen 工作模式下，发送到总线的数据长度
 * @param bus2hostLen 工作模式下，从总线接收的数据长度
 * @warning 状态机(即Anybus2spi_HandleTIMPeriodElapsedCallback函数)
 * 		运行后，仅允许调用本函数一次。再次调用本函数之前，需要先暂停调用状态机,
 * 		否则可能导致意料之外的问题。
 */
static AB2SErrorCode_e Anybus2spi_SetWorkingDataLen(uint16_t host2busLen, uint16_t bus2hostLen)
{
    if (tHostStatus.BusType == AnybusNone)
    {
        return AB2SPIE_BUSTYPE;	/* 未成功获取总线类型，模块不在线或通信无法建立 */
    }
    if (tHostStatus.BusType == CC_LINK && tHostStatus.pCCLConfigInfo == NULL)
    {
        /* CC-LINK 不允许直接配置数据长度，其数据长度由站数与倍数确定 */
        return AB2SPIE_BUSTYPE;
    }
    if (host2busLen > WORKING_MODE_MAX_DATA_LENGTH || bus2hostLen > WORKING_MODE_MAX_DATA_LENGTH)
    {
        return AB2SPIE_DATALEN;	/* 传入参数不符合规定 */
    }
    tHostStatus.FSMEvent = HOST_INSTRUCTION_NONE;
    tHostStatus.FSMState = BEFORE_INITIALIZATION;

    if (tDataSwapLIFO2Slave.maxSize != 0)
    {
        LIFO_Deinit(&tDataSwapLIFO2Slave);
    }
    if (tDataSwapLIFO2Host.maxSize != 0)
    {
        LIFO_Deinit(&tDataSwapLIFO2Host);
    }
    if (LIFO_Init(&tDataSwapLIFO2Slave, host2busLen) != true)
    {
        return AB2SPIE_HEAP;
    }
    if (LIFO_Init(&tDataSwapLIFO2Host, bus2hostLen) != true)
    {
        LIFO_Deinit(&tDataSwapLIFO2Slave);
        return AB2SPIE_HEAP;
    }

    tHostStatus.dataLen.ToBusLen = host2busLen;
    tHostStatus.dataLen.FromBusLen = bus2hostLen;
    tHostStatus.dataLen.MaxDataFramePhase = host2busLen > bus2hostLen ? host2busLen : bus2hostLen;
    tHostStatus.FSMEvent = HOST_INSTRUCTION_SET_WORKING_DATA_LEN;
    tHostStatus.FSMState = CONFIG_STATE;
    return AB2SPIE_OK;	/* 成功 */
}
/**
 * @brief 实现anybus2spi库的有限状态机，指定状态与指定事件的组合形成一个状态迁移路径，对应执行某个操作（函数）
 * @note  由用户在定时器中断中调用，或线程中调用。由于数据量较大，若在中断中调用，请勿使用阻塞方式SPI通信。
 */
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
void Anybus2spi_HandleTIMPeriodElapsedCallback()
{
    switch (tHostStatus.FSMState)
    {
    case BEFORE_INITIALIZATION:
        break;
    case CONFIG_STATE:
        HandleConfigState();
        break;
    case WORKING_STATE:
        /* 工作状态下，无错误时的状态迁移只有 WORKING_STATE->CRC_CHECK_STATE->WORKING_STATE 循环*/
        HandleWorkingState();
        break;
    case REQUEST_RESPON_STATE:
        HandleWaitResponseState();
        break;
    default:
        break;
    }
    HandleTimeout(FSM_SCHEDULE_PERIOD_US);
}
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
static void HandleTimeout(uint16_t heartBeat)
{
    tHostStatus.runState.timeTick += heartBeat;
    if (tHostStatus.runState.ModuleLinkStatus == ANYBUS_LINK_STATUS_OK)
        tHostStatus.runState.ModuleOfflineTick += heartBeat;
    if (tHostStatus.runState.timeTick >= FSM_TIMEOUT_TICK)
    {
        tHostStatus.runState.timeTick = 0;
        switch (tHostStatus.FSMEvent)
        {
        case HOST_INSTRUCTION_ENTER_WORKING_MODE:
        case HOST_INSTRUCTION_KEEP_DATA_TRANSFERING:
            tHostStatus.dataLen.TxRxLen = INSTRUCTION_MODE_FRAME_LENGTH;
            switch (tHostStatus.BusType)
            {
            case CC_LINK:
                tHostStatus.FSMEvent = HOST_INSTRUCTION_CCLINK_CONFIG_INFO;
                break;
            case Profinet:
            case Ethercat:
            case Ethernet_IP:
                tHostStatus.FSMEvent = HOST_INSTRUCTION_NETX90_CONFIG_INFO;
                break;
            case Profibus:
                tHostStatus.FSMEvent = HOST_INSTRUCTION_DPV1_CONFIG_INFO;
                break;
            default:
                tHostStatus.FSMEvent = HOST_INSTRUCTION_SET_WORKING_DATA_LEN;
                break;
            }
            tHostStatus.FSMState = CONFIG_STATE;
            tHostStatus.runState.Work2ConfigCnt++;
            break;
        case HOST_INSTRUCTION_SET_WORKING_DATA_LEN:
        case HOST_INSTRUCTION_GET_WORKING_DATA_LEN:
            tHostStatus.FSMEvent = HOST_INSTRUCTION_SET_WORKING_DATA_LEN;
            break;
        default:
            break;
        }
    }
    if (tHostStatus.runState.ModuleOfflineTick > MODULE_LINK_OFFLINE_TICK && tHostStatus.runState.ModuleLinkStatus == ANYBUS_LINK_STATUS_OK)
    {
        /* 首次通信成功后，若模块未响应超过MODULE_LINK_OFFLINE_TICK微秒，则认为模块离线 */
        tHostStatus.runState.ModuleLinkStatus = ANYBUS_LINK_STATUS_DISCONNECT;
        tHostStatus.runState.BusLinkStatus = ANYBUS_LINK_STATUS_DISCONNECT;
        /* 即使模块离线也继续尝试恢复 */
    }
}
static void HandleConfigState()
{
    switch (tHostStatus.FSMEvent)
    {
    case HOST_INSTRUCTION_GET_ANYBUS_TYPE:
        SendCMD_Config_GetAnybusType();
        break;
    case HOST_INSTRUCTION_SET_WORKING_DATA_LEN:
        SendCMD_Config_SetWorkingDataLen();
        break;
    case HOST_INSTRUCTION_GET_WORKING_DATA_LEN:
        SendCMD_Config_GetWorkingDataLen();
        break;
    case HOST_INSTRUCTION_CCLINK_CONFIG_INFO:
        SendCMD_Config_SetCCLinkConfigInfo();
        break;
    case HOST_INSTRUCTION_DPV1_CONFIG_INFO:
        SendCMD_Config_SetProfibusConfigInfo();
        break;
    case HOST_INSTRUCTION_NETX90_CONFIG_INFO:
        SendCMD_Config_SetNetX90ConfigInfo();
        break;
    case HOST_INSTRUCTION_ENTER_WORKING_MODE:
        SendCMD_Config_EnterWorkingMode();
        break;
    default:
        /* 非上述事件为无效事件，不进入等待回复状态 */
        return;
    }
    /* 发送完指令后进入等待回复状态 */
    tHostStatus.FSMState = REQUEST_RESPON_STATE;
}
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
static void HandleWorkingState()
{
    switch (tHostStatus.FSMEvent)
    {
    case HOST_INSTRUCTION_ENTER_CONFIGURATION_MODE:
        /** @todo未实现该功能*/
        SendCMD_Working_EnterConfigMode();
        tHostStatus.FSMState = REQUEST_RESPON_STATE;
        break;
    case HOST_INSTRUCTION_KEEP_DATA_TRANSFERING:
        /* 持续传输模式不需要额外的时钟信号来获取数据帧 */
        /* 先改变tHostStatus.FSMState 为 CRC_CHECK_STATE,来确保阻塞式发送时,能够立刻进行CRC运算 */
        tHostStatus.FSMState = CRC_CHECK_STATE;
        SendCMD_Working_KeepTransmiting();
        break;
    default:
        /* @todo state为WorkingMode但事件不对 */
        break;
    }
}
/**
 * @brief 由于SPI的同步通信机制，每次发送指令之后，需要再一次主动发起通信，以获取指令的反馈
 */
static void HandleWaitResponseState()
{
    CRC16CCITT_MemsetBufferWithCRC16(ucaHost2Slave, 0x81, tHostStatus.dataLen.TxRxLen);

    tHostStatus.FSMState = CRC_CHECK_STATE;
    Anybus2spi_SPITransmitReceive();

    /* 等待回复帧不计入总发送次数 */
    if (tHostStatus.runState.SPIComTxTotalCnt >= 1)
    {
        tHostStatus.runState.SPIComTxTotalCnt--;
    }
}
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
static void HandleCRCCheckState()
{
    if (CRC16CCITT_Check(ucaSlave2Host, tHostStatus.dataLen.TxRxLen))
    {
        tHostStatus.runState.SPIComRxTotalCnt++;
        ProcessCRCCheckCorrectState();
    }
    else
    {
        tHostStatus.runState.CRCErrorCnt++;
        if (tHostStatus.FSMEvent != HOST_INSTRUCTION_KEEP_DATA_TRANSFERING)
        {
            /* 事件不变，状态返回配置模式，则将执行对应的操作 */
            tHostStatus.dataLen.TxRxLen = INSTRUCTION_MODE_FRAME_LENGTH;
            tHostStatus.FSMState = CONFIG_STATE;
        }
        else
        {
            tHostStatus.FSMState = WORKING_STATE;
        }
    }
}
/**
 * @brief 用于CRC校验正确状态下的各个事件的通信
 */
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
static void ProcessCRCCheckCorrectState()
{
    Anybus_SlaveData_t slaveFB = { 0 };
    switch (tHostStatus.FSMEvent)
    {
    case HOST_INSTRUCTION_GET_ANYBUS_TYPE:
        memcpy(&slaveFB, ucaSlave2Host, tHostStatus.dataLen.TxRxLen);
        if (slaveFB.hostCommand == HOST_INSTRUCTION_GET_ANYBUS_TYPE)
        {
            uint32_t SlaveVersionCode = 0;
            tHostStatus.runState.timeTick = 0;
            tHostStatus.runState.ConfigModeCpltCnt++;
            /* 若获取从机总线类型成功，则进入配置模式，等待用户设置工作模式数据长度 */
            tHostStatus.BusType = slaveFB.Data[1] << 8 | slaveFB.Data[0];
            memcpy(&SlaveVersionCode, slaveFB.Data + 2, sizeof(uint32_t));
            AB2S_DecodeVersion(SlaveVersionCode, tHostStatus.tVersion.AB2SLibVersion_Slave);
            memcpy(tHostStatus.tVersion.ModuleSoftWareVersion, slaveFB.Data + 6, sizeof(tHostStatus.tVersion.ModuleSoftWareVersion));
            memcpy(tHostStatus.tVersion.ModuleHardWareVersion, slaveFB.Data + 6 + sizeof(tHostStatus.tVersion.ModuleSoftWareVersion), sizeof(tHostStatus.tVersion.ModuleHardWareVersion));
            tHostStatus.FSMState = CONFIG_STATE;
            tHostStatus.FSMEvent = HOST_INSTRUCTION_NONE;
        }
        else
        {
            /* 指令校验错误，重新发起获取从机总线类型 */
            tHostStatus.FSMState = CONFIG_STATE;
        }
        break;
    case HOST_INSTRUCTION_SET_WORKING_DATA_LEN:
        if (ucaSlave2Host[0] == HOST_INSTRUCTION_SET_WORKING_DATA_LEN)
        {
            tHostStatus.runState.timeTick = 0;
            tHostStatus.runState.ConfigModeCpltCnt++;
            tHostStatus.FSMState = CONFIG_STATE;
            tHostStatus.FSMEvent = HOST_INSTRUCTION_GET_WORKING_DATA_LEN;
        }
        else
        {
            /* 指令校验错误，重新发起设置总线长度 */
            tHostStatus.FSMState = CONFIG_STATE;
        }
        break;
    case HOST_INSTRUCTION_GET_WORKING_DATA_LEN:
        memcpy(&slaveFB, ucaSlave2Host, tHostStatus.dataLen.TxRxLen);
        if (slaveFB.hostCommand == HOST_INSTRUCTION_GET_WORKING_DATA_LEN)
        {
            uint16_t S2HLen = slaveFB.Data[1] << 8 | slaveFB.Data[0];
            uint16_t H2SLen = slaveFB.Data[3] << 8 | slaveFB.Data[2];

            tHostStatus.runState.timeTick = 0;
            tHostStatus.runState.ConfigModeCpltCnt++;

            /* 判断工作模式数据长度是否配置成功 */
            if (S2HLen == tHostStatus.dataLen.FromBusLen
                && H2SLen == tHostStatus.dataLen.ToBusLen)
            {
                tHostStatus.FSMState = CONFIG_STATE;
                tHostStatus.FSMEvent = HOST_INSTRUCTION_ENTER_WORKING_MODE;
            }
            else
            {
                /* 配置错误，重新配置 */
                tHostStatus.FSMState = CONFIG_STATE;
                tHostStatus.FSMEvent = HOST_INSTRUCTION_SET_WORKING_DATA_LEN;
            }
        }
        else
        {
            /* 指令校验错误，重新读取从机工作模式总线数据长度 */
            tHostStatus.FSMState = CONFIG_STATE;
        }
        break;
    case HOST_INSTRUCTION_CCLINK_CONFIG_INFO:
        if (ucaSlave2Host[0] == HOST_INSTRUCTION_CCLINK_CONFIG_INFO)
        {
            tHostStatus.runState.timeTick = 0;
            tHostStatus.runState.ConfigModeCpltCnt++;
            Anybus2spi_SetWorkingDataLen(tHostStatus.pCCLConfigInfo->RO_CCL_DATALEN, tHostStatus.pCCLConfigInfo->RO_CCL_DATALEN);
        }
        else
        {
            tHostStatus.FSMState = CONFIG_STATE;
        }
        break;
    case HOST_INSTRUCTION_DPV1_CONFIG_INFO:
        if (ucaSlave2Host[0] == HOST_INSTRUCTION_DPV1_CONFIG_INFO)
        {
            tHostStatus.runState.timeTick = 0;
            tHostStatus.runState.ConfigModeCpltCnt++;
            Anybus2spi_SetWorkingDataLen(tHostStatus.tProfiBusConfigInfo.Device2PLCLen, tHostStatus.tProfiBusConfigInfo.PLC2DeviceLen);
        }
        else
        {
            tHostStatus.FSMState = CONFIG_STATE;
        }
        break;
    case HOST_INSTRUCTION_NETX90_CONFIG_INFO:
        if (ucaSlave2Host[0] == HOST_INSTRUCTION_NETX90_CONFIG_INFO)
        {
            tHostStatus.runState.timeTick = 0;
            tHostStatus.runState.ConfigModeCpltCnt++;
            switch (tHostStatus.tNetX90ConfigInfo.eDataSwapLen)
            {
            case AB2S_Tx64_Rx64:
                Anybus2spi_SetWorkingDataLen(64, 64);
                break;
            case AB2S_Tx128_Rx128:
                Anybus2spi_SetWorkingDataLen(128, 128);
                break;
            case AB2S_Tx220_Rx220:
                Anybus2spi_SetWorkingDataLen(220, 220);
                break;
            default:
                break;
            }
        }
        else
        {
            tHostStatus.FSMState = CONFIG_STATE;
        }
        break;
    case HOST_INSTRUCTION_ENTER_WORKING_MODE:
        if (ucaSlave2Host[0] == HOST_INSTRUCTION_ENTER_WORKING_MODE)
        {
            tHostStatus.runState.timeTick = 0;
            tHostStatus.FSMState = WORKING_STATE;
            tHostStatus.runState.ConfigModeCpltCnt++;
            tHostStatus.FSMEvent = HOST_INSTRUCTION_KEEP_DATA_TRANSFERING;
            memset(ucaHost2Slave, 0, tHostStatus.dataLen.TxRxLen);
        }
        else
        {
            /* 指令校验错误，重新发送进入工作模式指令 */
            tHostStatus.dataLen.TxRxLen = INSTRUCTION_MODE_FRAME_LENGTH;
            tHostStatus.FSMState = CONFIG_STATE;
        }
        break;
    case HOST_INSTRUCTION_KEEP_DATA_TRANSFERING:
        if (ucaSlave2Host[0] == HOST_INSTRUCTION_KEEP_DATA_TRANSFERING)
        {
            /* 通信成功则清空错误数量 */
            tHostStatus.runState.timeTick = 0;
            tHostStatus.runState.ModuleOfflineTick = 0;
            tHostStatus.runState.WorkingModeCpltCnt++;
            tHostStatus.runState.ModuleLinkStatus = ANYBUS_LINK_STATUS_OK;
            tHostStatus.FSMState = WORKING_STATE;
            /* 复制从SPI传输到主机的总线数据到缓冲区 */
            LIFO_WriteMessage(&tDataSwapLIFO2Host, ucaSlave2Host + INSTRUCTION_MODE_HEAD_LEN, tHostStatus.dataLen.FromBusLen);
            tHostStatus.runState.BusLinkStatus = (ucaSlave2Host[2] >> 7) & 0x01u;
        }
        else
        {
            tHostStatus.FSMState = WORKING_STATE;
            /** 通信失败处理统一纳入超时管理函数中处理 */
        }
        break;
    case HOST_INSTRUCTION_ENTER_CONFIGURATION_MODE:
        break;
    default:
        break;
    }
}
static void SendCMD_Config_GetAnybusType()
{
    Anybus_HostData_t hostCMD = { 0 };

    hostCMD.Instruction = HOST_INSTRUCTION_GET_ANYBUS_TYPE;
    memcpy(ucaHost2Slave, (uint8_t*)&hostCMD, tHostStatus.dataLen.TxRxLen);
    CRC16CCITT_AppendCalc(ucaHost2Slave, tHostStatus.dataLen.TxRxLen);
    Anybus2spi_SPITransmitReceive();
}
static void SendCMD_Config_SetWorkingDataLen()
{
    Anybus_HostData_t hostCMD = { 0 };

    hostCMD.Instruction = HOST_INSTRUCTION_SET_WORKING_DATA_LEN;
    hostCMD.LengthOfValidData = sizeof(tHostStatus.dataLen.FromBusLen) + sizeof(tHostStatus.dataLen.ToBusLen);
    hostCMD.Data[0] = tHostStatus.dataLen.FromBusLen & 0xFF;
    hostCMD.Data[1] = tHostStatus.dataLen.FromBusLen >> 8;
    hostCMD.Data[2] = tHostStatus.dataLen.ToBusLen & 0xFF;
    hostCMD.Data[3] = tHostStatus.dataLen.ToBusLen >> 8;
    memcpy(ucaHost2Slave, (uint8_t*)&hostCMD, tHostStatus.dataLen.TxRxLen);
    CRC16CCITT_AppendCalc(ucaHost2Slave, tHostStatus.dataLen.TxRxLen);
    Anybus2spi_SPITransmitReceive();
}
static void SendCMD_Config_GetWorkingDataLen()
{
    Anybus_HostData_t hostCMD = { 0 };

    hostCMD.Instruction = HOST_INSTRUCTION_GET_WORKING_DATA_LEN;
    memcpy(ucaHost2Slave, (uint8_t*)&hostCMD, tHostStatus.dataLen.TxRxLen);
    CRC16CCITT_AppendCalc(ucaHost2Slave, tHostStatus.dataLen.TxRxLen);
    Anybus2spi_SPITransmitReceive();
}
static void SendCMD_Config_SetCCLinkConfigInfo()
{
    Anybus_HostData_t hostCMD = { 0 };

    hostCMD.Instruction = HOST_INSTRUCTION_CCLINK_CONFIG_INFO;
    memcpy(hostCMD.Data, tHostStatus.pCCLConfigInfo, sizeof(AB2S_CCLConfigInfo_t));
    memcpy(ucaHost2Slave, (uint8_t*)&hostCMD, tHostStatus.dataLen.TxRxLen);
    CRC16CCITT_AppendCalc(ucaHost2Slave, tHostStatus.dataLen.TxRxLen);
    Anybus2spi_SPITransmitReceive();
}
static void SendCMD_Config_SetProfibusConfigInfo()
{
    Anybus_HostData_t hostCMD = { 0 };

    hostCMD.Instruction = HOST_INSTRUCTION_DPV1_CONFIG_INFO;
    memcpy(hostCMD.Data, &tHostStatus.tProfiBusConfigInfo, sizeof(AB2S_ProfiBusConfigInfo_t));
    memcpy(ucaHost2Slave, (uint8_t*)&hostCMD, tHostStatus.dataLen.TxRxLen);
    CRC16CCITT_AppendCalc(ucaHost2Slave, tHostStatus.dataLen.TxRxLen);
    Anybus2spi_SPITransmitReceive();
}
static void SendCMD_Config_SetNetX90ConfigInfo()
{
    Anybus_HostData_t hostCMD = { 0 };

    hostCMD.Instruction = HOST_INSTRUCTION_NETX90_CONFIG_INFO;
    memcpy(hostCMD.Data, (uint8_t*)&tHostStatus.tNetX90ConfigInfo, sizeof(AB2S_netX90ConfigInfo_t));
    memcpy(ucaHost2Slave, (uint8_t*)&hostCMD, tHostStatus.dataLen.TxRxLen);
    CRC16CCITT_AppendCalc(ucaHost2Slave, tHostStatus.dataLen.TxRxLen);
    Anybus2spi_SPITransmitReceive();
}
static void SendCMD_Config_EnterWorkingMode()
{
    Anybus_HostData_t hostCMD = { 0 };

    hostCMD.Instruction = HOST_INSTRUCTION_ENTER_WORKING_MODE;
    memcpy(ucaHost2Slave, (uint8_t*)&hostCMD, tHostStatus.dataLen.TxRxLen);
    CRC16CCITT_AppendCalc(ucaHost2Slave, tHostStatus.dataLen.TxRxLen);
    Anybus2spi_SPITransmitReceive();
    tHostStatus.dataLen.TxRxLen = tHostStatus.dataLen.MaxDataFramePhase + INSTRUCTION_MODE_HEAD_TAIL_LEN;
}
static void SendCMD_Working_EnterConfigMode()
{
    memset(ucaHost2Slave, 0X00, tHostStatus.dataLen.TxRxLen);
    ucaHost2Slave[0] = 0x01;
    CRC16CCITT_AppendCalc(ucaHost2Slave, tHostStatus.dataLen.TxRxLen);
    Anybus2spi_SPITransmitReceive();
    tHostStatus.dataLen.TxRxLen = INSTRUCTION_MODE_FRAME_LENGTH;
}
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
static void SendCMD_Working_KeepTransmiting()
{
    static uint8_t DataRetention[WORKING_MODE_MAX_DATA_LENGTH] = { 0 };

    ucaHost2Slave[0] = 0x11u;
    ucaHost2Slave[1] = 0x00u;	/* Reserved */
    ucaHost2Slave[2] = 0x00u;	/* Reserved */

    if (LIFO_IsMessageBeenRead(&tDataSwapLIFO2Slave))
    {
        /* 若数据已经被读取，则返回上一帧数据 */
        memcpy(ucaHost2Slave + INSTRUCTION_MODE_HEAD_LEN, DataRetention, tHostStatus.dataLen.ToBusLen);
    }
    else
    {
        uint16_t realReadLen = 0;
        LIFO_ReadMessage(&tDataSwapLIFO2Slave, ucaHost2Slave + INSTRUCTION_MODE_HEAD_LEN, tHostStatus.dataLen.ToBusLen, &realReadLen);
        if (realReadLen != 0)
        {
            memcpy(DataRetention, ucaHost2Slave + INSTRUCTION_MODE_HEAD_LEN, tHostStatus.dataLen.ToBusLen);	/* 锁存本帧数据 */
        }
    }
    CRC16CCITT_AppendCalc(ucaHost2Slave, tHostStatus.dataLen.TxRxLen);
    Anybus2spi_SPITransmitReceive();
}
#if defined(IMXRT1176_USE_ITCM) && (IMXRT1176_USE_ITCM == 1)
__RAMFUNC(SRAM_ITC_cm7)
#endif
void Anybus2spi_HandleSPITxRxCpltCallback()
{
#if defined(USE_BLOKING_TRANSFER_MODE) && (USE_BLOKING_TRANSFER_MODE == 0)
    if (tHostStatus.FSMState == CRC_CHECK_STATE)
    {
        HandleCRCCheckState();
    }
#endif
}
/**
 * @brief 该函数用于在状态机启动后获取总线类型
 * @note 若正确调用Anybus2spi_HandleTIMPeriodElapsedCallback与Anybus2spi_Init函数25ms后，
 * 		 Anybus2spi_GetAnybusType的返回值仍是AnybusNone，则说明模块通信失败。
 * @return Anybus_BusType_e
 */
uint8_t Anybus2spi_GetAnybusType()
{
    /* 向从机获取总线类型(即以下State与Event的组合) */
    tHostStatus.FSMState = CONFIG_STATE;
    tHostStatus.FSMEvent = HOST_INSTRUCTION_GET_ANYBUS_TYPE;
    Bsp_Sleep(FSM_TIMEOUT_25ms);
    return tHostStatus.BusType;
}
/**
 * @brief 该函数用于在状态机启动前获取总线类型
 * @warning 当Anybus2spi_HandleTIMPeriodElapsedCallback正在被周期调用时，禁止调用本函数。
 * @return Anybus_BusType_e
 */
uint8_t Anybus2spi_GetAnybusTypeBlocking()
{
    /* 向从机获取总线类型(即以下State与Event的组合) */
    tHostStatus.FSMState = CONFIG_STATE;
    tHostStatus.FSMEvent = HOST_INSTRUCTION_GET_ANYBUS_TYPE;
    SendCMD_Config_GetAnybusType();
    Bsp_Sleep(900u);	/* 传输时间+延迟时间 约等于1ms */
    for (size_t i = 0; i < 10; i++)
    {
        SendCMD_Config_GetAnybusType();
#if defined(USE_BLOKING_TRANSFER_MODE) && (USE_BLOKING_TRANSFER_MODE == 0)
        Bsp_Sleep(200u);	/** TODO:传输时间 8M波特率时约等于100us */
#endif
        HandleCRCCheckState();
        Bsp_Sleep(800u);	/* 传输时间+延迟时间 约等于1ms */
        if (tHostStatus.BusType != AnybusNone)break;
    }

    return tHostStatus.BusType;
}
/**
 * @warning 用户传入的参数pConfigInfo必须是一个静态(or全局)变量，且是AB2S_CCLConfigInfo_t的实例而非一个指针变量。
 *          若pConfigInfo为局部变量，且在AB2S状态机运行期间释放，可能导致致命性错误。
 * @return AB2SErrorCode_e
 */
uint8_t Anybus2spi_SetCCLinkConfigInfo(AB2S_CCLConfigInfo_t* pConfigInfo)
{
    uint16_t CCLDataLen = 0;

    tHostStatus.pCCLConfigInfo = pConfigInfo;
    if (pConfigInfo->OccupiedStations == 0 || pConfigInfo->OccupiedStations > 4)
    {
        /* 1 <= OccupiedStations <= 4 */
        return AB2SPIE_PARAM;
    }
    if (pConfigInfo->ExtensionCycles != 1 && pConfigInfo->ExtensionCycles != 2 \
        && pConfigInfo->ExtensionCycles != 4 && pConfigInfo->ExtensionCycles != 8
        )
    {
        /* ExtensionCycles: 1,2,4,8 */
        return AB2SPIE_PARAM;
    }
    if (pConfigInfo->Baudrate > 4)
    {
        /* Baudrate: 0,1,2,3,4 */
        return AB2SPIE_PARAM;
    }
    if (pConfigInfo->NodeAddress > 64 || pConfigInfo->NodeAddress == 0)
    {
        /* NodeAddress: 1~64 */
        return AB2SPIE_PARAM;
    }
    if (pConfigInfo->OccupiedStations == 4 && pConfigInfo->ExtensionCycles == 8)
    {
        return AB2SPIE_PARAM;
    }
    pConfigInfo->RO_CCL_DATALEN = AB2S_GetCCLByteLen(pConfigInfo->OccupiedStations, pConfigInfo->ExtensionCycles);
    tHostStatus.FSMState = CONFIG_STATE;
    tHostStatus.FSMEvent = HOST_INSTRUCTION_CCLINK_CONFIG_INFO;
    return AB2SPIE_OK;
}
/**
 * @warning 用户传入的参数pConfigInfo必须是一个静态(or全局)变量，且是AB2S_ProfiBusConfigInfo_t的实例而非一个指针变量。
 *          若pConfigInfo为局部变量，且在AB2S状态机运行期间释放，可能导致致命性错误。
 * @return AB2SErrorCode_e
 */
uint8_t Anybus2spi_SetProfibusConfigInfo(uint8_t NodeAddress, AB2S_DataSwapLen_e eDSL)
{
    if (NodeAddress > 125)
    {
        /* NodeAddress: 0~125 */
        return AB2SPIE_PARAM;
    }
    switch (eDSL)
    {
    case AB2S_Tx64_Rx64:
        tHostStatus.tProfiBusConfigInfo.Device2PLCLen = 64;
        tHostStatus.tProfiBusConfigInfo.PLC2DeviceLen = 64;
        break;
    case AB2S_Tx128_Rx128:
        tHostStatus.tProfiBusConfigInfo.Device2PLCLen = 128;
        tHostStatus.tProfiBusConfigInfo.PLC2DeviceLen = 128;
        break;
    case AB2S_Tx220_Rx220:
        tHostStatus.tProfiBusConfigInfo.Device2PLCLen = 220;
        tHostStatus.tProfiBusConfigInfo.PLC2DeviceLen = 220;
        break;
    default:
        return AB2SPIE_PARAM;
    }
    tHostStatus.tProfiBusConfigInfo.NodeAddress = NodeAddress;
    tHostStatus.FSMState = CONFIG_STATE;
    tHostStatus.FSMEvent = HOST_INSTRUCTION_DPV1_CONFIG_INFO;
    return AB2SPIE_OK;
}
/**
 * @brief 当前netX90模块的Ethercat,Ethernet/IP,Profiniet三种协议栈使用本函数设定通信长度以及产品代码(productCode)
 * @note 当使用本函数配置anybus B40模块时，仅通信长度配置信息有效；产品线信息，产品代码无效。
 * @return AB2SErrorCode_e
 */
uint8_t Anybus2spi_SetNetX90ConfigInfo(AB2S_ProductLine_e ePL, AB2S_DataSwapLen_e eDSL)
{
    if (ePL < AB2S_PL_MIN || ePL > AB2S_PL_MAX)
    {
        return AB2SPIE_PARAM;
    }
    if (eDSL < AB2S_TxRX_MIN || eDSL > AB2S_TxRX_MAX)
    {
        return AB2SPIE_PARAM;
    }

    tHostStatus.tNetX90ConfigInfo.eProductLine = ePL;

    if (ePL == AB2S_PL_LEETX_T_COMPATIBLE_OLD)
    {
        /*
            当 eDSL 为 AB2S_PL_LEETX_T_COMPATIBLE_OLD 时
            1.从机实际不使用tHostStatus.tNetX90ConfigInfo.usProductCode作为产品代码。
            2.发送与接收通信数据长度只能为220字节。
        */
        if (eDSL != AB2S_Tx220_Rx220)
        {
            return AB2SPIE_PARAM;
        }
        tHostStatus.tNetX90ConfigInfo.eDataSwapLen = AB2S_Tx220_Rx220;
        tHostStatus.tNetX90ConfigInfo.usProductCode = 0;
    }
    else
    {
        tHostStatus.tNetX90ConfigInfo.eDataSwapLen = eDSL;
        tHostStatus.tNetX90ConfigInfo.usProductCode = (ePL << 8) | eDSL;
    }

    tHostStatus.FSMState = CONFIG_STATE;
    tHostStatus.FSMEvent = HOST_INSTRUCTION_NETX90_CONFIG_INFO;
    return AB2SPIE_OK;
}
/**
 * @brief 用于调用者与总线交换数据
 * @todo 当前需要发送接收数据长度都不为0才能交换数据，之后可能需要按需求修改
 */
void Anybus2spi_GetAndSetBusData(uint8_t* pSet, uint8_t* pGet)
{
    Anybus2spi_SetBusData(pSet);
    Anybus2spi_GetBusData(pGet);
}
void Anybus2spi_GetBusData(uint8_t* pGet)
{

    /* 若本函数调用频率高于SPI数据交互频率，数据保持变量能够防止无数据出队列可能导致的数据问题 */
    static uint8_t DataRetention[WORKING_MODE_MAX_DATA_LENGTH] = { 0 };
    if (tHostStatus.runState.ModuleLinkStatus == ANYBUS_LINK_STATUS_DISCONNECT)
    {
        memset(pGet, 0x00U, tHostStatus.dataLen.FromBusLen);
    }
    else
    {
        if (LIFO_IsMessageBeenRead(&tDataSwapLIFO2Host))
        {
            /* 若数据已经被读取，则返回上一帧数据 */
            memcpy(pGet, DataRetention, tHostStatus.dataLen.FromBusLen);
        }
        else
        {
            uint16_t realReadLen = 0;
            LIFO_ReadMessage(&tDataSwapLIFO2Host, pGet, tHostStatus.dataLen.FromBusLen, &realReadLen);
            if (realReadLen != 0)
            {
                memcpy(DataRetention, pGet, tHostStatus.dataLen.FromBusLen);	/* 锁存本帧数据 */
            }
        }
    }
}
void Anybus2spi_SetBusData(const uint8_t* pSet)
{
    LIFO_WriteMessage(&tDataSwapLIFO2Slave, pSet, tHostStatus.dataLen.ToBusLen);
}

void Anybus2spi_GetRunStateRecordParam(runStateRecord_t** pRSR)
{
    *pRSR = &tHostStatus.runState;
}
void Anybus2spi_CleanRunStateRecordParam()
{
    memset(&tHostStatus.runState, 0x00U, sizeof(runStateRecord_t));
}
AB2S_versionInfo_t* Anybus2spi_GetVersionInfo()
{
    return &tHostStatus.tVersion;
}
