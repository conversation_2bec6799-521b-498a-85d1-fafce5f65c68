#ifndef BASE_H_
#define BASE_H_

#include <stdbool.h>
#include <string.h>
#include <stdint.h>

#define	PRODUCT_LINE				0			//0:监控仪				1:Accumove			2:EcoMove

#if (PRODUCT_LINE == 0)
#define SYSTEM_MOTION_MODE			0           //0:监控仪				1:控制器	(监控仪需要把 SYSTEM_MOVE_SERIES修改成 1:AccuMove)
#define SYSTEM_MOVE_SERIES			1           //0:EcoMove;			1:AccuMove				(选择监控时一定要切换到 AccuMove)
#define SYSTEM_DRIVE_TYPE			1
#elif (PRODUCT_LINE == 1)
#define SYSTEM_MOTION_MODE			1           //0:监控仪				1:控制器	(监控仪需要把 SYSTEM_MOVE_SERIES修改成 1:AccuMove)
#define SYSTEM_MOVE_SERIES			1           //0:EcoMove;			1:AccuMove				(选择监控时一定要切换到 AccuMove)
#define SYSTEM_DRIVE_TYPE			1			//1:INOVANCE_DRIVE,		2:RAYNEN_DRIVE
#elif (PRODUCT_LINE == 2)
#define SYSTEM_MOTION_MODE			1           //0:监控仪				1:控制器	(监控仪需要把 SYSTEM_MOVE_SERIES修改成 1:AccuMove)
#define SYSTEM_MOVE_SERIES			0           //0:EcoMove;			1:AccuMove				(选择监控时一定要切换到 AccuMove)
#define SYSTEM_DRIVE_TYPE			2			//1:INOVANCE_DRIVE,		2:RAYNEN_DRIVE
#endif // (PRODUCT_LINE == 0)

#define MAX_CHARTS_NUMBER			1			//系统的曲线最大值
#define CURVE_MAX_POINTS_NUMBER		500000		//系统单条曲线的最大值

#define FORCE_FACTOR				1.100001	//压力系数(最大值)

#define MaxErrorInfoCount			20 
#define Max_Connection_Num			3

#define MAX_DynaVar_TOPIC_NUM		50

#define  GLOBALVAR_MAX_NUMBER       50

#define IO_NUMBER					16

#define	WITHOUT_SENSOR_MODE			false

#define MultiPointCalibrateNumber	20		//最大20点标定
#define FilterNumber				2		//最大对同一个参数 设置5个滤波算法

#define AxisNum						1
#define ForceMaxNumber				1		//最大接入入10个力传感器
#define PotentiometerMaxNumber		1		//最大接入10个电位计 传感器
#define ExtSensorsMaxNumber			1		//额外可接入10个传感器
#define SensorsMaxNumber			(ForceMaxNumber + PotentiometerMaxNumber + ExtSensorsMaxNumber + AxisNum*5)		//最大接入传感器的数量  电机包含（Pos、vel、Acc、current、Torque）
#define SensorsMaxEntryDesc			SensorsMaxNumber*5		//每一个参数传感器参数对应的sdo  都有  name、SensorType、Min、Max、Value

#define MicroSecondPerSecond	1000000
#define ErrorCode				uint16_t
#define ErrorCheck(errCode)		(if(errCode) return errCode;)
#define arraySize(a)			(sizeof((a)) / sizeof((a[0])))
#define DeltaTimeIn_uS(t1,t2)	(((t2) - (t1)) / 1000)
#define uS2S(time)				((time)/MicroSecondPerSecond)
#define nS2mS(time)				((time)/1000000)
#define S2uS(time)				((time)*MicroSecondPerSecond)
#define mS2uS(time)				((time)*1000)
//#define mS2nS(time)				((time)*1000000UL)			会丢失精度
#define Max(x,y)				((x)>=(y)?(x):(y))
#define Min(x,y)				((x)>=(y)?(y):(x))
//#define Abs(x)            	(((x)>= 0)?(x):(-x))
#define IsInRange(x, min, max) ((max)>=(min)?((x)>=(min) && (x)<=(max)):((x)>=(max) && (x)<=(min)))
#define Saturation(x, lowerLimit, upperLimit)   ((upperLimit)>=(lowerLimit)?(Min((upperLimit),Max((x), (lowerLimit)))):(Min((lowerLimit),Max((x), (upperLimit)))))


typedef unsigned char		byte;
typedef uint8_t             uint8;
typedef int8_t              int8;
typedef uint16_t            uint16;
typedef int16_t             int16;
typedef int32_t             int32;
typedef uint32_t            uint32;
typedef uint64_t            uint64;
typedef int64_t				int64;
typedef float               float32;
typedef double              float64;

typedef char				str16[16];
typedef char				str32[32];
typedef char				str64[64];
typedef char				str128[128];
typedef char				str255[255];

extern int32	SYS_BASE_TIME_uS;
extern double	SYS_BASE_TIME_S;

#pragma pack(1)
typedef enum
{
	Warm		= 0x00,			//警告
	Error		= 0x0F,			//错误
	HwError		= 0xF0,			//硬件故障
	MjError		= 0xFF			//严重故障 major Error
}ErrorLevel;


typedef struct 
{
	bool			bFaultInstant;		//立刻进入错误态
	ErrorLevel		eErrLever;
	ErrorCode		ErrCode;
	str255			sErrDescribe;
}ErrorInfo;


typedef union
{
	bool* pbool;			//byte length: 1
	byte* pbyte;			//byte length: 1
	char* pChar;
	uint8* puint8;
	int8* pint8;
	int16* pint16;
	uint16* puint16;
	int32* pint32;
	uint32* puint32;
	int64* pint64;
	uint64* puint64;
	float32* pfloat32;
	float64* pfloat64;
	str16* pstr16;
	str32* pstr32;
	str64* pstr64;
	str128* pstr128;
	str255* pstr255;
}PtrConvert;

typedef enum _EExecutedState {
	EExeDefeated = -1,
	EExeSucceed = 1
}EExecutedState;


typedef enum _AccessType {
	ACCESS_RO = 1,
	ACCESS_WO = 2,
	ACCESS_RW = 3,
}EAccessType;

typedef enum _ESaveFlag {
	Save_None = 0,
	Save_Factory = 1,
	Save_User = 2
}ESaveFlag;

typedef	enum {//!!! : uint8_t
	DT_None = 0,
	DT_Bool = 1,
	DT_I8 = 2,
	DT_U8 = 3,
	DT_I16 = 4,
	DT_U16 = 5,
	DT_I32 = 6,
	DT_U32 = 7,
	DT_I64 = 8,
	DT_U64 = 9,
	DT_F32 = 10,
	DT_F64 = 11,
	DT_Str = 12,
	DT_WStr = 13,
	DT_ByteArray = 14,
	DT_RemapArray = 15,
	DT_DynaValue = 16
}EDataType;


typedef struct {
	uint8	uSubIndex;
	char* Name;
	char* Alias;
	EDataType DataType;
	EAccessType ObjAccess;
	ESaveFlag SaveFlag;
	void*	pVar;
	double	Minimum;
	double	Maximum;

	char*	Desc;
	char*	ExtString;
	uint8	BitOffset;
	uint8	BitLength;
}SDOEntryDesc;

typedef struct _SDOEntryGroup {
	uint16	uIndex;
	SDOEntryDesc* pSdoEntryList;
	uint8	uSdoEntryCount;
}SDOEntryGroup;

typedef struct _ByteArray {
	uint32	uContentLength;
	byte* pContent;
}ByteArray;

typedef struct _RemapDescr {
	uint16		nMemberCounter;
	SDOEntryDesc* MemberEntry[16];
}RemapDescr;

typedef	enum _EDynaVarType {
	DynaConst = 0,
	DynaIo,
	DynaGlobal,
	DynaLocal,
	DynaMeas,
	DynaLink
}EDynaVarType;

typedef struct _DynaVar
{
	EDynaVarType		eType;
	EDataType			eDataType;
	str32				szKey;
	str64				sName;
	float32				fVarSwitched;		//将VarUnion值转换成f32用于统一计算
	float64				lfVarSwitched;		//将VarUnion值转换成f64用于统一计算
	union
	{
		bool	bVar;
		uint8	u8Var;
		int8	i8Var;
		uint16	u16Var;
		int16	i16Var;
		int32	i32Var;
		uint32	u32Var;
		uint64	u64Var;
		int64	i64Var;
		float32	fVar;
		float64	dVar;
		str255	sVar;
	}VarUnion;
}DynaVar;

typedef void (CycleFunction)();
typedef struct {
	int32       RunPeriod_uS;
	double		NextPeriod_uS;
	double		WaitTime_uS;
	double		OffsetInEveryPeriod_uS;
	CycleFunction* pFunc;
	float64		ExecTime_uS;
	float64		MaxExecTime_uS;
	float64		MeanExecTime_uS;
	float64		MinExecTime_uS;
	struct {
		float32		Sum;
		int			Num;
	}_ExecTimeAuxCalc;
	float64		Jitter_uS;
	float64		MaxJitter_uS;
	float64		MinJitter_uS;
	bool        bResetFlag;
	float32     CPULoad;
	float32     MemoryLoad;
	float32     NetworkLoad;

	volatile int64 start_tick;
	volatile int64 last_tick;
	uint64 gSysTimestamp_uS;
	uint64 gSysTimestamp_uS_Last;
}ThreadExecInfo;



#pragma pack()

#pragma pack(1)
typedef struct {
	bool bTrigSignal;

	bool bInit;
	bool bLastSignal;
	bool bTrig;
}TrigData;

typedef struct
{
	float	MaxTime;
	float	MinTime;
	float	AverageTime;
	float	CurTime;
}TimeData;
#pragma pack()

#pragma pack(1)
typedef struct _ExecuteFeedBack
{
	bool        bExeDataHadNotReset;
	bool        bExecuteEnd;
	ErrorInfo   ExeErrInfo;
}ExecuteFeedback;
#pragma pack()

#pragma pack(1)
typedef struct _ErrorPara
{
	bool		bHadNotSave;
	ErrorInfo	ErrInfo;
}ErrorPara;
#pragma pack()
#pragma pack(1)
typedef struct _ErrorInfoManagePara
{
	uint16		uErrInfoCount;
	ErrorPara   aErrArray[MaxErrorInfoCount];
}ErrorInfoManagePara;
#pragma pack()
#pragma pack(1)
typedef struct Tag2FPoint {
	float X;
	float Y;
}Tag2FPoint;
#pragma pack()
#pragma pack(1)
typedef struct Tag3FPoint {
	float T;
	float X;
	float Y;
}Tag3FPoint;
#pragma pack()

#pragma pack(1)
typedef struct
{
	str16       sKey;
	uint32      iKey;
	str255      sName;
	str255      sVar;
}SdoDataUnit;
#pragma pack()

#define SDO_MAX_WR_COUNT 200
extern SdoDataUnit aSdoData[SDO_MAX_WR_COUNT];
extern SDOEntryDesc* pEntrySdo[SDO_MAX_WR_COUNT];
extern uint16 WriteSdoCount;

extern bool gEthercatDeviceInOp;
extern bool gSysSimulation;
extern void R_Trig(TrigData* rTrig);
extern void F_Trig(TrigData* rTrig);
extern EDataType DataTypeStr2Enum(char* psDataType);
extern void ErrorInfoPack(ErrorInfo* pErrorInfo, char* pFuncName, const char* fmt, ...);
extern uint16 DATATYPE_SIZE[16];
extern const char* EDataTypeName[17];
extern ErrorInfoManagePara* psErrInfo;
extern void ErrorInfoArrayReset(ErrorInfoManagePara* psErrInfoPara);
extern int8 SysError(ErrorInfoManagePara* psErrInfoPara);

#define NONE         "/033[m"
#define RED          "/033[0;32;31m"
#define LIGHT_RED    "/033[1;31m"
#define GREEN        "/033[0;32;32m"
#define LIGHT_GREEN  "/033[1;32m"
#define BLUE         "/033[0;32;34m"
#define LIGHT_BLUE   "/033[1;34m"
#define DARY_GRAY    "/033[1;30m"
#define CYAN         "/033[0;36m"
#define LIGHT_CYAN   "/033[1;36m"
#define PURPLE       "/033[0;35m"
#define LIGHT_PURPLE "/033[1;35m"
#define BROWN        "/033[0;33m"
#define YELLOW       "/033[1;33m"
#define LIGHT_GRAY   "/033[0;37m"
#define WHITE        "/033[1;37m"

#endif /* BASE_H_ */
