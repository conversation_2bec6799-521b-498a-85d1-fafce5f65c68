#include "ChannelAndChart.h"
#include "rttimer.h"
#include "sensors/Sensor.h"
#include "base.h"
#include "SysShareMemoryDefine.h"
#include "SysVarDefine.h"
#include "PushResult.h"
#include "../sequence/SeqItemMeasurement.h"

bool bFirstPoint = true;
float32 fAppendLastPos;
float32 fAppendLastForce;

uint64 TotalCounter = 0;

float32 fInterValPos = 0;
float32 fInterValForce = 0;

uint16 iTimeIntervalVal = 0;
uint64 uSampleExecuteIndex = 0;

uint16 uPushCount = 0;

uint64 curveStart_ticks;
uint64 curve_ticks;
float32 fTime;

uint32 uPeakCount = 0;
uint32 uPeakMaxPosIndex = 0;
uint32 uPeakMaxForceIndex = 0;
uint32 uVallyPosIndex = 0;
uint32 uVallyMaxForceIndex = 0;

bool bLastStopPoint;
bool bUnormalAppend;

float32 fMaxPos, fMaxForce, fMaxExtPos;
/** AppendChannelDataReset
* @param[in]     None
* @param[out]    None
* @return        None
*/
void AppendChannelDataReset()
{
	uPeakCount = 0;
	uPeakMaxPosIndex = 0;
	uPeakMaxForceIndex = 0;
	uVallyPosIndex = 0;
	uVallyMaxForceIndex = 0;
	//dbgPrint(1, 0, "\n\nAppendChannelDataReset\n");
	uPushCount = 0;
	bFirstPoint = true;		//
	bLastStopPoint = false;
	bUnormalAppend = false;
	uSampleExecuteIndex = 0;
	curveStart_ticks = GetSysTick();
	psChartsData->uPointsCount = 0;
	psChartsData->uHadPushPoints = 0;
	bPasuedStopCmd = false;
	// 重置  fAppendLastPos，确保  （AxisMc[0].Axis.LogicFbk.fPos - fAppendLastPos） > 1mm\0.1mm\0.01mm
	psChartsData->bChartEvalFinish = false;
	psChartsData->bChartEvalOK = false;
	psChartsData->bCollecFinish = false;
	psChartsData->bStartEval = false;

	fInterValPos = 0;
	fInterValForce = 0;
	TotalCounter = 0;

	fMaxPos = pSysShareData->sSysFbkVar.fPosFbk;
	fMaxForce = pSysShareData->sSysFbkVar.fSenosrVarFbk;
	fMaxExtPos = pSysShareData->sSysFbkVar.fExtPosFbk;


	float32 fBaseTimeCoe = 1;
	if (SYS_BASE_TIME_uS != 0)
		fBaseTimeCoe = (1000.0 / (float32)SYS_BASE_TIME_uS);

	switch (psChartsData->eSampleType)
	{
	case Interval_Time_4000:
	case Interval_Time_2000:
		iTimeIntervalVal = 1;
		break;
	case Interval_Time_1000:
		iTimeIntervalVal = 2 * fBaseTimeCoe;		// 1000 / SYS_BASE_TIME_uS;     8取4
		break;
	case Interval_Time_0500:					//间隔周期执行							  16取4
		iTimeIntervalVal = 4 * fBaseTimeCoe;		// 1000 / SYS_BASE_TIME_uS;
		break;
	case Interval_Time_0100:
		iTimeIntervalVal = 20 * fBaseTimeCoe;		//500us
		break;
	case Interval_Time_0050:
		iTimeIntervalVal = 40 * fBaseTimeCoe;
		break;
	case Interval_Time_0010:
		iTimeIntervalVal = 80 * fBaseTimeCoe;
		break;
	case	Interval_Pos_1000um://		//1mm   间隔采样
		fInterValPos = 1;
		break;
	case	Interval_Pos_0100um://		//0.1mm 间隔采样	
		fInterValPos = 0.1;
		break;
	case	Interval_Pos_0010um://		//0.01mm 间隔采样	
		fInterValPos = 0.01;
		break;
	case	Interval_Force_1000N://		//满量程的1kn
		fInterValForce = 1;
		break;
	case	Interval_Force_0100N://		//满量程的0.1kn		
		fInterValForce = 0.1;
		break;
	case	Interval_Force_0010N://		//满量程的0.01kn	
		fInterValForce = 0.01;
		break;
	default:

		break;
	}

	//dbgPrint(1, 0, "psChartsData->eSampleType Type%d  iTimeIntervalVal:%d\n", psChartsData->eSampleType, iTimeIntervalVal);
}


/** FindChannelPeakVally
* @param[in]    bool* pbFirst
* @param[out]   SSensorsPara* pChannelData
* @return       None
*/
void FindChannelPeakVally(SSensorsPara* pChannelData, float32* pPos, float32* pForce, float32* fExtPos)
{
	if (bFirstPoint)
	{
		pChannelData->sSensorUnit[0].sRunSeqPeakValleyData.fPeakVal = *pPos;// *pChannelData->sSensorUnit[i].pfSensorVar;
		pChannelData->sSensorUnit[0].sRunSeqPeakValleyData.fValleyVal = *pPos;
		pChannelData->sSensorUnit[5].sRunSeqPeakValleyData.fPeakVal = *pForce;// *pChannelData->sSensorUnit[i].pfSensorVar;
		pChannelData->sSensorUnit[5].sRunSeqPeakValleyData.fValleyVal = *pForce;
		pChannelData->sSensorUnit[6].sRunSeqPeakValleyData.fPeakVal = *fExtPos;// *pChannelData->sSensorUnit[i].pfSensorVar;
		pChannelData->sSensorUnit[6].sRunSeqPeakValleyData.fValleyVal = *fExtPos;
		//for (uint8 i = 0; i < pChannelData->SenActivedCount; i++)
		//{
		//	if(strcmp(pChannelData->sSensorUnit[i].spName,"Position") == 0)
		//		dbgPrint(1, 0, "Position Index:%d\n", i);

		//	if (strcmp(pChannelData->sSensorUnit[i].spName, "Force") == 0)
		//		dbgPrint(1, 0, "Force Index:%d\n", i);

		//	if (strcmp(pChannelData->sSensorUnit[i].spName, "ExtShift") == 0)
		//		dbgPrint(1, 0, "ExtShift Index:%d\n", i);
		//}
	}
	else
	{
		if (*pPos > pChannelData->sSensorUnit[0].sRunSeqPeakValleyData.fPeakVal)
			pChannelData->sSensorUnit[0].sRunSeqPeakValleyData.fPeakVal = *pPos;

		if (*pPos < pChannelData->sSensorUnit[0].sRunSeqPeakValleyData.fValleyVal)
			pChannelData->sSensorUnit[0].sRunSeqPeakValleyData.fValleyVal = *pPos;

		if (*pForce > pChannelData->sSensorUnit[5].sRunSeqPeakValleyData.fPeakVal)
			pChannelData->sSensorUnit[5].sRunSeqPeakValleyData.fPeakVal = *pForce;

		if (*pForce < pChannelData->sSensorUnit[5].sRunSeqPeakValleyData.fValleyVal)
			pChannelData->sSensorUnit[5].sRunSeqPeakValleyData.fValleyVal = *pForce;

		if (*fExtPos > pChannelData->sSensorUnit[6].sRunSeqPeakValleyData.fPeakVal)
			pChannelData->sSensorUnit[6].sRunSeqPeakValleyData.fPeakVal = *fExtPos;

		if (*fExtPos < pChannelData->sSensorUnit[6].sRunSeqPeakValleyData.fValleyVal)
			pChannelData->sSensorUnit[6].sRunSeqPeakValleyData.fValleyVal = *fExtPos;
	}
	uPeakCount++;
}

/** TimePeakVally
* @param[in]    bool* pbBuffCalcFinish
* @param[out]   None
* @return       None
*/
Tag3FPoint aPointsBuff[400];
Tag3FPoint aPeakVallyPoints[4];				//最终按照时间来排序
Tag3FPoint tempPoint;
uint16  tmpVarCount = 0;
uint16 uFinishCount = 0;
void TimePeakVally(bool* pbBuffCalcFinish, bool* pbLastPoint, bool* pbUnMormalAppend, uint16* pPeakVallyCount, float32* pPos, float32* pForce, float32* fExtPos)
{
	*pbBuffCalcFinish = false;
	aPointsBuff[(uSampleExecuteIndex) % iTimeIntervalVal].T = fTime;// (float32)(uSampleExecuteIndex * SYS_BASE_TIME_S);

	if (strcmp((const char*)psChartsData->sChart[0].sChartSetInfo.sXChannelName, "Position") == 0)
		aPointsBuff[(uSampleExecuteIndex) % iTimeIntervalVal].X = *pPos;
	else  if (strcmp((const char*)psChartsData->sChart[0].sChartSetInfo.sXChannelName, "Force") == 0)
		aPointsBuff[(uSampleExecuteIndex) % iTimeIntervalVal].X = *pForce;
	else if (strcmp((const char*)psChartsData->sChart[0].sChartSetInfo.sXChannelName, "ExtShift") == 0)
		aPointsBuff[(uSampleExecuteIndex) % iTimeIntervalVal].X = *fExtPos;

	if (strcmp((const char*)psChartsData->sChart[0].sChartSetInfo.sYChannelName, "Position") == 0)
		aPointsBuff[(uSampleExecuteIndex) % iTimeIntervalVal].Y = *pPos;
	else if (strcmp((const char*)psChartsData->sChart[0].sChartSetInfo.sYChannelName, "Force") == 0)
		aPointsBuff[(uSampleExecuteIndex) % iTimeIntervalVal].Y = *pForce;
	else if (strcmp((const char*)psChartsData->sChart[0].sChartSetInfo.sYChannelName, "ExtShift") == 0)
		aPointsBuff[(uSampleExecuteIndex) % iTimeIntervalVal].Y = *fExtPos;


	if (uSampleExecuteIndex == 0 ||		//添加曲线第一个点（时间戳为0）
		((uSampleExecuteIndex % iTimeIntervalVal) == iTimeIntervalVal - 1) || *pbLastPoint || *pbUnMormalAppend)
	{
		//这里buff数据满了 开始计算 time peak vally值
		memset(&aPeakVallyPoints[0], 0, 4 * sizeof(Tag3FPoint));
		if ((uSampleExecuteIndex % iTimeIntervalVal) >= 3)
		{
			for (uint16 uPointsIndex = 0; uPointsIndex <= (uSampleExecuteIndex % iTimeIntervalVal); uPointsIndex++)
			{
				if (uPointsIndex == 0)
				{
					for (uint8 iIndexResult = 0; iIndexResult < 4; iIndexResult++)
					{
						aPeakVallyPoints[iIndexResult].T = aPointsBuff[0].T;
						aPeakVallyPoints[iIndexResult].X = aPointsBuff[0].X;
						aPeakVallyPoints[iIndexResult].Y = aPointsBuff[0].Y;
					}
				}
				else
				{
					if (aPointsBuff[uPointsIndex].X < aPeakVallyPoints[0].X)//X min
					{
						aPeakVallyPoints[0].T = aPointsBuff[uPointsIndex].T;
						aPeakVallyPoints[0].X = aPointsBuff[uPointsIndex].X;
						aPeakVallyPoints[0].Y = aPointsBuff[uPointsIndex].Y;
					}

					if (aPointsBuff[uPointsIndex].X > aPeakVallyPoints[1].X)//X max
					{
						aPeakVallyPoints[1].T = aPointsBuff[uPointsIndex].T;
						aPeakVallyPoints[1].X = aPointsBuff[uPointsIndex].X;
						aPeakVallyPoints[1].Y = aPointsBuff[uPointsIndex].Y;
					}

					if (aPointsBuff[uPointsIndex].Y < aPeakVallyPoints[2].Y)//Y min
					{
						aPeakVallyPoints[2].T = aPointsBuff[uPointsIndex].T;
						aPeakVallyPoints[2].X = aPointsBuff[uPointsIndex].X;
						aPeakVallyPoints[2].Y = aPointsBuff[uPointsIndex].Y;
					}

					if (aPointsBuff[uPointsIndex].Y > aPeakVallyPoints[3].Y)//Y max
					{
						aPeakVallyPoints[3].T = aPointsBuff[uPointsIndex].T;
						aPeakVallyPoints[3].X = aPointsBuff[uPointsIndex].X;
						aPeakVallyPoints[3].Y = aPointsBuff[uPointsIndex].Y;
					}
				}
			}

			// 按照时间进行排序
			for (uint8 iIndexResult = 0; iIndexResult < 3; iIndexResult++)
			{
				for (uint8 m = 0; m < 3 - iIndexResult; m++)
				{
					if (aPeakVallyPoints[m].T > aPeakVallyPoints[m + 1].T)
					{
						tempPoint.T = aPeakVallyPoints[m].T;
						tempPoint.X = aPeakVallyPoints[m].X;
						tempPoint.Y = aPeakVallyPoints[m].Y;

						aPeakVallyPoints[m].T = aPeakVallyPoints[m + 1].T;
						aPeakVallyPoints[m].X = aPeakVallyPoints[m + 1].X;
						aPeakVallyPoints[m].Y = aPeakVallyPoints[m + 1].Y;

						aPeakVallyPoints[m + 1].T = tempPoint.T;
						aPeakVallyPoints[m + 1].X = tempPoint.X;
						aPeakVallyPoints[m + 1].Y = tempPoint.Y;
					}
				}
			}

			tmpVarCount = 4;
			//去掉相同的T的
			for (uint8 iIndexResult = 0; iIndexResult <= 2; iIndexResult++)
			{
				if (aPeakVallyPoints[3 - iIndexResult].T == aPeakVallyPoints[2 - iIndexResult].T)
				{
					tmpVarCount--;
					if (iIndexResult > 0)
						memcpy(&aPeakVallyPoints[2 - iIndexResult], &aPeakVallyPoints[3 - iIndexResult], (iIndexResult + 1) * sizeof(Tag3FPoint));
				}
			}
			*pbBuffCalcFinish = true;
			*pPeakVallyCount = tmpVarCount;
		}
		else
		{
			memset(&aPeakVallyPoints[0], 0, 4 * sizeof(Tag3FPoint));
			tmpVarCount = (uSampleExecuteIndex % iTimeIntervalVal) + 1;
			memcpy(&aPeakVallyPoints[0], &aPointsBuff[0], (tmpVarCount) * sizeof(Tag3FPoint));
			*pbBuffCalcFinish = true;
			*pPeakVallyCount = tmpVarCount;
		}

		//if (*pPeakVallyCount > 0 && *pbLastPoint)
		//{
		//	dbgPrint(1, 0, " *pbLastPoint:%s  uSampleExecuteIndex:%d  iTimeIntervalVal:%d  (uSampleExecuteIndex ModiTimeIntervalVal):%d\n",
		//		*pbLastPoint ? "true" : "false",
		//		uSampleExecuteIndex,
		//		iTimeIntervalVal,
		//		(uSampleExecuteIndex% iTimeIntervalVal));

		//if (*pbLastPoint)
		//	dbgPrint(1, 0, "\n***** bCollecFinish:%d  *pPeakVallyCount:%d\n", (uSampleExecuteIndex % iTimeIntervalVal), *pPeakVallyCount);

		//if ((*pbLastPoint || *pbUnMormalAppend) && *pPeakVallyCount >= 0)
		//{
		//	for (uint8 iIndexResult = 0; iIndexResult < *pPeakVallyCount; iIndexResult++)
		//	{
		//		dbgPrint(1, 0, "***** bCollecFinish:%s  uPointsCount:%d  aPeakVallyPoints  Count:%d  [%f,%f,%f]\n",
		//			psChartsData->bCollecFinish ? "true" : "false",
		//			psChartsData->uPointsCount,
		//			iIndexResult,
		//			aPeakVallyPoints[iIndexResult].T,
		//			aPeakVallyPoints[iIndexResult].X,
		//			aPeakVallyPoints[iIndexResult].Y);
		//	}
		//}
	}
}

/** AppedPointsAccordSensorType
* @param[in]    bool bEnable
* @param[out]   None
* @return       None
*/
void AppedPointsAccordSensorType(uint8 IndexChart)
{
	if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sXChannelName, "Position") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].X = pSysShareData->sSysFbkVar.fPosFbk;
	else  if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sXChannelName, "Force") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].X = pSysShareData->sSysFbkVar.fSenosrVarFbk;
	else if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sXChannelName, "ExtShift") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].X = pSysShareData->sSysFbkVar.fExtPosFbk;

	if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sYChannelName, "Position") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].Y = pSysShareData->sSysFbkVar.fPosFbk;
	else if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sYChannelName, "Force") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].Y = pSysShareData->sSysFbkVar.fSenosrVarFbk;
	else if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sYChannelName, "ExtShift") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].Y = pSysShareData->sSysFbkVar.fExtPosFbk;
}

/** AppedPointsAccordSensorType
* @param[in]    bool bEnable
* @param[out]   None
* @return       None
*/
void AppedPeakPointAccordSensorType(uint8 IndexChart)
{
	if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sXChannelName, "Position") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].X = fMaxPos;
	else  if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sXChannelName, "Force") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].X = fMaxForce;
	else if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sXChannelName, "ExtShift") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].X = fMaxExtPos;

	if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sYChannelName, "Position") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].Y = fMaxPos;
	else if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sYChannelName, "Force") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].Y = fMaxForce;
	else if (strcmp((const char*)psChartsData->sChart[IndexChart].sChartSetInfo.sYChannelName, "ExtShift") == 0)
		psChartsData->sChart[IndexChart].Points[psChartsData->uPointsCount].Y = fMaxExtPos;
}
void UpdatePeakPointMax(float32* pPos, float32* pForce, float32* fExtPos)
{
	if (*pPos > fMaxPos)
		fMaxPos = pSysShareData->sSysFbkVar.fPosFbk;

	if (*pForce > fMaxForce)
		fMaxForce = pSysShareData->sSysFbkVar.fSenosrVarFbk;

	if (*fExtPos > fMaxExtPos)
		fMaxExtPos = pSysShareData->sSysFbkVar.fExtPosFbk;
}

/** AppendChannelDataToBuffer
* 采样后更新折返点极值和索引值
* @param[in]    PeakValue：采样后的点
* @param[in]    uPointsCount：采样后点的索引
* @param[in]    pReturnPoint：折返点数据
* @param[out]   None
* @return       None
*/
void UpdateReturnValueAndIndex(float PeakValue, uint32 uPointsCount, ReturnPoint* pReturnPoint)
{
	float ReturnValue;
	switch (pReturnPoint->ReturnType)
	{
	case Xmin:
	{
		if (PeakValue == pReturnPoint->ReturnValue->fXmin)		//滞后情况
		{
			pReturnPoint->ReturnIndex = uPointsCount;
		}
		else if (PeakValue < pReturnPoint->ReturnValue->fXmin)	//非滞后情况 更新折返点索引和值
		{
			pReturnPoint->ReturnIndex = uPointsCount;
			pReturnPoint->ReturnValue->fXmin = PeakValue;
		}
		break;
	}
	case Xmax:
	{
		if (PeakValue == pReturnPoint->ReturnValue->fXmax)		//滞后情况
		{
			pReturnPoint->ReturnIndex = uPointsCount;
		}
		else if (PeakValue > pReturnPoint->ReturnValue->fXmax)	//非滞后情况 更新折返点索引和值
		{
			pReturnPoint->ReturnIndex = uPointsCount;
			pReturnPoint->ReturnValue->fXmax = PeakValue;
		}
		break;
	}
	case Ymin:
	{
		if (PeakValue == pReturnPoint->ReturnValue->fYmin)      //滞后情况
		{
			pReturnPoint->ReturnIndex = uPointsCount;
		}
		else if (PeakValue < pReturnPoint->ReturnValue->fYmin)	//非滞后情况 更新折返点索引和值
		{
			pReturnPoint->ReturnIndex = uPointsCount;
			pReturnPoint->ReturnValue->fYmin = PeakValue;
		}
		break;
	}
	case Ymax:
	{
		if (PeakValue == pReturnPoint->ReturnValue->fYmax)      //滞后情况
		{
			pReturnPoint->ReturnIndex = uPointsCount;
		}
		else if (PeakValue > pReturnPoint->ReturnValue->fYmax)	//非滞后情况 更新折返点索引和值
		{
			pReturnPoint->ReturnIndex = uPointsCount;
			pReturnPoint->ReturnValue->fYmax = PeakValue;
		}
		break;
	}
	}
}

/** AppendChannelDataToBuffer
* @param[in]    bool bEnable
* @param[out]   None
* @return       None
*/
bool bTimePeakBuffCalcFinish;
TrigData fEnableAppendTrig;
uint16 uPeakVallyCalcCount = 0;

ErrorInfo appendBuffErrInfo;
float32 fAppendPos, fAppendForce, fAppendExtPos;
uint16 uPushWaitCounter = 0;
float cReturnValue = 0;
ErrorInfo AppendChannelDataToBuffer(bool* pbEnable, bool* pbUnMormalRecode, bool* pSeqLastPoint)
{
	appendBuffErrInfo.ErrCode = 0;
	//if (*pSeqLastPoint || *pbUnMormalRecode)
	//dbgPrint(1, 0, "AppendChannelDataToBuffer  bEnable:%s bUnMormalRecode:%s bLastPoint:%s \n",
	//				* pbEnable ? "true" : "false",
	//				*pbUnMormalRecode ? "true" : "false",
	//				*pSeqLastPoint ?"true" : "false");

	if (*pbEnable || *pSeqLastPoint || *pbUnMormalRecode)
	{
		if (uSampleExecuteIndex == 0)
		{
			curveStart_ticks = GetSysTick();
			curve_ticks = curveStart_ticks;
			fTime = 0;
		}
		else
		{
			curve_ticks = GetSysTick();
			fTime = (float32)((float32)(curve_ticks - curveStart_ticks) / 1000000000.0);		//纳秒转换成秒
		}

		fAppendPos = pSysShareData->sSysFbkVar.fPosFbk;
		fAppendForce = pSysShareData->sSysFbkVar.fSenosrVarFbk;
		fAppendExtPos = pSysShareData->sSysFbkVar.fExtPosFbk;

		FindChannelPeakVally(&pSysShareData->sSysAvaliablePara, &fAppendPos, &fAppendForce, &fAppendExtPos);			//一定要放在这里 放到下面会出现假如采集超限了 捕捉不到传感器过载或更大峰值
		if (psChartsData->uPointsCount <= CURVE_MAX_POINTS_NUMBER)
		{
			bool bSamplePrint = false;
			for (uint8 i = 0; i < Min(psChartsData->iChartCount, 1); i++)	//MAX_CHARTS_NUMBER
			{
				bSamplePrint = false;
				switch (psChartsData->eSampleType)
				{
				case Interval_Time_4000:			//超采样处理
				case Interval_Time_2000:
					//未作 时间段峰谷 处理  单纯间隔时间采样
					psChartsData->sChart[i].Points[psChartsData->uPointsCount].T = fTime;
					AppedPointsAccordSensorType(i);
					//printf("\npReturnPoint.ReturnValue: %f\n", pReturnPoint.ReturnValue->fXmax);
					//printf("psChartsData->uPointsCount: %d\n", psChartsData->uPointsCount);
					//printf("pReturnPoint.ReturnIndex: %d\n", pReturnPoint.ReturnIndex);
					//printf("psChartsData->sChart[i].Points[pReturnPoint.ReturnIndex].X: %f\n", psChartsData->sChart[0].Points[pReturnPoint.ReturnIndex].X);
					psChartsData->uPointsCount++;
					//dbgPrint(1, 0, "uPointsCount:%d fTime:%f\n", psChartsData->uPointsCount,fTime);
					break;
				case Interval_Time_1000:
				case Interval_Time_0500:	//间隔周期执行
				case Interval_Time_0100:
				case Interval_Time_0050:
				case Interval_Time_0010:
				{
					TimePeakVally(&bTimePeakBuffCalcFinish, pSeqLastPoint, pbUnMormalRecode, &uPeakVallyCalcCount, &fAppendPos, &fAppendForce, &fAppendExtPos);
					if (bTimePeakBuffCalcFinish)
					{
						memcpy(&psChartsData->sChart[i].Points[psChartsData->uPointsCount], &aPeakVallyPoints[0], uPeakVallyCalcCount * sizeof(Tag3FPoint));
						//if (pReturnPoint.ReturnState == 1)	//折返模式处理
						//{
						//	//cReturnValue = pReturnPoint.ReturnValue;
						//	switch (pReturnPoint.ReturnType)
						//	{
						//	case Xmin:
						//	case Xmax:
						//		for (uint8 j = 0; j < uPeakVallyCalcCount; j++)
						//		{
						//			UpdateReturnValueAndIndex(psChartsData->sChart[i].Points[psChartsData->uPointsCount + j].X, psChartsData->uPointsCount + j, &pReturnPoint);
						//		}
						//		break;
						//	case Ymin:
						//	case Ymax:
						//		for (uint8 j = 0; j < uPeakVallyCalcCount; j++)
						//		{
						//			UpdateReturnValueAndIndex(psChartsData->sChart[i].Points[psChartsData->uPointsCount + j].Y, psChartsData->uPointsCount + j, &pReturnPoint);
						//		}
						//		break;
						//	}
						//	printf("\npReturnPoint.ReturnValue: %f\n", pReturnPoint.ReturnValue->fXmax);
						//	printf("psChartsData->uPointsCount: %d\n", psChartsData->uPointsCount);
						//	printf("pReturnPoint.ReturnIndex: %d\n", pReturnPoint.ReturnIndex);
						//	printf("psChartsData->sChart[i].Points[pReturnPoint.ReturnIndex].X: %f\n", psChartsData->sChart[0].Points[pReturnPoint.ReturnIndex].X);
						//	printf("uPeakVallyCalcCount: %d\n", uPeakVallyCalcCount);
						//	printf("*pSeqLastPoint: %d\n", *pSeqLastPoint);
						//}

						//for (uint8 j = 0; j < uPeakVallyCalcCount; j++)
						//{
						//	dbgPrint(1, 0, "[%d] t:%f x:%f y:%f\n",
						//		psChartsData->uPointsCount + j,
						//		psChartsData->sChart[i].Points[psChartsData->uPointsCount + j].T,
						//		psChartsData->sChart[i].Points[psChartsData->uPointsCount + j].X,
						//		psChartsData->sChart[i].Points[psChartsData->uPointsCount + j].Y);
						//}

						psChartsData->uPointsCount += uPeakVallyCalcCount;
						//dbgPrint(1, 0, "uPointsCount:%d uPeakVallyCalcCount:%d\n", psChartsData->uPointsCount, uPeakVallyCalcCount);
					}
				}
				break;
				case Interval_Pos_1000um:		//1mm位置等间隔采样
				case Interval_Pos_0100um:		//0.1mm位置等间隔采样
				case Interval_Pos_0010um:		//0.01mm位置等间隔采样
					if (SYSTEM_MOTION_MODE)
					{
						UpdatePeakPointMax(&fAppendPos, &fAppendForce, &fAppendExtPos);
						if (bFirstPoint || *pSeqLastPoint || (abs(pSysShareData->sSysFbkVar.fPosFbk - fAppendLastPos) >= fInterValPos))
						{
							psChartsData->sChart[i].Points[psChartsData->uPointsCount].T = fTime;// (float32)(uSampleExecuteIndex * SYS_BASE_TIME_S);
							fAppendLastPos = pSysShareData->sSysFbkVar.fPosFbk;
							AppedPeakPointAccordSensorType(i);//AppedPointsAccordSensorType(i);
							bSamplePrint = true;
							psChartsData->uPointsCount++;
						}
					}
					else
					{
						UpdatePeakPointMax(&fAppendPos, &fAppendForce, &fAppendExtPos);
						if (bFirstPoint || *pSeqLastPoint || (abs(pSysShareData->sSysFbkVar.fExtPosFbk - fAppendLastPos) >= fInterValPos))
						{
							psChartsData->sChart[i].Points[psChartsData->uPointsCount].T = fTime;// (float32)(uSampleExecuteIndex * SYS_BASE_TIME_S);
							fAppendLastPos = pSysShareData->sSysFbkVar.fExtPosFbk;
							AppedPeakPointAccordSensorType(i);//AppedPointsAccordSensorType(i);
							fMaxPos = 0;
							fMaxForce = 0;
							fMaxExtPos = 0;
							//if (pReturnPoint.ReturnState == 1)
							//{
							//	switch (pReturnPoint.ReturnType)
							//	{
							//		//case Xmin:
							//	case Xmax:
							//		UpdateReturnValueAndIndex(psChartsData->sChart[i].Points[psChartsData->uPointsCount].X, psChartsData->uPointsCount, &pReturnPoint);
							//		break;
							//		//case Ymin:
							//	case Ymax:
							//		UpdateReturnValueAndIndex(psChartsData->sChart[i].Points[psChartsData->uPointsCount].Y, psChartsData->uPointsCount, &pReturnPoint);
							//		break;
							//	}
							//	printf("\npReturnPoint.ReturnValue: %f\n", pReturnPoint.ReturnValue->fXmax);
							//	printf("psChartsData->uPointsCount: %d\n", psChartsData->uPointsCount);
							//	printf("pReturnPoint.ReturnIndex: %d\n", pReturnPoint.ReturnIndex);
							//	printf("psChartsData->sChart[i].Points[pReturnPoint.ReturnIndex].X: %f\n", psChartsData->sChart[0].Points[pReturnPoint.ReturnIndex].X);
							//	printf("uPeakVallyCalcCount: %d\n", uPeakVallyCalcCount);
							//	printf("*pSeqLastPoint: %d\n", *pSeqLastPoint);
							//}

							bSamplePrint = true;
							psChartsData->uPointsCount++;
						}
					}
					break;
				case Interval_Force_1000N:
				case Interval_Force_0100N:
				case Interval_Force_0010N:
					UpdatePeakPointMax(&fAppendPos, &fAppendForce, &fAppendExtPos);
					if (bFirstPoint || *pSeqLastPoint || (abs(pSysShareData->sSysFbkVar.fSenosrVarFbk - fAppendLastForce) >= fInterValForce))
					{
						psChartsData->sChart[i].Points[psChartsData->uPointsCount].T = fTime;// (float32)(uSampleExecuteIndex * SYS_BASE_TIME_S);
						AppedPeakPointAccordSensorType(i);//AppedPointsAccordSensorType(i);

						//if (pReturnPoint.ReturnState == 1)
						//{
						//	switch (pReturnPoint.ReturnType)
						//	{
						//		//case Xmin:
						//	case Xmax:
						//		UpdateReturnValueAndIndex(psChartsData->sChart[i].Points[psChartsData->uPointsCount].X, psChartsData->uPointsCount, &pReturnPoint);
						//		break;
						//		//case Ymin:
						//	case Ymax:
						//		UpdateReturnValueAndIndex(psChartsData->sChart[i].Points[psChartsData->uPointsCount].Y, psChartsData->uPointsCount, &pReturnPoint);
						//		break;
						//	}
						//	printf("\npReturnPoint.ReturnValue: %f\n", pReturnPoint.ReturnValue->fXmax);
						//	printf("psChartsData->uPointsCount: %d\n", psChartsData->uPointsCount);
						//	printf("pReturnPoint.ReturnIndex: %d\n", pReturnPoint.ReturnIndex);
						//	printf("psChartsData->sChart[i].Points[pReturnPoint.ReturnIndex].X: %f\n", psChartsData->sChart[0].Points[pReturnPoint.ReturnIndex].X);
						//	printf("uPeakVallyCalcCount: %d\n", uPeakVallyCalcCount);
						//	printf("*pSeqLastPoint: %d\n", *pSeqLastPoint);
						//}
						fMaxPos = 0;
						fMaxForce = 0;
						fMaxExtPos = 0;
						fAppendLastForce = pSysShareData->sSysFbkVar.fSenosrVarFbk;
						bSamplePrint = true;
						psChartsData->uPointsCount++;
					}
					break;
				default:
				{
					appendBuffErrInfo.ErrCode = 5611;
					appendBuffErrInfo.eErrLever = Error;
					dbgPrint(1, 0, "AppendChannelDataToBuffer  eSampleType Error \n");
				}
				break;
				}
				//if (*pSeqLastPoint && (pReturnPoint.ReturnState == 1))	//折返模式处理
				//{
				//	pReturnPoint.ReturnStop = true;
				//	pSMeasure_Returntext.fXmax = pReturnPoint.ReturnValue->fXmax;
				//	pSMeasure_Returntext.fXmin = pReturnPoint.ReturnValue->fXmin;
				//	pSMeasure_Returntext.fYmax = pReturnPoint.ReturnValue->fYmax;
				//	pSMeasure_Returntext.fYmin = pReturnPoint.ReturnValue->fYmin;
				//}
			}
		}
		else
		{
			//dbgPrint(1, 0, "uPointsCount > %d\n", CURVE_MAX_POINTS_NUMBER);
			if (!*pSeqLastPoint && !*pbUnMormalRecode)
			{
				appendBuffErrInfo.ErrCode = 5610;
				appendBuffErrInfo.eErrLever = Error;
			}
		}

		uSampleExecuteIndex++;
		bFirstPoint = false;
	}

	////如果和上位机连接
	//if (psChartsData->uPointsCount > 0 &&
	//		psChartsData->uHadPushPoints < psChartsData->uPointsCount)
	//{
	//	if ((pSysShareData->gSysCounter % 150 == 0) || ((psChartsData->uPointsCount - psChartsData->uHadPushPoints) * (psChartsData->iChartCount * 2 + 1) * 4 > 1000)
	//		|| *pSeqLastPoint || *pbUnMormalRecode)
	//	{
	//		bPushData = true;
	//		dbgPrint(1, 0, "uPushWaitCounter = 0\n");//
	//	}

	//	if (bPushData)
	//	{
	//		if (*pSysShareData->pbConnected)
	//		{
	//			if (pSysShareData->PushChartstLen != 0)
	//				uPushWaitCounter++;
	//			else
	//			{
	//				if(uPushWaitCounter > 0)
	//					dbgPrint(1, 0, "pSysShareData->PushChartstLen != 0 \n\n\n");//

	//				PushChartFrame();
	//				bPushData = false;
	//			}
	//		}
	//		else
	//		{
	//			bPushData = false;
	//			//if (pSysShareData->gSysCounter % 100 == 0)
	//			//	dbgPrint(1, 0, "PushChartFrame Error! UnConnected!!\n");
	//		}
	//	}
	//}

	ErrorInfoPack(&appendBuffErrInfo, "AppendChannelDataToBuffer", "");
	return appendBuffErrInfo;
}

/** AppendChannelData		//sequence measurement start 时开始存储数据点
*
* @param[in]     None
* @param[out]    None
* @return		 void
*/
TrigData	rPausedTrig;
TrigData	rStopedTrig;
bool bPasuedStopCmd;
void AppendRunSeqData()
{
	if (pSysShareData->sExSW.bSequenceBusy)
	{
		if (Seq.bMeasurementStart)
		{
			rPausedTrig.bTrigSignal = pSysShareData->sExSW.bSequencePaused;
			R_Trig(&rPausedTrig);

			rStopedTrig.bTrigSignal = Seq.bSequenceStoped;//Seq.bStopRequest;
			R_Trig(&rStopedTrig);
		}

		fEnableAppendTrig.bTrigSignal = Seq.bMeasurementStart;
		F_Trig(&fEnableAppendTrig);

		if ((rStopedTrig.bTrig && !bPasuedStopCmd) || fEnableAppendTrig.bTrig)
		{
			bLastStopPoint = true;
		}
		else
			bLastStopPoint = false;

		if ((rPausedTrig.bTrig) && pSysShareData->SeqAux.iActiveRowIndex != 0)	//要排除掉HOME的暂停
		{
			bUnormalAppend = true;
		}
		else
			bUnormalAppend = false;

		if ((pSysShareData->sExSW.eSysState == Sys_State_RunSequence && !pSysShareData->sExSW.bSequencePaused && !Seq.bSequenceStoped && !bPasuedStopCmd) ||
			bUnormalAppend ||
			(bLastStopPoint && !bPasuedStopCmd))
		{
			//printf("bLastStopPoint:%s  bUnormalAppend:%s bPasuedStopCmd:%s  bSequencePaused:%s\n", 
			//	bLastStopPoint ? "true " : "false", 
			//	bUnormalAppend ? "true " : "false",
			//	bPasuedStopCmd ? "true " : "false",
			//	pSysShareData->sExSW.bSequencePaused ? "true " : "false");
			AppendChannelDataToBuffer(&Seq.bMeasurementStart, &bUnormalAppend, &bLastStopPoint);
		}
	}
}
