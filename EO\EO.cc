#include "EO.h"
#include "base.h"
#include "math.h"
#include "syslog.h"
#define max(a,b) ((a) > (b) ? (a) : (b))
#define min(a,b) ((a) < (b) ? (a) : (b))

//#define MaxPointCounts		1000//500000
//#define Max_PointCounts		1000//500000
#define Max_FieldBusNameCount 22

#define DefaultValue 0x0000C0FF

#define Max_Polygon_PointsCount 10  //10边型
#define Max_EO_Count 10
#define FileName "PolygonTrying.q2"
#define csvFile "Test1.csv"

#define TestFlag false
#define bPrintFlag false
#define NA -1000000.0
#define ResultItemLength 13  //Calculation开始

#define Max_PolygonCalcCounts 4  
#define MaxHyInsectPoint 1000
#define Max_EnvelopePointsCounts   500   //包络上下边界 单边界最大点数

#define EPSILON 1e-4   //Inflexion dx最小值
#define BUFFER_SIZE 10   // Inflexion 环形缓冲区的大小
#define Sys_Resolution  1000  // Inflexion 1000*dx取整
//feature计算速度
#define SPEED_WINDOW_SIZE 5  // 平均窗口大小

SLimitEoPara        sLimitEo;
using namespace std;


typedef enum eFieldBusType {
	XMax_X = 0,
	XMax_Y = 1,
	YMax_X = 2,
	YMax_Y = 3,
	XMin_X = 4,
	XMin_Y = 5,
	YMin_X = 6,
	YMin_Y = 7,
	Entry_X = 8,
	Entry_Y = 9,
	EXIT_X = 10,
	EXIT_Y = 11,
	PeakPeak_Y = 12,

	Average_Y = 13,
	MinTime = 14,
	MaxTime = 15,
	MinSpeed = 16,
	MaxSpeed = 17,
	MinInflexion = 18,
	MaxInflexion = 19,
	InflexCoord_X = 20,             //add
	InflexCoord_Y = 21,              //add

	MinDeltaY = 22,
	DigIn = 23,
	MaxDeltaY = 24,
	Gradient0X = 25,
	Gradient0Y = 26,
	Gradient1X = 27,
	Gradient1Y = 28,
	Gradient2X = 39,
	Gradient2Y = 30,
	MinGradient = 31,
	MaxGradient = 32,
	Hysteresis = 33

}eFieldBusType;

typedef enum eFormState {
	NoEnter_NoExit = 0,
	Entered_NoExit = 1,
	Entered_Exited = 2,
	EoPass = 3,

	EoError = 10
}eFormState;

typedef enum eEOType {
	eLine = 0,
	ePolygon = 1,
	eEnvelope = 3,
	eHystersis = 5,
	eArea = 6
}eEOType;

typedef struct TagEO_ConfData_Base {
	unsigned short int UseGlobalHy;
	float xHysteresis;
	float yHysteresis;
	unsigned short int RefID;
	bool bInvolveEval;

	//Others
}TagEO_ConfData_Base;

typedef struct TagEO_ConfData_Ext {
	unsigned long int ByteLength;
	short* pConfBuf;
}TagEO_ConfData_Ext;


//Qnext???
//// 定义一个联合体来包含多种EO类型
//union TagEO_ConfData_Union {
//	TagEO_Type_Line line;
//	TagEO_Type_Polygon polygon;
//	TagEO_Type_Envelope envelope;
//};


typedef struct TagFieldBusUnitData {
	char FieldbusName[255];
	unsigned int FieldbbusGrounpIndex;
	unsigned int FieldbbusSubIndex;
}TagFieldBusUnitData;

typedef struct TagFieldBusData {
	unsigned short int FieldbusCount;
	TagFieldBusUnitData FieldbusData[19];
}TagFieldBusData;

typedef struct TagFielPosUintData {
	unsigned int X_GroupIndex;
	unsigned int X_SubIndex;
	unsigned int Y_GroupIndex;
	unsigned int Y_SubIndex;
}TagFielPosUintData;

typedef struct TagFieldbusPos {
	unsigned short int FieldbusPosCount;
	TagFielPosUintData FieldbusPosData[9];
}TagFieldbusPos;

typedef struct TagPolygon_TunnelConf {
	float EdgeLine_a;
	float EdgeLine_b;

	float LimitEdgMin;
	float LimitEdgMax;


}TagPolygon_TunnelConf;



typedef struct TagEO_AttributeVar {
	bool bEvaled = false;
	bool bNoPass[2];
	bool bAnyPass[2];
	bool bOutside = false;
	bool bInside = false;
	unsigned int EoIndex;
	int	EnterStart;
	int EnterEnd;
	int ExitStart;
	int ExitEnd;
	TagPolygon_TunnelConf TunnelVar[10];
	unsigned int PointsCount;    //多边形点数
	unsigned int HyPointsCount;    //多边形迟滞对应点数

	Tag2FPoint RealModelPoints[Max_Polygon_PointsCount];    //多边形真实框
	Tag2FPoint HyModelPoints[Max_Polygon_PointsCount];      //多边形迟滞框

	Tag2FPoint RefRealModelPoints[Max_Polygon_PointsCount];
	Tag2FPoint RefHyModelPoints[Max_Polygon_PointsCount];

	bool bFinishProcess;

}TagEO_AttributeVar;

typedef enum ePolygonCalc_Type {
	AverageY = 0,
	Time_ = 1,
	Speed = 2,
	Inflexion = 3
}ePolygonCalc_Type;


//定义结构体来返回拐点和最大/最小斜率
typedef struct {
	Tag3FPoint inflectionPoint;  //拐点坐标
	float maxSlope;              //最大斜率
	float minSlope;              //最小斜率
} InflectionResult;
//Inflexion 环形缓冲区，用于存储最近的BUFFER_SIZE个点
typedef struct {
	Tag3FPoint points[BUFFER_SIZE];
	int index;       //当前插入点的索引
	int count;       //当前缓冲区中实际存储的点的数量
} CircularBuffer;


typedef struct TagHyInterSectPoint {
	bool bDirection;  //true 穿入； false 穿出
	Tag3FPoint HyInterSectPoint;
}TagHyInterSectPoint;

typedef struct TagEoResultItem {
	const char* iTemName = new char[255];
	//enum eFieldBusType iTemType;
	bool bActive;
	bool isPass;
	float RealValue;
	float MinRange;   //只有Calculation这边需要最大和最小范围
	float MaxRange;
}TagEoResultItem;



typedef struct TagEoResult {
	//char EoIndex; //Eo索引
	int iEoType;
	bool EoPass;
	std::string sErrorCode;
	//const char sErrorCode[20];
	TagEoResultItem ResultItem[64]; //当前是33个
}TagEoResult;

typedef struct TagResult {
	//bool CraftIsOK;   //工艺结果，EO结果
	//bool CurveCount;  //曲线个数
	//char CurveID;    //曲线ID
	//bool CurveIsOK;   //曲线结果
	TagEoResult EoResult;

	//TagEoCurveData CurveResult;  //这个不应该放这里，应该放到外面一层，这是整条曲线的结果，不是EO的结果
}TagEO_Result;

typedef struct TagEO_AssisVar {
	unsigned long int  CurPointCount;   //当前曲线点
	unsigned long int  LastPointCount;
	unsigned long int  Tunnel_CurPointCount;   //当前曲线点
	unsigned long int  Tunnel_LastPointCount;
	enum eFormState FormState;
	bool bAllowRepeatEntryExit;
	Tag2FPoint TempEnterPoint;//穿入点
	Tag2FPoint TempExitPoint;//穿入点
	Tag2FPoint EnterPoint;//穿入点
	float EnterPoint_T;
	bool bDirection;//Line的判断会用到
	bool bIntersectEntryLine;
	bool bHadCrossHy = false;   //进出过迟滞
	bool bHadPointsInPolygon = false;
	short HyInsectPointCount = 0;

	bool bEoToRef = false;  //EoRef
	bool bLineEnterIntersected;
	bool bLineExitIntersected;
	bool bLineRepeatIntersected;
	bool bFirstCalcPoint;
	bool bTempHyExit = false;

	Tag2FPoint ExitPoint; //穿出点
	float ExitPoint_T;
	bool bIntersectExitLine;

	Tag2FPoint Repeat_Err_Point; //重复穿入穿出错误点
	float Repeat_Err_Point_T;
	bool bRErrIntersect;

	bool bOutsideHyPolygon;
	bool ContainPolygon;
	bool bEval;

	unsigned int EntryIntersecPointID;
	unsigned int ExitIntersecPointID;
	unsigned int RepeatIntersecPointID;
	TagEoResultItem ProcessVar[ResultItemLength];

	bool bHyEntered;  //真实框穿入迟滞框
	bool bHyExited;  //迟滞框穿入真实框
	TagHyInterSectPoint aHyIntersectPoint[100];

	// 新增CalcCount相关计算的状态变量
	//AverageY
	bool bFirst_Ave;
	unsigned int Aver_CalcPointCount;
	float Aver_TempTotal;
	float AverageY_Data;

	//Time_
	bool bFirst_Time;

	//Speed
	bool bFirst_Speed;
	float StartX;
	float StartTime;
	float Speed_Data;
	float RecentSpeeds[SPEED_WINDOW_SIZE]; // 假设SPEED_WINDOW_SIZE已定义
	int SpeedIndex;
	bool WindowFilled;

	//Inflexion
	bool bFirst_Inflexion;
	CircularBuffer buffer; // 假设CircularBuffer类型已定义

}TagEO_AssisVar;

typedef struct TagEO_Data {
	//unsigned int EoCount;
	enum eEOType EOType;
	unsigned int EoMap;
	bool Enable;
	char Versions;
	TagEO_ConfData_Base ConfData_Base;

	//TagEO_ConfData_Union EoConfDataUnion;
	TagEO_ConfData_Ext ExtConf;
	TagFieldBusData FieldbusData;
	TagFieldbusPos FieldbusPos;

	TagEO_AttributeVar AttributeVar;

	TagEO_AssisVar CalcAssistVar;
	TagEO_Result Result;
	ErrorCode EoError;

}TagEO_Data;

//!!!!!EO Type Struct !!!!!
typedef struct TagTunnelGroupData {
	short TunnelStartPoint;
	short TunnelEndPoint;
}TagTunnelGroupData;

typedef struct TagPolygonCalcRange {
	bool bActive;
	float Min;
	float Max;
}TagPolygonCalcRange;

typedef struct TagRectangleCalUnit {
	TagPolygonCalcRange aRange[4];
	float Inflexion_dx;
}TagRectangleCalUnit;

typedef struct TagEO_Type_Line {
	short EntryStart;
	short EntryEnd;
	Tag2FPoint Point[2];
	//Tag2FPoint Point1;
}TagEO_Type_Line;

typedef struct TagEO_Type_Polygon {
	int EntryStart;
	int EntryEnd;
	int ExitStart;
	int ExitEnd;
	short int Contains;
	unsigned int PointsCount;
	Tag2FPoint aRectanglePoints[10];
	unsigned int TunnelCount;
	TagTunnelGroupData TunnelGroup[10];
	unsigned short int CalcCount;
	TagRectangleCalUnit CalcConf;

}TagEO_Type_Polygon;


//add Envelope Data
typedef struct TagEO_Type_Envelope {
	float XMax;
	float Xmin;
	float Ymax;
	float Ymin;
	int iEntryExitType;
	int iLowerPointsCount;
	int iUpperPointsCount;
	int iEnvelopePointsCount;
	Tag2FPoint aLowerPoint[Max_EnvelopePointsCounts];
	Tag2FPoint aUpperPoint[Max_EnvelopePointsCounts];
	Tag2FPoint aEnvelopePoint[2 * Max_EnvelopePointsCounts];  //上边界+下边界合起来的从上->下，左->右的闭合多边形
}TagEO_Type_Envelope;

typedef struct TagEo_PythonData {
	bool bIsPass;
	char Operator[255];
	Tag3FPoint tMinPoint;
	Tag3FPoint tMaxPoint;
	float TimeAverage;

	Tag3FPoint XminPoint;
	Tag3FPoint XmaxPoint;
	float XAverage;
	Tag3FPoint ForceMinPoint;
	Tag3FPoint ForceMaxPoint;
	float ForceAverage;

	float PeakPeakX;
	float PeakPeakY;

}TagEo_PythonData;

typedef struct PublicPara {
	float LocalxHy;
	float LocalyHy;

}PublicPara;

typedef struct TagEo_TunnelPara {
	unsigned long int  CurPointCount;   //停止区域 当前曲线点
	unsigned long int  LastPointCount;

}TagEo_TunnelPara;

/******************************	Simulation	*********************************/
typedef struct MarchSiumInData
{
	unsigned short int	_MotorCW;
	int		          _TargetPos;
	char		      _ModesOfOp;
}MarchSiumInData;

typedef struct MarchSiumOutData
{
	float		        _Force;
	int		            _MotorRealPostion;
	int		            _EncoderPosition;
	unsigned short int	_MotorSw;
	unsigned short int	_MotorErrorCode;
	char		        _ModesOfOpDis;
	float		        ExtSensorPos;
}MarchSiumOutData;

/************************************************************	Fuction Declare	***************************************************************/

extern unsigned int AppendPoints();//读取压机CSV文件点数据

bool Geometry_Point_Polygon(Tag3FPoint* pcheckPoint, Tag2FPoint* pPolygon, unsigned int PointsCount);

extern bool Geometry_Point_Line(struct Tag3FPoint* pPoint, struct Tag2FPoint* pLine1, struct Tag2FPoint* pLine2);
bool Geometry_Line_Line(Tag2FPoint* pA, Tag2FPoint* pB, Tag2FPoint* pC, Tag2FPoint* pD, Tag2FPoint* pIntersection);
//void GetPolygonProcessMinMax(TagEO_Data* pEO_Data);
bool IsPointAtLine(Tag3FPoint* pQ, Tag2FPoint* pLine1, Tag2FPoint* pLine2);
unsigned int EO_Line_Init(TagEO_Data* pEO_Data);
bool EO_Line_IsOk(TagEO_Data* pEO_Data, Tag3FPoint* pCurve, bool SeqFinished);
void EO_Line_ResetPara();
//void EoResultToFieldusData();

void ResultData(char** pBuffer, uint64* Length, bool* pbCraftIsOK);

void ProcessVarCalc(TagEO_Data* pEoData, Tag3FPoint* pCurPoint);
unsigned int EO_Polygon_Init(TagEO_Data* pEO_Data);
bool EO_Polygon_IsOK(TagEO_Data* pEO_Data, Tag3FPoint* pCurve, bool SeqFinished);
void EO_Polygon_ResetPara(TagEO_Data* pEO_Data);

unsigned int EO_Envelope_Init(TagEO_Data* pEO_Data);
bool EO_Envelope_IsOK(TagEO_Data* pEO_Data, Tag3FPoint* pCurve, bool SeqFinished);
void EO_Envelope_ResetPara(TagEO_Data* pEO_Data);

/******************************	Simulation	*********************************/
bool R_Trig(bool preSignal, bool curSignal);
double Delay(double newValue);
float GetAntiForce(float TargetPos);
extern void MarchSium(MarchSiumInData* psMarchSiumIn, MarchSiumOutData* psMarchSiumOut);







using namespace std;

using namespace rapidjson;
/************************************************************	Variables	***************************************************************/

/******************************	EO-line	*********************************/

TagEO_Type_Line* pConf_Line;
Tag2FPoint LineHyPoint[4];
//bool bDirection;

/******************************	EO-All	*********************************/
TagEO_Data eoConfs[Max_EO_Count] = {};
Tag3FPoint Buffer[CURVE_MAX_POINTS_NUMBER] = {};
unsigned int PointCounts;
unsigned char EoCount;
bool bAddEoUnit;
bool BufferFinished;

/******************************	EO-Main	*********************************/
bool bCurveTruncationVisible;
uint32 TurncationPointIndex;
PublicPara PubPara;
bool bTunnelStop;
bool bTrigerSeqStop;
bool EoCalcFinish;


/******************************	EO-Polygon	*********************************/

//TagEoResultItem ProcessVar[ResultItemLength];
TagEO_Type_Polygon* pPolygon;
TagEO_Data* pEoData;
Tag3FPoint* pCurPoint, * pLastPoint;
Tag2FPoint Point2F1, Point2F2;
//相交状态
//enum eFormState envelopeFormState, envelopeLastForm;
//enum eFormState FormState, LastForm;  
//enum eFormState LFormState;

unsigned int bProcessCalcCount;
unsigned int j;
unsigned long long Index;
//评估结束标志位
bool bEvalEnd;
//bool bFirst_Ave[10], bFirst_Time_[10], bFirst_Speed[10], bFirst_Inflexion[10];
bool bHavePointsInPolygon[10];
//bool bFirstCalcPoint;
//当前点与矩形的关系
bool Relation, LastRelation, HyRelation;
//线和矩形的关系
//bool bLineEnterIntersected, bLineExitIntersected, bLineRepeatIntersected;    
bool bSeqHadFinished = false;
//float AverageY_Data[10], Aver_TempTotal[10], Aver_CalcPointCount[10], StartX[10], StartTime[10], Speed_Data[10];  
float TempVar;

/*******************************     EO-Envelope     ************************************/
TagEO_Type_Envelope* pEnvelope;


/******************************	EO-Result	*********************************/
TagEoCurveData CurResult;

const char* aFieldbusName[64] = { "XMax-X","XMax-Y","YMax-X","YMax-Y","XMin-X","XMin-Y","YMin-X","YMin-Y",
								  "Entry-X","Entry-Y","Exit-X","Exit-Y","Peak-Peak-Y","AverageY","MinTime","MaxTime",
								  "MinSpeed","MaxSpeed","MinInflexion","MaxInflexion","InflexCoordX","InflexCoordY" };

// Populate ResultItem objects
vector<string> PolygonResultNames = {
	"XMax-X", "XMax-Y", "YMax-X", "YMax-Y", "XMin-X", "XMin-Y", "YMin-X", "YMin-Y",
	"Entry-X", "Entry-Y", "Exit-X", "Exit-Y", "Peak-Peak-Y", "AverageY", "MinTime",
	"MaxTime", "MinSpeed", "MaxSpeed", "MinInflexion", "MaxInflexion",
	"InflexCoordX", "InflexCoordY"
};
vector<string> LineResultNames = {
	"Entry-X", "Entry-Y"
};

vector<string> EnvelopeResultNames = {
	"XMax-X", "XMax-Y", "YMax-X", "YMax-Y", "XMin-X", "XMin-Y", "YMin-X", "YMin-Y",
	"Entry-X", "Entry-Y", "Exit-X", "Exit-Y", "Peak-Peak-Y"
};


/************************************************************	FUCTION	***************************************************************/
/**
* @name Vector2D
* @brief Get Vector2D
* @param A
* @param B
* @retuen AB
*/
Tag2FPoint Vector2D(Tag2FPoint A, Tag2FPoint B)
{
	Tag2FPoint AB;
	AB.X = B.X - A.X;
	AB.Y = B.Y - A.Y;
	return AB;
}
/**
* @name CrossProduct
* @brief Caculate the CrossProduct of Point1 and Point2
* @param Point1
* @param Point2
* @retuen CrossProduct Value
*/
float CrossProduct(Tag2FPoint Point1, Tag2FPoint Point2)
{
	return Point1.X * Point2.Y - Point1.Y * Point2.X;
}

/**
* @name IsPointAtLine
* @brief Is the Point at the line
* @param Point
* @param Line1 Line2
* @retuen
*/
bool IsPointAtLine(Tag3FPoint* pQ, Tag2FPoint* pLine1, Tag2FPoint* pLine2)
{
	float crossProduct;
	//保证点不在线段的延长线上以及反向延长线上
	if (pQ->X >= min(pLine1->X, pLine2->X) && pQ->X <= max(pLine2->X, pLine1->X) && pQ->Y >= min(pLine1->Y, pLine2->Y) && pQ->Y <= max(pLine2->Y, pLine1->Y))
	{
		crossProduct = (pLine1->X - pQ->X) * (pLine2->Y - pQ->Y) - (pLine1->Y - pQ->Y) * (pLine2->X - pQ->X);
		if (crossProduct == 0.0)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	else
	{
		return false;
	}
}

/**
* @name Geometry_Point_Line
* @brief Judge the direction of a point and the Line12
* @param pPoint
* @param pLine1
* @param pLine2
* @retuen bRelation
*/
bool Geometry_Point_Line(Tag3FPoint* pPoint, Tag2FPoint* pLine1, Tag2FPoint* pLine2)
{
	bool bRelation = false;

	//计算向量叉积
	float crossProduct = (pLine2->X - pLine1->X) * (pPoint->Y - pLine1->Y) -
		(pLine2->Y - pLine1->Y) * (pPoint->X - pLine1->X);

	//判断叉积的符号
	if (crossProduct > 0)
	{
		bRelation = true;  //点在直线的左侧或下方
	}
	else
	{
		bRelation = false; //点在直线的右侧或上方
	}

	return bRelation;
}





/**
* @name IsValidCrossing
* @brief 判断两点是否有效穿越线段
* @param pCurPoint  pLastPoint
* @param pLine1
* @param pLine2
* @retuen bRelation
*/
bool IsValidCrossing(Tag3FPoint* pCurPoint, Tag3FPoint* pLastPoint,
	Tag2FPoint* pLine1, Tag2FPoint* pLine2)
{
	// 检查左右位置是否改变
	bool bDir = Geometry_Point_Line(pCurPoint, pLine1, pLine2);
	bool bLastDir = Geometry_Point_Line(pLastPoint, pLine1, pLine2);

	// 如果方向没有变化，则不是穿越
	if (bDir == bLastDir) {
		return false;
	}

	// 计算两点连线与线段的交点
	Tag2FPoint Point2F1 = { pCurPoint->X, pCurPoint->Y };
	Tag2FPoint Point2F2 = { pLastPoint->X, pLastPoint->Y };
	Tag2FPoint IntersectionPoint;

	bool hasIntersection = Geometry_Line_Line(pLine1, pLine2,
		&Point2F1, &Point2F2,
		&IntersectionPoint);

	// 检查交点是否在线段上
	if (hasIntersection) {
		// 计算线段向量和长度
		float ABx = pLine2->X - pLine1->X;
		float ABy = pLine2->Y - pLine1->Y;
		float AB_squared = ABx * ABx + ABy * ABy;

		// 计算交点相对于线段起点的向量
		float AIx = IntersectionPoint.X - pLine1->X;
		float AIy = IntersectionPoint.Y - pLine1->Y;

		// 计算交点在线段上的投影位置比例
		float t = (AIx * ABx + AIy * ABy) / AB_squared;

		// 检查交点是否在线段范围内 (0 <= t <= 1)
		return (t >= 0.0f && t <= 1.0f);
	}

	return false; // 无交点
}

/**
* * @name IsPointOnSegment
 * @brief 线线相交 辅助函数：检查一个点是否在线段上（用于处理共线情况）
 */
bool IsPointOnSegment(const Tag2FPoint* p, const Tag2FPoint* a, const Tag2FPoint* b) {

	bool in_x_range = (p->X >= (a->X < b->X ? a->X : b->X) &&
		p->X <= (a->X > b->X ? a->X : b->X));

	bool in_y_range = (p->Y >= (a->Y < b->Y ? a->Y : b->Y) &&
		p->Y <= (a->Y > b->Y ? a->Y : b->Y));

	return in_x_range && in_y_range;
}


/**
* @name  Geometry_Line_Line
* @brief Judge the Intersecting relationships of LineAB and LineCD
* @param pA pB pC pD(A B 是Line,Polygon Point)
* @param Intersection : The intersection point
* @retuen relation of intersection (bool)
*/
bool Geometry_Line_Line(Tag2FPoint* pA, Tag2FPoint* pB, Tag2FPoint* pC, Tag2FPoint* pD, Tag2FPoint* pIntersection)
{
	float r_dx = pB->X - pA->X;
	float r_dy = pB->Y - pA->Y;
	float s_dx = pD->X - pC->X;
	float s_dy = pD->Y - pC->Y;

	float denominator = r_dx * s_dy - r_dy * s_dx;

	float ac_dx = pC->X - pA->X;
	float ac_dy = pC->Y - pA->Y;

	//检查是否平行或共线
	if (std::fabs(denominator) < 1e-6f)
	{
		//是否共线？
		float cross_product_ac_r = ac_dx * r_dy - ac_dy * r_dx;
		if (std::fabs(cross_product_ac_r) < 1e-6f)
		{
			if (IsPointOnSegment(pA, pC, pD) || IsPointOnSegment(pB, pC, pD) ||
				IsPointOnSegment(pC, pA, pB) || IsPointOnSegment(pD, pA, pB)) {
				//共线且重叠，技术上是相交的，但交点不唯一。
				//这种情况下，可以选择不计算特定交点或返回一个端点。
				//这里为了简单，直接返回true，后续再看是否在改
				if (pIntersection != nullptr) {
					// 对于共线重叠，交点没有唯一确定值，？？
					// 先返回pA或pC作为参考点
					pIntersection->X = pA->X;
					pIntersection->Y = pA->Y;
				}
				return true;
			}
		}
		//平行但不共线，或共线但不重叠
		return false;
	}

	float t = (ac_dx * s_dy - ac_dy * s_dx) / denominator;
	float u = (ac_dx * r_dy - ac_dy * r_dx) / denominator;

	//判断交点是否在两条线段内部
	//t和u都必须在 [0, 1] 区间内
	if (t >= -1e-6f && t <= 1.0f + 1e-6f && u >= -1e-6f && u <= 1.0f + 1e-6f)
	{
		//线段相交，计算交点
		if (pIntersection != nullptr)
		{
			pIntersection->X = pA->X + t * r_dx;
			pIntersection->Y = pA->Y + t * r_dy;
		}
		return true;
	}
	//交点在延长线上，线段本身不相交
	return false;
}


/**
* @name  Geometry_Point_Polygon
* @brief Judge the contain relationships between a point and a polygon
* @param pcheckPoint
* @param pPolygon
* @param PointsCount
* @retuen relation of Containment (bool)
*/
bool Geometry_Point_Polygon(Tag3FPoint* pcheckPoint, Tag2FPoint* pPolygon, unsigned int PointsCount) {
	if (PointsCount == 0) {
		if (bPrintFlag) printf("The PointsCount is invalid!");
		return false;
	}

	//射线法判断点是否在多边形内
	int nCross = 0;
	for (unsigned int i = 0; i < PointsCount; i++) {
		Tag2FPoint P1 = pPolygon[i];
		Tag2FPoint P2 = pPolygon[(i + 1) % PointsCount]; //处理闭合边

		//检查点是否在当前边上
		if (IsPointAtLine(pcheckPoint, &P1, &P2)) {
			return true;
		}

		//忽略水平边
		if (P1.Y == P2.Y) continue;

		//确定边的方向及有效Y区间
		float deltaY = P2.Y - P1.Y;
		bool isUpward = deltaY > 0;
		float minY = isUpward ? P1.Y : P2.Y;
		float maxY = isUpward ? P2.Y : P1.Y;

		//检查点Y坐标是否在有效区间内（考虑开闭区间）
		if ((isUpward && (pcheckPoint->Y >= minY && pcheckPoint->Y < maxY)) ||
			(!isUpward && (pcheckPoint->Y > minY && pcheckPoint->Y <= maxY)))
		{
			//计算交点X坐标
			float xIntersection = (pcheckPoint->Y - P1.Y) * (P2.X - P1.X) / deltaY + P1.X;

			//确保交点在点的严格右侧
			if (xIntersection > pcheckPoint->X) {
				nCross++;
			}
		}
	}

	return (nCross % 2) == 1;
}

/**
* @name  AddEoUnit
* @brief Analysis Json Profile-Demo
* @param
* @retuen ErrCode(unsigned int)
* @Areas External calls
*/
ErrorCode AddEoUnit(char* pEO)
{
	uint16 u16errCode = 0;
	float EntryStart, EntryStop, ExitStart, ExitStop;
	bool bContain;
	unsigned int ErrCode = 0;
	Tag2FPoint ConfLine[2] = { 0 };
	char* EO;
	EO = pEO;
	Document eo;
	eo.Parse(EO);

	if (eo.HasParseError())
	{
		if (bPrintFlag)
		{
			printf("Err: Can not parse Json Eo!");
		}
		u16errCode = 46001;  //Err: Can not parse Json Eo!
	}

	//Init eoConfs
	if (Max_EO_Count > 0)
	{
		// 释放之前分配的内存
		//for (unsigned char i = 0; i < Max_EO_Count - 1; i++)
		//{
		//	if (eoConfs[i].ExtConf.pConfBuf != NULL)
		//	{
		//		free(eoConfs[i].ExtConf.pConfBuf);
		//		eoConfs[i].ExtConf.pConfBuf = NULL; 
		//	}		
		//}
		// 使用 memset 将结构体数组中的指针初始化为 NULL
		memset(eoConfs, 0, sizeof(eoConfs));
		// 为每个结构体中的指针分配内存并初始化

	}

	if (eo.IsObject() && eo.HasMember("Branchs") && eo["Branchs"].IsArray())
	{
		const Value& branchs = eo["Branchs"];

		for (SizeType i = 0; i < branchs.Size(); ++i)
		{
			const Value& branch = branchs[i];

			// Name
			if (branch.IsObject() && branch.HasMember("Name") && branch["Name"].IsString())
			{
				//std::cout << "Branch Name: " << branch["Name"].GetString() << std::endl;
			}
			else
			{
				u16errCode = 46010;  //Err: Json string invalid
				return u16errCode;
			}
			//"Evaluation"--> "Branchs"
			if (branch.HasMember("Evaluation") && branch["Evaluation"].IsObject())
			{
				const Value& evaluation = branch["Evaluation"];

				// "EvaluationObjects" array --> "Evaluation"
				if (evaluation.HasMember("EvaluationObjects") && evaluation["EvaluationObjects"].IsArray())
				{
					const Value& evalObjects = evaluation["EvaluationObjects"];

					EoCount = evalObjects.Size();
					//std::cout << "EOCount：" << EoCount << std::endl;

					if (EoCount > Max_EO_Count)
					{
						bAddEoUnit = false;
						return 46011;
					}
					for (SizeType j = 0; j < evalObjects.Size(); ++j)
					{
						const Value& TempEoObject = evalObjects[j];

						if (TempEoObject.IsObject())
						{
							//EoType
							if (TempEoObject.HasMember("EoType") && TempEoObject["EoType"].IsInt())
							{
								eoConfs[j].EOType = (eEOType)TempEoObject["EoType"].GetInt();
								eoConfs[j].EoMap = j;
								//std::cout << "EoType: " << eoConfs[j].EOType << std::endl;
							}
							else
							{
								u16errCode = 46012;  //Err: Json string invalid
								return u16errCode;
							}
							//Property
							if (TempEoObject.HasMember("Property") && TempEoObject["Property"].IsObject())
							{
								const Value& TempProperty = TempEoObject["Property"];

								if (TempProperty.HasMember("Identity") && TempProperty["Identity"].IsInt())
								{
									eoConfs[j].ConfData_Base.RefID = TempProperty["Identity"].GetInt();
									//std::cout << "Identity:" << eoConfs[j].ConfData_Base.RefID << std::endl;
								}
								else
								{
									u16errCode = 46013;  //Err: Json string invalid
									return u16errCode;

								}

								if (TempProperty.HasMember("IsInvolved") && TempProperty["IsInvolved"].IsBool())
								{
									eoConfs[j].ConfData_Base.bInvolveEval = TempProperty["IsInvolved"].GetBool();
									//std::cout << "InvolveEval:" << eoConfs[j].ConfData_Base.bInvolveEval << std::endl;
								}
								else
								{
									u16errCode = 46014;  //Err: Json string invalid
									return u16errCode;
								}

								if (TempProperty.HasMember("IsProgramHysteresis") && TempProperty["IsProgramHysteresis"].IsBool())
								{
									eoConfs[j].ConfData_Base.UseGlobalHy = !TempProperty["IsProgramHysteresis"].GetBool();
									//std::cout << "IsProgramHysteresis:" << eoConfs[j].ConfData_Base.UseGlobalHy << std::endl;
								}
								else
								{
									u16errCode = 46015;  //Err: Json string invalid
									return u16errCode;

								}

								// XHysteresis
								if (TempProperty.HasMember("XHysteresis")) {
									if (TempProperty["XHysteresis"].IsFloat()) {
										eoConfs[j].ConfData_Base.xHysteresis = TempProperty["XHysteresis"].GetFloat();
									}
									else if (TempProperty["XHysteresis"].IsInt()) {
										int xHysteresisInt = TempProperty["XHysteresis"].GetInt();
										eoConfs[j].ConfData_Base.xHysteresis = static_cast<float>(xHysteresisInt);
									}
									// std::cout << "XHysteresis:" << eoConfs[j].ConfData_Base.xHysteresis << std::endl;
								}

								// YHysteresis
								if (TempProperty.HasMember("YHysteresis")) {
									if (TempProperty["YHysteresis"].IsFloat()) {
										eoConfs[j].ConfData_Base.yHysteresis = TempProperty["YHysteresis"].GetFloat();
									}
									else if (TempProperty["YHysteresis"].IsInt()) {
										int yHysteresisInt = TempProperty["YHysteresis"].GetInt();
										eoConfs[j].ConfData_Base.yHysteresis = static_cast<float>(yHysteresisInt);
									}
									// std::cout << "YHysteresis:" << eoConfs[j].ConfData_Base.yHysteresis << std::endl;
								}

							}
							else
							{
								u16errCode = 46018;  //Err: Json string invalid
								return u16errCode;

							}

							switch (eoConfs[j].EOType)
							{
							case eLine:
								//EoContent
							{
								if (TempEoObject.HasMember("EoContent") && TempEoObject["EoContent"].IsObject())
								{
									const Value& TempEoContent = TempEoObject["EoContent"];
									if (TempEoContent.HasMember("EntryStart") && TempEoContent["EntryStart"].IsInt())
									{
										EntryStart = TempEoContent["EntryStart"].GetInt();
										//std::cout << "EntryStart:" << EntryStart << std::endl;
									}
									else
									{
										u16errCode = 46019;  //Err: Json string invalid
										//return u16errCode;

									}

									if (TempEoContent.HasMember("EntryStop") && TempEoContent["EntryStop"].IsInt())
									{
										EntryStop = TempEoContent["EntryStop"].GetInt();
										//std::cout << "EntryStop:" << EntryStop << std::endl;
									}
									else
									{
										u16errCode = 46020;  //Err: Json string invalid
										return u16errCode;

									}
									// Accessing "LinePoints" array within "EoContent"
									if (TempEoContent.HasMember("LinePoints") && TempEoContent["LinePoints"].IsArray())
									{
										const Value& linePoints = TempEoContent["LinePoints"];

										//Size()==2
										for (SizeType k = 0; k < linePoints.Size(); ++k) {
											const Value& linePoint = linePoints[k];

											// Accessing "X" and "Y" objects within "LinePoints"
											if (linePoint.HasMember("X") && linePoint["X"].IsObject() &&
												linePoint.HasMember("Y") && linePoint["Y"].IsObject()) {
												float xValue = linePoint["X"]["Value"].GetFloat();
												float yValue = linePoint["Y"]["Value"].GetFloat();
												if (k >= 0 && k < 2)
												{
													ConfLine[k].X = xValue;
													ConfLine[k].Y = yValue;
												}
												else
												{
													if (bPrintFlag)
													{
														printf("Err: LinePoints count is more than 2 !");
													}

													u16errCode = 46021;  //Err: Json string invalid
													return u16errCode;
												}
												//std::cout << "Point " << k << ": X=" << xValue << ", Y=" << yValue << std::endl;
											}
										}
									}
									else
									{
										u16errCode = 46022;  //Err: Json string invalid
										return u16errCode;

									}
								}
								else
								{
									u16errCode = 46023;  //Err: Json string invalid
									return u16errCode;

								}

								//Result  array
								if (TempEoObject.HasMember("Results") && TempEoObject["Results"].IsArray() && !TempEoObject["Results"].IsNull())
								{
									const Value& aResult = TempEoObject["Results"];
									if (aResult.IsArray() && !aResult.IsNull())
									{
										//char iLineResultCount = aResult.Size();

										for (SizeType i = 0; i < aResult.Size(); i++)
										{
											const Value& result = aResult[i];

											//check the key of "IsVisiable"  //Qnext
											if (result.IsObject() && result.HasMember("IsVisiable") && result["IsVisiable"].IsBool())
											{
												bool isVisible = result["IsVisiable"].GetBool();
												eoConfs[j].Result.EoResult.ResultItem[i].bActive = isVisible;
												if (isVisible)
												{
													//对下字符串
													if (result.HasMember("Name") && result["Name"].IsString())
													{
														const char* name = result["Name"].GetString();
														if (i + 8 < Max_FieldBusNameCount)
														{
															if (strcmp(name, aFieldbusName[i + 8]) == 0)   //Entry-X,Enty-Y
															{
																//printf("EoIndex= %d ,bActive \n", j, isVisible);
																//std::cout << "ResultItem " << i << ": Name=aFieldbus" << ", Name=" << name << std::endl;
																//Qnext "FieldBus"字段
															}
															else
															{
																//std::cout << "Error ResultItem " << i << ": Name!=aFieldbus" << ", Name=" << name << std::endl;
																u16errCode = 46024;  //Err: Max_FieldBusNameCount Err
																return u16errCode;

															}
														}
														else
														{
															u16errCode = 46025;  //Err: Max_FieldBusNameCount Err
															return u16errCode;

														}

													}
												}
												else
												{
													//invalid Result Item
													//std::cout << "ResultItem " << i << ": IsVisiable=" << isVisible << std::endl;
												}
											}
										}
									}
									else
									{
										u16errCode = 46026;  //Err: Json string invalid
										return u16errCode;

									}
								}
								else
								{
									u16errCode = 46027;  //Err: Json string invalid
									return u16errCode;

								}

								//分配内存前先判空是否释放内存
								if (eoConfs[j].ExtConf.pConfBuf != nullptr) {
									delete reinterpret_cast<TagEO_Type_Line*>(eoConfs[j].ExtConf.pConfBuf);
									eoConfs[j].ExtConf.pConfBuf = nullptr;
								}

								TagEO_Type_Line* pTempLine = new TagEO_Type_Line;
								if (pTempLine == nullptr) {
									return 46028;  // 内存分配失败，返回错误代码
								}
								pTempLine->EntryStart = EntryStart;
								pTempLine->EntryEnd = EntryStop;
								pTempLine->Point[0] = ConfLine[0];   // 设置第一个点
								pTempLine->Point[1] = ConfLine[1];  // 设置第二个点
								// 将指针赋值给 pConfBuf
								eoConfs[j].ExtConf.pConfBuf = reinterpret_cast<short*>(pTempLine);

								break;
							}
							case ePolygon:
							{
								//EoContent
								TagEO_Type_Polygon* ptemp;
								ptemp = new TagEO_Type_Polygon;
								if (ptemp == nullptr) {
									return 46028;  // 内存分配失败，返回错误代码
								}


								if (TempEoObject.HasMember("EoContent") && TempEoObject["EoContent"].IsObject())
								{
									//Points
									const Value& TempEoContent = TempEoObject["EoContent"];
									if (TempEoContent.HasMember("EntryStart") && TempEoContent["EntryStart"].IsInt())
									{
										EntryStart = TempEoContent["EntryStart"].GetInt();
										//std::cout << "EntryStart:" << EntryStart << std::endl;
									}
									else
									{
										u16errCode = 46029;  //Err: Json string Err
										return u16errCode;
									}

									if (TempEoContent.HasMember("EntryStop") && TempEoContent["EntryStop"].IsInt())
									{
										EntryStop = TempEoContent["EntryStop"].GetInt();
										//std::cout << "EntryStop:" << EntryStop << std::endl;
									}
									else
									{
										u16errCode = 46030;  //Err: Json string Err
										return u16errCode;
									}

									if (TempEoContent.HasMember("ExitStart") && TempEoContent["ExitStart"].IsInt())
									{
										ExitStart = TempEoContent["ExitStart"].GetInt();
										//std::cout << "ExitStart:" << ExitStart << std::endl;
									}
									else
									{
										u16errCode = 46031;  //Err: Json string Err
										return u16errCode;
									}

									if (TempEoContent.HasMember("ExitStop") && TempEoContent["ExitStop"].IsInt())
									{
										ExitStop = TempEoContent["ExitStop"].GetInt();
										//std::cout << "ExitStop:" << ExitStop << std::endl;
									}
									else
									{
										u16errCode = 46032;  //Err: Json string Err
										return u16errCode;
									}

									if (TempEoContent.HasMember("IsInside") && TempEoContent["IsInside"].IsBool())
									{
										bContain = TempEoContent["IsInside"].GetBool();
										//std::cout << "ExitStop:" << ExitStop << std::endl;
									}
									else
									{
										u16errCode = 46053;  //Err: Json string Err
										return u16errCode;
									}

									// Accessing "LinePoints" array within "EoContent"
									if (TempEoContent.HasMember("PolygonPoints") && TempEoContent["PolygonPoints"].IsArray())
									{
										const Value& polygonPoints = TempEoContent["PolygonPoints"];

										ptemp->PointsCount = polygonPoints.Size();

										for (SizeType k = 0; k < polygonPoints.Size(); ++k) {
											const Value& polygonPoint = polygonPoints[k];

											// Accessing "X" and "Y" objects within "LinePoints"
											if (polygonPoint.HasMember("X") && polygonPoint["X"].IsObject() &&
												polygonPoint.HasMember("Y") && polygonPoint["Y"].IsObject()) {
												float xValue = polygonPoint["X"]["Value"].GetFloat();
												float yValue = polygonPoint["Y"]["Value"].GetFloat();
												ptemp->aRectanglePoints[k].X = xValue;
												ptemp->aRectanglePoints[k].Y = yValue;
												//std::cout << "Point " << k << ": X=" << xValue << ", Y=" << yValue << std::endl;
											}
											else
											{
												u16errCode = 46033;  //Err: Json string Err
												return u16errCode;
											}
										}
									}
									else
									{
										u16errCode = 46034;  //Err: Json string Err
										return u16errCode;
									}


									//Tunnels
									if (TempEoContent.HasMember("Tunnels") && TempEoContent["Tunnels"].IsArray())
									{
										const Value& tunnels = TempEoContent["Tunnels"];
										ptemp->TunnelCount = tunnels.Size();
										if (ptemp->TunnelCount > 0 && ptemp->TunnelCount < Max_Polygon_PointsCount)
										{
											for (SizeType i = 0; i < tunnels.Size(); ++i)
											{
												const Value& tunnel = tunnels[i];

												if (tunnel.HasMember("Start") && tunnel["Start"].IsInt() && tunnel.HasMember("Stop") && tunnel["Stop"].IsInt())
												{
													int start = tunnel["Start"].GetInt();
													int stop = tunnel["Stop"].GetInt();
													ptemp->TunnelGroup[i].TunnelStartPoint = start;
													ptemp->TunnelGroup[i].TunnelEndPoint = stop;
													//std::cout << "Tunnel " << i << ": Start=" << start << ", Stop=" << stop << std::endl;

												}
												else
												{
													u16errCode = 46035;  //Err: Json string Err
													return u16errCode;
												}
											}
										}
									}

								}
								else
								{
									u16errCode = 46038;  //Err: Json string Err
									return u16errCode;

								}

								//Result  array
								if (TempEoObject.HasMember("Results") && TempEoObject["Results"].IsArray() && !TempEoObject["Results"].IsNull())
								{
									const Value& aResult = TempEoObject["Results"];
									if (aResult.IsArray() && !aResult.IsNull())
									{
										for (SizeType i = 0; i < aResult.Size(); i++)
										{
											const Value& result = aResult[i];

											//check the key of "IsVisiable"  //Qnext
											if (result.IsObject() && result.HasMember("IsVisiable") && result["IsVisiable"].IsBool())
											{
												bool isVisible = result["IsVisiable"].GetBool();
												eoConfs[j].Result.EoResult.ResultItem[i].bActive = isVisible;
												if (isVisible)
												{
													//对下字符串
													if (result.HasMember("Name") && result["Name"].IsString())
													{
														const char* name = result["Name"].GetString();
														if (i < Max_FieldBusNameCount)
														{
															if (strcmp(name, aFieldbusName[i]) == 0)
															{

																//std::cout << "ResultItem " << i << ": Name=aFieldbus" << ", Name=" << name << std::endl;
																//Qnext "FieldBus"字段
															}
															else
															{
																u16errCode = 46039;  //Err: Json string Err
																return u16errCode;
															}
														}
													}
												}
											}
											else
											{
												u16errCode = 46041;  //Err: Json string Err
												return u16errCode;
											}
										}
									}
								}


								//Calculation array
								if (TempEoObject.HasMember("CalculationVisiable") && TempEoObject["CalculationVisiable"].IsBool())
								{
									bool bCalcisVisible = TempEoObject["CalculationVisiable"].GetBool();
									if (bCalcisVisible)
									{
										if (TempEoObject.HasMember("Calculations") && TempEoObject["Calculations"].IsArray() && !TempEoObject["Calculations"].IsNull())
										{
											const Value& calculations = TempEoObject["Calculations"];

											ptemp->CalcCount = calculations.Size();
											if (ptemp->CalcCount > 0 && ptemp->CalcCount <= 4)
											{
												// Loop through each object in the "Calculations" array
												for (SizeType i = 0; i < calculations.Size(); ++i) {
													const Value& calculation = calculations[i];

													// Check if the object has "Visible" key
													if (calculation.IsObject() && calculation.HasMember("Visiable") && calculation["Visiable"].IsBool())
													{
														bool isVisible = calculation["Visiable"].GetBool();
														ptemp->CalcConf.aRange[i].bActive = isVisible;    //新增 isVisible 判断是否有效
														if (isVisible)
														{
															// Max
															if (calculation.HasMember("Max")) {
																if (calculation["Max"].IsFloat()) {
																	float max = calculation["Max"].GetFloat();
																	ptemp->CalcConf.aRange[i].Max = max;
																}
																else if (calculation["Max"].IsInt()) {
																	int maxInt = calculation["Max"].GetInt();
																	float max = static_cast<float>(maxInt);
																	ptemp->CalcConf.aRange[i].Max = max;
																}
																else {
																	u16errCode = 46041; // Err: Json string Err
																	return u16errCode;
																}
															}

															// Min
															if (calculation.HasMember("Min")) {
																if (calculation["Min"].IsFloat()) {
																	float min = calculation["Min"].GetFloat();
																	ptemp->CalcConf.aRange[i].Min = min;
																}
																else if (calculation["Min"].IsInt()) {
																	int minInt = calculation["Min"].GetInt();
																	float min = static_cast<float>(minInt);
																	ptemp->CalcConf.aRange[i].Min = min;
																}
																else {
																	u16errCode = 46042; // Err: Json string Err
																	return u16errCode;
																}
															}

															// Dx (inflexion)
															if (i == 3) { // inflexion
																if (calculation.HasMember("Dx")) {
																	if (calculation["Dx"].IsFloat()) {
																		float dx = calculation["Dx"].GetFloat();
																		ptemp->CalcConf.Inflexion_dx = dx;
																	}
																	else if (calculation["Dx"].IsInt()) {
																		int dxInt = calculation["Dx"].GetInt();
																		float dx = static_cast<float>(dxInt);
																		ptemp->CalcConf.Inflexion_dx = dx;
																	}
																	else {
																		u16errCode = 46043; // Err: Json string Err
																		return u16errCode;
																	}
																}
															}

														}
														else
														{
															ptemp->CalcConf.aRange[i].Max = 0;
															ptemp->CalcConf.aRange[i].Max = 0;
															//std::cout << "Calculation " << i << ":  visibility false" << std::endl;
														}
														//Max
													}
													else
													{
														//std::cout << "Calculation " << i << ": No visibility information" << std::endl;
														//ptemp->CalcConf.aRange[i].Max = 0;
														//ptemp->CalcConf.aRange[i].Max = 0;
														////std::cout << "Calculation " << i << ": No visibility information" << std::endl;

														u16errCode = 46044;  //Err: Json string Err
														return u16errCode;

													}
												}
											}
											else
											{
												u16errCode = 46045;  //Err: Json string Err
												return u16errCode;
											}
										}
									}
									else
									{
										ptemp->CalcCount = 0;
									}

								}

								//分配内存前先判空是否释放内存
								if (eoConfs[j].ExtConf.pConfBuf != nullptr) {
									delete reinterpret_cast<TagEO_Type_Polygon*>(eoConfs[j].ExtConf.pConfBuf);
									eoConfs[j].ExtConf.pConfBuf = nullptr;
								}
								ptemp->EntryStart = EntryStart;
								ptemp->EntryEnd = EntryStop;
								ptemp->ExitStart = ExitStart;
								ptemp->ExitEnd = ExitStop;
								ptemp->Contains = bContain;

								eoConfs[j].ExtConf.pConfBuf = reinterpret_cast<short*>(ptemp);

								break;
							}
							case eEnvelope:
							{
								TagEO_Type_Envelope* pEnvelope = new TagEO_Type_Envelope;
								int pointIndex = 0;

								if (TempEoObject.HasMember("EoContent") && TempEoObject["EoContent"].IsObject())
								{
									const Value& eoContent = TempEoObject["EoContent"];

									// XMax
									if (eoContent.HasMember("XMax") && eoContent["XMax"].IsObject()) {
										const Value& xMax = eoContent["XMax"];
										if (xMax.HasMember("Value")) {
											if (xMax["Value"].IsFloat()) {
												pEnvelope->XMax = xMax["Value"].GetFloat();
											}
											else if (xMax["Value"].IsInt()) {
												int xMaxInt = xMax["Value"].GetInt();
												pEnvelope->XMax = static_cast<float>(xMaxInt);
											}
											else {
												pEnvelope->XMax = 0; // 默认值
											}
										}
										else {
											pEnvelope->XMax = 0; // 默认值
										}
									}
									else {
										pEnvelope->XMax = 0; // 默认值
									}

									// XMin
									if (eoContent.HasMember("XMin") && eoContent["XMin"].IsObject()) {
										const Value& xMin = eoContent["XMin"];
										if (xMin.HasMember("Value")) {
											if (xMin["Value"].IsFloat()) {
												pEnvelope->Xmin = xMin["Value"].GetFloat();
											}
											else if (xMin["Value"].IsInt()) {
												int xMinInt = xMin["Value"].GetInt();
												pEnvelope->Xmin = static_cast<float>(xMinInt);
											}
											else {
												pEnvelope->Xmin = 0; // 默认值
											}
										}
										else {
											pEnvelope->Xmin = 0; // 默认值
										}
									}
									else {
										pEnvelope->Xmin = 0; // 默认值
									}


									//iEntryExitType
									if (eoContent.HasMember("EntryExitType") && eoContent["EntryExitType"].IsInt())
									{
										pEnvelope->iEntryExitType = eoContent["EntryExitType"].GetInt();
									}

									//LowerPoints数组
									if (eoContent.HasMember("LowerPoints") && eoContent["LowerPoints"].IsArray())
									{
										const Value& lowerPoints = eoContent["LowerPoints"];
										pEnvelope->iLowerPointsCount = lowerPoints.Size();

										// 先存储aLowerPoint的第一个点
										if (pEnvelope->iLowerPointsCount > 0)
										{
											pEnvelope->aLowerPoint[0].X = lowerPoints[0]["X"].GetFloat();
											pEnvelope->aLowerPoint[0].Y = lowerPoints[0]["Y"].GetFloat();

											pEnvelope->aEnvelopePoint[pointIndex].X = pEnvelope->aLowerPoint[0].X;
											pEnvelope->aEnvelopePoint[pointIndex].Y = pEnvelope->aLowerPoint[0].Y;
											++pointIndex;
										}

										//存储剩余的LowerPoints到aLowerPoint数组中
										for (SizeType k = 1; k < lowerPoints.Size() && k < Max_EnvelopePointsCounts; ++k)
										{
											pEnvelope->aLowerPoint[k].X = lowerPoints[k]["X"].GetFloat();
											pEnvelope->aLowerPoint[k].Y = lowerPoints[k]["Y"].GetFloat();
										}
									}

									//UpperPoints
									if (eoContent.HasMember("UpperPoints") && eoContent["UpperPoints"].IsArray())
									{
										const Value& upperPoints = eoContent["UpperPoints"];
										pEnvelope->iUpperPointsCount = upperPoints.Size();

										//正序存储UpperPoints到aEnvelopePoint数组中
										for (SizeType k = 0; k < upperPoints.Size() && k < Max_EnvelopePointsCounts; ++k)
										{
											pEnvelope->aUpperPoint[k].X = upperPoints[k]["X"].GetFloat();
											pEnvelope->aUpperPoint[k].Y = upperPoints[k]["Y"].GetFloat();

											pEnvelope->aEnvelopePoint[pointIndex].X = pEnvelope->aUpperPoint[k].X;
											pEnvelope->aEnvelopePoint[pointIndex].Y = pEnvelope->aUpperPoint[k].Y;
											++pointIndex;
										}
									}

									// 将aLowerPoint除第一个点外逆序存储到aEnvelopePoint中
									for (int k = pEnvelope->iLowerPointsCount - 1; k > 0; --k)
									{
										pEnvelope->aEnvelopePoint[pointIndex].X = pEnvelope->aLowerPoint[k].X;
										pEnvelope->aEnvelopePoint[pointIndex].Y = pEnvelope->aLowerPoint[k].Y;
										++pointIndex;
									}

									//计算并赋值iEnvelopePointsCount
									pEnvelope->iEnvelopePointsCount = pointIndex;

									//需要给ResultItem的最值求范围  Ymin_Y,Ymax_Y
									float Ymin = pEnvelope->aEnvelopePoint[0].Y;
									float Ymax = pEnvelope->aEnvelopePoint[0].Y;

									for (int i = 1; i < pEnvelope->iEnvelopePointsCount; ++i)
									{
										if (pEnvelope->aEnvelopePoint[i].Y < Ymin) {
											Ymin = pEnvelope->aEnvelopePoint[i].Y;
										}
										if (pEnvelope->aEnvelopePoint[i].Y > Ymax) {
											Ymax = pEnvelope->aEnvelopePoint[i].Y;
										}
									}
									pEnvelope->Ymin = Ymin;
									pEnvelope->Ymax = Ymax;
								}

								//Result  array
								if (TempEoObject.HasMember("Results") && TempEoObject["Results"].IsArray() && !TempEoObject["Results"].IsNull())
								{
									const Value& aResult = TempEoObject["Results"];
									if (aResult.IsArray() && !aResult.IsNull())
									{
										for (SizeType i = 0; i < aResult.Size(); i++)
										{
											const Value& result = aResult[i];

											//check the key of "IsVisiable"  //Qnext
											if (result.IsObject() && result.HasMember("IsVisiable") && result["IsVisiable"].IsBool())
											{
												bool isVisible = result["IsVisiable"].GetBool();
												eoConfs[j].Result.EoResult.ResultItem[i].bActive = isVisible;
												if (isVisible)
												{
													//对下字符串
													if (result.HasMember("Name") && result["Name"].IsString())
													{
														const char* name = result["Name"].GetString();
														if (i < Max_FieldBusNameCount)
														{
															if (strcmp(name, aFieldbusName[i]) == 0)
															{

																//std::cout << "ResultItem " << i << ": Name=aFieldbus" << ", Name=" << name << std::endl;
																//Qnext "FieldBus"字段
															}
															else
															{
																u16errCode = 46039;  //Err: Json string Err
																return u16errCode;
															}
														}
													}
												}
											}
											else
											{
												u16errCode = 46041;  //Err: Json string Err
												return u16errCode;
											}
										}
									}
								}


								//分配内存前先判空是否释放内存
								if (eoConfs[j].ExtConf.pConfBuf != nullptr) {
									delete reinterpret_cast<TagEO_Type_Envelope*>(eoConfs[j].ExtConf.pConfBuf);
									eoConfs[j].ExtConf.pConfBuf = nullptr;
								}
								eoConfs[j].ExtConf.pConfBuf = reinterpret_cast<short*>(pEnvelope);

								break;
							}
							default:
								break;
							}

						}
					}
				}
				else
				{
					u16errCode = 46048;  //Err: Json string invalid
					return u16errCode;

				}

				if (evaluation.HasMember("FieldbusLinkerVisible") && evaluation["FieldbusLinkerVisible"].IsBool())
				{
					;
					//std::cout << "FieldbusLinkerVisible: " << evaluation["FieldbusLinkerVisible"].GetBool() << std::endl;
				}
				else
				{
					u16errCode = 46049;  //Err: Json string invalid
					return u16errCode;
				}

				// XHysteresis
				if (evaluation.HasMember("XHysteresis")) {
					if (evaluation["XHysteresis"].IsFloat()) {
						PubPara.LocalxHy = evaluation["XHysteresis"].GetFloat();
					}
					else if (evaluation["XHysteresis"].IsInt()) {
						PubPara.LocalxHy = static_cast<float>(evaluation["XHysteresis"].GetInt());
					}
				}

				// YHysteresis
				if (evaluation.HasMember("YHysteresis")) {
					if (evaluation["YHysteresis"].IsFloat()) {
						PubPara.LocalyHy = evaluation["YHysteresis"].GetFloat();
					}
					else if (evaluation["YHysteresis"].IsInt()) {
						PubPara.LocalyHy = static_cast<float>(evaluation["YHysteresis"].GetInt());
					}
				}

				// CurveTruncationVisible
				if (evaluation.HasMember("CurveTruncationVisible")) {
					if (evaluation["CurveTruncationVisible"].IsBool()) {
						bCurveTruncationVisible = evaluation["CurveTruncationVisible"].GetBool();
					}
					// std::cout << "CurveTruncationVisible:" << bCurveTruncationVisible << std::endl;
				}


			}
			else
			{
				u16errCode = 46052;  //Err: Json string invalid
				return u16errCode;
			}
		}
	}
	EoInit();
	return 0;
}

/**
* @name  EO_Init
* @brief Init All EO
* @param
* @retuen ErrCode(unsigned int)
* @Areas External call
*/
ErrorCode EoInit()
{
	unsigned int ErrCode = 0;
	for (unsigned int IndexEO = 0; IndexEO < Max_EO_Count - 1; IndexEO++)
	{
		memset(&(eoConfs[IndexEO].AttributeVar), 0, sizeof(TagEO_AttributeVar));
	}
	if (EoCount > 0)
	{

		for (unsigned int i = 0; i < EoCount; i++)
		{
			switch (eoConfs[i].EOType)
			{
			case eLine:
				ErrCode = EO_Line_Init(&(eoConfs[i]));
				break;
			case ePolygon:
				ErrCode = EO_Polygon_Init(&(eoConfs[i]));
				break;
			case eEnvelope:
				ErrCode = EO_Envelope_Init(&(eoConfs[i]));
				break;
			default:
				break;
			}
		}
	}
	return ErrCode;
}


/**
* @name  EO_Reset
* @brief Reset EO Para
* @param
* @retuen ErrCode(unsigned int)
* @Areas External calls
*/
void EoReset()
{
	bTunnelStop = false;
	bTrigerSeqStop = false;
	EoCalcFinish = false;
	//ref reset
	//LFormState = NoEnter_NoExit;  //如果是多曲线这个状态可能会有问题 Q?
	if (EoCount > 0)
	{
		for (unsigned char i = 0; i < EoCount; i++)
		{
			//	memset(&(eoConfs[i].Result), 0, sizeof(TagEO_Result));
			eoConfs[i].CalcAssistVar.LastPointCount = 0;
			eoConfs[i].CalcAssistVar.CurPointCount = 0;
			eoConfs[i].CalcAssistVar.Tunnel_CurPointCount = 0;
			eoConfs[i].CalcAssistVar.Tunnel_LastPointCount = 0;
			eoConfs[i].CalcAssistVar.FormState = NoEnter_NoExit;
			eoConfs[i].CalcAssistVar.bLineEnterIntersected = false;
			eoConfs[i].CalcAssistVar.bLineExitIntersected = false;
			eoConfs[i].CalcAssistVar.bLineRepeatIntersected = false;
			eoConfs[i].CalcAssistVar.bOutsideHyPolygon = false;

			for (short index = 0; index < PolygonResultNames.size(); index++)
			{
				eoConfs[i].Result.EoResult.sErrorCode = "NoErr";
				//	eoConfs[i].Result.EoResult.ResultItem[index].MaxRange = DefaultValue;
				//	eoConfs[i].Result.EoResult.ResultItem[index].MinRange = DefaultValue;
				eoConfs[i].Result.EoResult.ResultItem[index].RealValue = DefaultValue;
			}
			eoConfs[i].Result.EoResult.EoPass = true;
			switch (eoConfs[i].EOType)
			{
			case eLine:
				EO_Line_ResetPara();
				break;
			case ePolygon:
				EO_Polygon_ResetPara(&eoConfs[i]);
				break;
			case eEnvelope:
				EO_Envelope_ResetPara(&eoConfs[i]);
				break;
			default:
				break;
			}
		}
	}
	//dbgPrint(1, 0, "\n\nReset  EO  Para\n");
}

/**
* @name  EO_CurveResult
* @brief Calculate the CurveResult
* @param pDataBuf PointCount
* @retuen
* @Areas
*/
void EO_CurveResult(Tag3FPoint* pDataBuf, unsigned long int iPointCount)
{
	unsigned int CurPointCount;
	Tag3FPoint* pPoint;
	float PyDataTotalTime = 0, PyDataTotalX = 0, PyDataTotalForce = 0;

	if (iPointCount >= 0 && iPointCount < CURVE_MAX_POINTS_NUMBER)
	{
		CurPointCount = iPointCount;
	}
	else
	{
		CurPointCount = CURVE_MAX_POINTS_NUMBER;
	}

	if (CurPointCount > 0)
	{
		for (unsigned int i = 0; i < CurPointCount; i++)
		{
			pPoint = pDataBuf + i;
			if (i == 0)
			{
				CurResult.tMaxPoint = *pPoint;
				CurResult.XminPoint = *pPoint;
				CurResult.XmaxPoint = *pPoint;
				CurResult.YminPoint = *pPoint;
				CurResult.YmaxPoint = *pPoint;

				CurResult.Xmin_X = 0;
				CurResult.Xmin_Y = 0;
				CurResult.Xmax_X = 0;
				CurResult.Xmax_Y = 0;
				CurResult.Ymin_X = 0;
				CurResult.Ymin_Y = 0;
				CurResult.Ymax_X = 0;
				CurResult.Ymax_Y = 0;
				CurResult.Tmax_X = 0;
				CurResult.Tmax_Y = 0;
				CurResult.Tmax_T = 0;

				CurResult.PeakPeakX = 0;
				CurResult.PeakPeakY = 0;
				TurncationPointIndex = 0;  //20250422 add 曲线截断
			}
			else
			{
				PyDataTotalTime = PyDataTotalTime + pPoint->T;
				PyDataTotalX = PyDataTotalX + pPoint->X;
				PyDataTotalForce = PyDataTotalForce + pPoint->Y;

				//Tmax
				if (pPoint->T > CurResult.tMaxPoint.T)
				{
					CurResult.tMaxPoint = *pPoint;
				}

				//Xmax
				if (pPoint->X > CurResult.XmaxPoint.X)
				{
					CurResult.XmaxPoint = *pPoint;
				}
				//Xmin
				if (pPoint->X < CurResult.XminPoint.X)
				{
					CurResult.XminPoint = *pPoint;
				}
				//ForceMax
				if (pPoint->Y > CurResult.YmaxPoint.Y)
				{
					TurncationPointIndex = i;
					CurResult.YmaxPoint = *pPoint;
				}
				//ForceMin
				if (pPoint->Y < CurResult.YminPoint.Y)
				{
					CurResult.YminPoint = *pPoint;
				}
			}
		}

		CurResult.PeakPeakX = CurResult.XmaxPoint.X - CurResult.XminPoint.X;
		CurResult.PeakPeakY = CurResult.YmaxPoint.Y - CurResult.YminPoint.Y;
		CurResult.Xmin_X = CurResult.XminPoint.X;
		CurResult.Xmin_Y = CurResult.XminPoint.Y;
		CurResult.Xmax_X = CurResult.XmaxPoint.X;
		CurResult.Xmax_Y = CurResult.XmaxPoint.Y;
		CurResult.Ymin_X = CurResult.YminPoint.X;
		CurResult.Ymin_Y = CurResult.YminPoint.Y;
		CurResult.Ymax_X = CurResult.YmaxPoint.X;
		CurResult.Ymax_Y = CurResult.YmaxPoint.Y;
		CurResult.Tmax_X = CurResult.tMaxPoint.X;
		CurResult.Tmax_Y = CurResult.tMaxPoint.Y;
		CurResult.Tmax_T = CurResult.tMaxPoint.T;
	}
	else
	{
		//ERROR
	}
}

/**
* @name  EO_Init
* @brief Init All EO
* @param pDataBuf PointCount SeqFinish ReStartEoCalc
* @retuen ErrCode(unsigned int)
* @Areas External calls
*/
ErrorCode EoMain(Tag3FPoint* pDataBuf, uint32 PointCount, bool* pbSeqFinish, bool ReStartEoCalc, bool* EoCalcFinished, bool* pbCraftIsOK, char** pResultBuffer, uint64* pResultLength)
{
	unsigned int ErrCode = 0;
	bool bResult = true;
	if (ReStartEoCalc)
	{
		bTrigerSeqStop = false;
		ReStartEoCalc = false;
		bTunnelStop = false;
	}
	//Q_Nest EoToRef  坐标系变换

	if (SYSTEM_MOVE_SERIES)
	{
		if (EoCount > 0)
		{
			if (!EoCalcFinish)
			{
				bTrigerSeqStop = bTunnelStop;
				for (unsigned char i = 0; i < EoCount; i++)
				{
					if (eoConfs[i].CalcAssistVar.LastPointCount == 0)
					{
						eoConfs[j].Result.EoResult.EoPass = true;
						bTrigerSeqStop = false;
						ErrCode = 0;
					}
					eoConfs[i].CalcAssistVar.CurPointCount = PointCount;
					eoConfs[i].CalcAssistVar.bEoToRef = true;    //参考系的暂时OK Q?
					//printf("EOMain##    EoIndex= %d  PointCount: %d  bSeqFinish: %d    \n", i, PointCount, *pbSeqFinish);
					//EoIsOK
					if (eoConfs[i].CalcAssistVar.CurPointCount > 0 && eoConfs[i].CalcAssistVar.CurPointCount < CURVE_MAX_POINTS_NUMBER && eoConfs[i].CalcAssistVar.bEoToRef)
					{
						switch (eoConfs[i].EOType)
						{
						case eLine:
							EO_Line_IsOk(&(eoConfs[i]), pDataBuf, *pbSeqFinish);
							break;
						case ePolygon:
							EO_Polygon_IsOK(&(eoConfs[i]), pDataBuf, *pbSeqFinish);
							break;
						case eEnvelope:
							EO_Envelope_IsOK(&(eoConfs[i]), pDataBuf, *pbSeqFinish);
							break;
						default:
							break;
						}
						eoConfs[i].CalcAssistVar.LastPointCount = PointCount;
					}

					if (*pbSeqFinish)
					{
						if (!eoConfs[i].CalcAssistVar.bEval)
						{
							eoConfs[i].CalcAssistVar.bEval = true;
						}
						EoCalcFinish = true;
					}
				}
			}
		}
		else
		{
			EoCalcFinish = true;//return 0x001;  //无评估框
		}
	}
	else
	{
		EoCalcFinish = true;
	}

	if (*pbSeqFinish)
	{
		if (SYSTEM_MOVE_SERIES)
		{
			if (EoCount > 0)
			{
				for (unsigned char j = 0; j < EoCount; j++)
				{
					if (!eoConfs[j].CalcAssistVar.bEval)
					{
						EoCalcFinish = false;
						break;
					}
				}
			}
		}
		else
		{

		}
	}
	else
	{
		EoCalcFinish = false;
	}

	if (EoCalcFinish)
	{
		//CurveResult
		EO_CurveResult(pDataBuf, PointCount);

		//将 CurveResult 和 ResultBuff 合并起来
		//ResultData();//这些变量是直接放进类里面呢，还是用这个参数传递进去呢？  Q？
		//EoResultToFieldusData();
		bResult = true;
		*EoCalcFinished = EoCalcFinish;
		if (SYSTEM_MOVE_SERIES)
		{
			if (EoCount > 0)
			{
				for (unsigned char j = 0; j < EoCount; j++)
				{
					if (eoConfs[j].CalcAssistVar.bEval)
					{
						if (!eoConfs[j].Result.EoResult.EoPass || bTunnelStop)  //如果EoPass NG + bTunnelStop触发
						{
							if (eoConfs[j].ConfData_Base.bInvolveEval)
							{
								bResult = false;
								break;
							}
						}
					}
				}
			}
		}
		else
		{
			if (sLimitEo.bActive)
			{

				bResult = true;
				if (sLimitEo.fLimitForce >= 0)
				{
					if (CurResult.Ymax_Y > sLimitEo.fLimitForce)
						bResult = false;
				}
				else
				{
					if (CurResult.Ymin_Y < sLimitEo.fLimitForce)
						bResult = false;
				}

				if (sLimitEo.fLimitPos >= 0)
				{
					if (CurResult.Xmax_X > sLimitEo.fLimitPos)
						bResult = false;
				}
				else
				{
					if (CurResult.Xmin_X < sLimitEo.fLimitPos)
						bResult = false;
				}
			}
			else
			{
				bResult = true;
			}
		}

		if (pbCraftIsOK != NULL)
		{
			*pbCraftIsOK = bResult;
		}

		ResultData(pResultBuffer, pResultLength, pbCraftIsOK);
		//std::cout << *pResultBuffer << std::endl;
		//pResultData = pResultJsonData;
	}


	return ErrCode;
}


/**
* @name  EO_Line_Init
* @brief Init EO_Line
* @param pEO_Data
* @retuen ErrCode(unsigned int)
*/
unsigned int EO_Line_Init(TagEO_Data* pEO_Data)
{
	unsigned int ErrCode;
	float xHy = 0, yHy = 0;
	Tag2FPoint CenterPoint;
	pConf_Line = (TagEO_Type_Line*)pEO_Data->ExtConf.pConfBuf;
	pEO_Data->AttributeVar.EoIndex = pEO_Data->EoMap;
	pEO_Data->CalcAssistVar.EnterPoint.X = DefaultValue;
	pEO_Data->CalcAssistVar.EnterPoint.Y = DefaultValue;
	pEO_Data->CalcAssistVar.bDirection = false;

	if (pConf_Line != 0)
	{
		pEO_Data->AttributeVar.bNoPass[0] = false;
		pEO_Data->AttributeVar.bNoPass[1] = false;
		pEO_Data->AttributeVar.bAnyPass[0] = false;
		pEO_Data->AttributeVar.bAnyPass[1] = false;
		ErrCode = 0;
	}
	else
	{
		return 0xFFEE;   //指针为空
	}
	if (pEO_Data->ConfData_Base.UseGlobalHy)//glocal Hy
	{
		xHy = PubPara.LocalxHy;
		yHy = PubPara.LocalyHy;
	}
	else
	{
		xHy = pEO_Data->ConfData_Base.xHysteresis;
		yHy = pEO_Data->ConfData_Base.yHysteresis;
	}

	if (xHy == 0 && yHy == 0) //无迟滞
	{
		pEO_Data->AttributeVar.HyModelPoints[0] = pConf_Line->Point[0];
		pEO_Data->AttributeVar.HyModelPoints[1] = pConf_Line->Point[1];
	}
	else
	{
		CenterPoint.X = (pConf_Line->Point[0].X + pConf_Line->Point[1].X) / 2;
		CenterPoint.Y = (pConf_Line->Point[0].Y + pConf_Line->Point[1].Y) / 2;

		if (pConf_Line->Point[0].X == pConf_Line->Point[1].X)//竖线
		{
			LineHyPoint[0].X = pConf_Line->Point[0].X - xHy;
			LineHyPoint[0].Y = pConf_Line->Point[0].Y;
			LineHyPoint[1].X = pConf_Line->Point[1].X - xHy;
			LineHyPoint[1].Y = pConf_Line->Point[1].Y;

			LineHyPoint[2].X = pConf_Line->Point[1].X + xHy;
			LineHyPoint[2].Y = pConf_Line->Point[1].Y;
			LineHyPoint[3].X = pConf_Line->Point[0].X + xHy;
			LineHyPoint[3].Y = pConf_Line->Point[0].Y;
		}
		else if (pConf_Line->Point[0].Y == pConf_Line->Point[1].Y)  //横
		{
			LineHyPoint[1].X = pConf_Line->Point[0].X;
			LineHyPoint[1].Y = pConf_Line->Point[0].Y + yHy;
			LineHyPoint[2].X = pConf_Line->Point[1].X;
			LineHyPoint[2].Y = pConf_Line->Point[1].Y + yHy;

			LineHyPoint[0].X = pConf_Line->Point[0].X;
			LineHyPoint[0].Y = pConf_Line->Point[0].Y - yHy;
			LineHyPoint[3].X = pConf_Line->Point[1].X;
			LineHyPoint[3].Y = pConf_Line->Point[1].Y - yHy;
		}
		else
		{
			if (bPrintFlag)
			{
				printf("The Line RealModelPoints is invalid!\n");
			}
		}
		pEO_Data->AttributeVar.HyPointsCount = 4;
		for (int j = 0; j < 4; j++)  //0、1、2、3
		{
			pEO_Data->AttributeVar.HyModelPoints[j].X = LineHyPoint[j].X;
			pEO_Data->AttributeVar.HyModelPoints[j].Y = LineHyPoint[j].Y;
		}
	}
	pEO_Data->AttributeVar.PointsCount = 2;
	pEO_Data->AttributeVar.RealModelPoints[0] = pConf_Line->Point[0];
	pEO_Data->AttributeVar.RealModelPoints[1] = pConf_Line->Point[1];

	//当前Line的穿入穿出方向 AnyPass NoPass判断
	if (pConf_Line->EntryEnd - pConf_Line->EntryStart == 1)
	{
		//P1-->P0
		pEO_Data->AttributeVar.EnterStart = 0;
		pEO_Data->AttributeVar.EnterEnd = 1;
		pEO_Data->CalcAssistVar.bDirection = true;
	}
	else if (pConf_Line->EntryStart - pConf_Line->EntryEnd == 1)
	{
		//P0-->P1
		pEO_Data->AttributeVar.EnterStart = 1;
		pEO_Data->AttributeVar.EnterEnd = 0;
		pEO_Data->CalcAssistVar.bDirection = false;//反向
	}
	//else if (((pConf_Line->EntryStart==32767)&& (pConf_Line->EntryEnd==0))||((pConf_Line->EntryEnd == 32767) && (pConf_Line->EntryStart == 0)))
	else if (pConf_Line->EntryStart + pConf_Line->EntryEnd == 32767)
	{
		pEO_Data->AttributeVar.bAnyPass[0] = true;
		pEO_Data->AttributeVar.EnterStart = 0;
		pEO_Data->AttributeVar.EnterEnd = 1;
	}
	else if (pConf_Line->EntryStart + pConf_Line->EntryEnd == -32768)   //这个张工又改了
	{
		pEO_Data->AttributeVar.bNoPass[0] = true;
		pEO_Data->AttributeVar.EnterStart = 1;
		pEO_Data->AttributeVar.EnterEnd = 0;
	}
	return 0;

	//ResultItem 的初值赋值
}

/**
* @name  EO_Line_IsOK
* @brief pCurve:the Curve Point Buffer
* @param pEO_Data
* @retuen IsOK
*/
bool EO_Line_IsOk(TagEO_Data* pEO_Data, Tag3FPoint* pCurve, bool SeqFinished)
{
	bool bIsOK = false;
	Tag3FPoint* pCurPoint;
	Tag3FPoint* pLastPoint;
	unsigned int IndexPoint = 0;
	Tag2FPoint aLineRealPoint[2];
	Tag2FPoint Point2F1, Point2F2;
	//bool bRelation = false, bLastRelation = false;
	bool bDir, bLastDir; //点和直线的关系
	bool bRelation; //直线和直线的关系
	bool bPoint_HyRelation = false;  //点和迟滞框的关系
	//Tag2FPoint IntersectionPoint;
	//曾经在迟滞内并出迟滞
	//bool bHadInOutHy = false;
	bool bAllowRepeatEntry = false;

	//rt_dbgPrint(1, 0, "EO_Line_IsOK## ####### IndexPoint:%d  FormState:%d \n", IndexPoint, pEO_Data->CalcAssistVar.FormState);
	//rt_dbgPrint(1, 0, "EO_Line_IsOk CurPointCount:%d      ,LastPointCount: %d \n", pEO_Data->CalcAssistVar.LastPointCount, pEO_Data->CalcAssistVar.CurPointCount);
	pConf_Line = (TagEO_Type_Line*)pEO_Data->ExtConf.pConfBuf;
	pEO_Data->AttributeVar.EoIndex = pEO_Data->EoMap;
	if (pEO_Data->CalcAssistVar.CurPointCount > 0 && pEO_Data->CalcAssistVar.CurPointCount < CURVE_MAX_POINTS_NUMBER)
	{
		if (pEO_Data->CalcAssistVar.CurPointCount >= pEO_Data->CalcAssistVar.LastPointCount)
		{
			for (unsigned int IndexPoint = pEO_Data->CalcAssistVar.LastPointCount; IndexPoint < pEO_Data->CalcAssistVar.CurPointCount; IndexPoint++)
			{
				//rt_dbgPrint(1, 0, "EO_Line_IsOK##  IndexPoint:%d  FormState:%d \n", IndexPoint, pEO_Data->CalcAssistVar.FormState);
				pCurPoint = pCurve + IndexPoint;
				if (IndexPoint == 0)
				{
					pLastPoint = pCurPoint;
					pEO_Data->CalcAssistVar.FormState = NoEnter_NoExit;
				}
				else
				{
					pLastPoint = pCurve + (IndexPoint - 1);
					Point2F1.X = pCurPoint->X;
					Point2F1.Y = pCurPoint->Y;
					Point2F2.X = pLastPoint->X;
					Point2F2.Y = pLastPoint->Y;

					//得确保pCurPoint在当前Line范围内
					bDir = Geometry_Point_Line(pCurPoint, &(pEO_Data->AttributeVar.RealModelPoints[0]), &(pEO_Data->AttributeVar.RealModelPoints[1]));
					bLastDir = Geometry_Point_Line(pLastPoint, &(pEO_Data->AttributeVar.RealModelPoints[0]), &(pEO_Data->AttributeVar.RealModelPoints[1]));
					// 添加投影检测
					bool validCrossing = IsValidCrossing(pCurPoint, pLastPoint,
						&(pEO_Data->AttributeVar.RealModelPoints[0]),
						&(pEO_Data->AttributeVar.RealModelPoints[1]));

					switch (pEO_Data->CalcAssistVar.FormState)
					{
						//1.没有找到穿入
						//前后两点对Line的关系发生变化，如果坐标在Line的范围内则必有穿入行为发生。这里做范围约束可以减少一点点计算量
					case NoEnter_NoExit: {
						if (bDir != bLastDir && validCrossing)  //前后两个点和直线的方向发生改变
						{
							bRelation = Geometry_Line_Line(&(pEO_Data->AttributeVar.RealModelPoints[0]), &(pEO_Data->AttributeVar.RealModelPoints[1]), &(Point2F1), &(Point2F2), &(pEO_Data->CalcAssistVar.EnterPoint));
							if (bRelation) //发生相交，判断相交方向
							{
								if (pEO_Data->AttributeVar.bNoPass[0])
								{
									pEO_Data->Result.EoResult.sErrorCode = "Line_Entry1";
									pEO_Data->CalcAssistVar.FormState = EoError;
								}
								else if (pEO_Data->AttributeVar.bAnyPass[0])
								{
									pEO_Data->CalcAssistVar.FormState = Entered_Exited;
								}
								else
								{
									if (pEO_Data->AttributeVar.RealModelPoints[1].X == pEO_Data->AttributeVar.RealModelPoints[0].X) //竖
									{
										//左边穿入右
										if (pEO_Data->CalcAssistVar.bDirection)
										{
											if (Point2F1.X > Point2F2.X)
											{
												//正确穿入
												pEO_Data->CalcAssistVar.FormState = Entered_Exited;
												//rt_dbgPrint(1, 0, "LineIsOK##  EnterPoint.X:%f \n      EnterPoint.Y: %f   \n", pEO_Data->CalcAssistVar.EnterPoint.X, pEO_Data->CalcAssistVar.EnterPoint.Y);
											}
											else
											{
												pEO_Data->Result.EoResult.sErrorCode = "Line_Entry2";
												pEO_Data->CalcAssistVar.FormState = EoError;
											}
										}
										else
										{
											if (Point2F1.X < Point2F2.X)
											{
												//正确穿入
												pEO_Data->CalcAssistVar.FormState = Entered_Exited;
												//	rt_dbgPrint(1, 0, "LineIsOK##  EnterPoint.X:%f \n      EnterPoint.Y: %f   \n", pEO_Data->CalcAssistVar.EnterPoint.X, pEO_Data->CalcAssistVar.EnterPoint.Y);
											}
											else
											{
												pEO_Data->Result.EoResult.sErrorCode = "Line_Entry3";
												pEO_Data->CalcAssistVar.FormState = EoError;
											}
										}
									}
									else if (pEO_Data->AttributeVar.RealModelPoints[1].Y == pEO_Data->AttributeVar.RealModelPoints[0].Y) //横
									{
										//下边穿入上
										if (pEO_Data->CalcAssistVar.bDirection)
										{
											if (Point2F1.Y > Point2F2.Y)
											{
												//正确穿入
												pEO_Data->CalcAssistVar.FormState = Entered_Exited;
											}
											else
											{
												pEO_Data->Result.EoResult.sErrorCode = "Line_Entry4";
												pEO_Data->CalcAssistVar.FormState = EoError;
											}
										}
										else
										{
											if (Point2F1.Y < Point2F2.Y)
											{
												//正确穿入
												pEO_Data->CalcAssistVar.FormState = Entered_Exited;
											}
											else
											{
												pEO_Data->Result.EoResult.sErrorCode = "Line_Entry5";
												pEO_Data->CalcAssistVar.FormState = EoError;
											}
										}
									}
								}
							}
						}
						else  //未发生穿入行为
						{
							;
						}
					}break;
					case Entered_NoExit: {
						//迟滞内穿回
						//判断是否出迟滞框
						bPoint_HyRelation = Geometry_Point_Polygon(pCurPoint, &(pEO_Data->AttributeVar.HyModelPoints[0]), pEO_Data->AttributeVar.HyPointsCount);
						if (bPoint_HyRelation != true)
						{
							pEO_Data->CalcAssistVar.bOutsideHyPolygon = true;
							//迟滞框内重复穿入后出迟滞框
							pEO_Data->Result.EoResult.sErrorCode = "Line_OutOfHy";
							pEO_Data->CalcAssistVar.FormState = EoError;
						}
						else
						{
							if (bDir != bLastDir && validCrossing) //迟滞内再次穿出
							{
								bRelation = Geometry_Line_Line(&(pEO_Data->AttributeVar.RealModelPoints[0]), &(pEO_Data->AttributeVar.RealModelPoints[1]), &(Point2F1), &(Point2F2), &(pEO_Data->CalcAssistVar.ExitPoint));
								if (bRelation) //发生相交，判断相交方向
								{
									if (pEO_Data->AttributeVar.bNoPass[0] || pEO_Data->CalcAssistVar.bOutsideHyPolygon)
									{
										pEO_Data->Result.EoResult.sErrorCode = "Line_Entry6";
										pEO_Data->CalcAssistVar.FormState = EoError;
									}
									else
									{
										pEO_Data->CalcAssistVar.FormState = Entered_Exited;  //迟滞下的穿回
										break;
									}
								}
							}
							else
							{
								;
							}
						}
					}break;
					case Entered_Exited: {
						//记录首次穿入点坐标
						if (pEO_Data->Result.EoResult.ResultItem[0].RealValue == DefaultValue) {
							pEO_Data->Result.EoResult.ResultItem[0].isPass = true;
							pEO_Data->Result.EoResult.ResultItem[0].RealValue = pEO_Data->CalcAssistVar.EnterPoint.X;
							pEO_Data->Result.EoResult.ResultItem[1].isPass = true;
							pEO_Data->Result.EoResult.ResultItem[1].RealValue = pEO_Data->CalcAssistVar.EnterPoint.Y;
							//rt_dbgPrint(1, 0, "LineIsOK#Entered_Exited  ResultItem EnterPoint.X:%f \n      EnterPoint.Y: %f   \n", pEO_Data->Result.EoResult.ResultItem[0].RealValue, pEO_Data->Result.EoResult.ResultItem[1].RealValue);
							//rt_dbgPrint(1, 0, "EO_Line_IsOK##EOIndex:%d     [0]bActive: %d    [1]bActive: %d \n", pEO_Data->EoMap, pEO_Data->Result.EoResult.ResultItem[0].bActive, pEO_Data->Result.EoResult.ResultItem[1].bActive);
						}

						//是否有迟滞
						//应该再加上是否忽略重复穿入穿出
						if (bAllowRepeatEntry)
						{
							pEO_Data->CalcAssistVar.FormState = EoPass;
							pEO_Data->CalcAssistVar.bEval = true;
							break;;  //退出case
						}
						//判断点和迟滞框的关系
						bPoint_HyRelation = Geometry_Point_Polygon(pCurPoint, &(pEO_Data->AttributeVar.HyModelPoints[0]), pEO_Data->AttributeVar.HyPointsCount);
						if (bPoint_HyRelation != true)
						{
							pEO_Data->CalcAssistVar.bOutsideHyPolygon = true;
							//出迟滞框
						}

						if (pEO_Data->AttributeVar.HyPointsCount == 2 || pEO_Data->CalcAssistVar.bOutsideHyPolygon)//无迟滞
						{
							if (bDir != bLastDir && validCrossing) //再次发生相交现象
							{
								pEO_Data->Result.EoResult.sErrorCode = "Line_RepeatEntry";
								pEO_Data->CalcAssistVar.FormState = EoError;  //Err:这个应该是RepeatEntry
								break;
							}
						}
						else if ((pEO_Data->AttributeVar.HyPointsCount == 4) && !pEO_Data->CalcAssistVar.bOutsideHyPolygon) //含迟滞
						{
							if (bDir != bLastDir && validCrossing) //迟滞内穿回
							{
								//是否需要判断穿入穿出方向呢？，哪条边穿入的已经没有意义了

								//先判断是否相交
								bRelation = Geometry_Line_Line(&(pEO_Data->AttributeVar.RealModelPoints[0]), &(pEO_Data->AttributeVar.RealModelPoints[1]), &(Point2F1), &(Point2F2), &(pEO_Data->CalcAssistVar.ExitPoint));
								if (bRelation) //发生相交，判断相交方向
								{
									if (pEO_Data->AttributeVar.bNoPass[0] || pEO_Data->CalcAssistVar.bOutsideHyPolygon)
									{
										pEO_Data->Result.EoResult.sErrorCode = "Line_RepeatEntry2";
										pEO_Data->CalcAssistVar.FormState = EoError;
									}
									else
									{
										pEO_Data->CalcAssistVar.FormState = Entered_NoExit;  //迟滞下的穿回
									}
								}
							}
							else
							{
								;
							}
						}
						else
						{
							//Err 迟滞点数不对
						}
					}break;
					case EoPass: {
						//遍历全部点
						if (SeqFinished)
						{
							pEO_Data->CalcAssistVar.bEval = true;
							pEO_Data->Result.EoResult.EoPass = true;
						}
					}break;

					case EoError:
						//pEO_Data->Result.EoResult.sErrorCode = "Line_Entry7";
						//	pEO_Data->Result.ErrCodeMask.ErrCodeOffset.Entry = true;
						pEO_Data->Result.EoResult.EoPass = false;
						break;

					}
				}
			}
			//遍历完Buffer内全部点
			if (SeqFinished) //在for循环里面就会遍历每个点，这里的SeqFinished标志位只是Bufferfinished的标志位跟其他的没关系，不能放在这   Qnext？
			{
				if (pEO_Data->CalcAssistVar.FormState == EoError || (!pEO_Data->AttributeVar.bNoPass[0] && pEO_Data->CalcAssistVar.FormState == NoEnter_NoExit) || pEO_Data->CalcAssistVar.FormState == Entered_NoExit)
				{
					//rt_dbgPrint(1, 0, "EO_Line_IsOK##   FormState:%d \n", pEO_Data->CalcAssistVar.FormState);
					pEO_Data->Result.EoResult.EoPass = false;
					pEO_Data->CalcAssistVar.bEval = true;
				}
				else
				{
					pEO_Data->CalcAssistVar.bEval = true;
					pEO_Data->Result.EoResult.EoPass = true;
				}

			}
		}
	}
	else
	{
		pEO_Data->Result.EoResult.sErrorCode = "LinePointCounts_invalid";
		pEO_Data->Result.EoResult.EoPass = false;
		pEO_Data->CalcAssistVar.bEval = true;
	}

	return (pEO_Data->Result.EoResult.EoPass);
}

/**
* @name  EO_Line_ResetPara
* @brief
* @param
* @retuen
*/
void EO_Line_ResetPara()
{
	for (unsigned int i = 0; i < Max_EO_Count; i++)
	{
		//Reset 
		//Q_Next
		//IsOK参数reset
		eoConfs[i].Result.EoResult.sErrorCode = "NoErr";
		if (eoConfs[i].EOType == eLine)
		{
			eoConfs[i].CalcAssistVar.EnterPoint.X = DefaultValue;
			eoConfs[i].CalcAssistVar.EnterPoint.Y = DefaultValue;
			//memset(&(eoConfs[i].Result.EoResult.ResultItem[0]), 0, 2 * sizeof(TagEoResultItem));
			eoConfs[i].Result.EoResult.ResultItem[0].MinRange = min(eoConfs[i].AttributeVar.RealModelPoints[0].X, eoConfs[i].AttributeVar.RealModelPoints[1].X);
			eoConfs[i].Result.EoResult.ResultItem[0].MaxRange = max(eoConfs[i].AttributeVar.RealModelPoints[0].X, eoConfs[i].AttributeVar.RealModelPoints[1].X);
			eoConfs[i].Result.EoResult.ResultItem[1].MinRange = min(eoConfs[i].AttributeVar.RealModelPoints[0].Y, eoConfs[i].AttributeVar.RealModelPoints[1].Y);
			eoConfs[i].Result.EoResult.ResultItem[1].MaxRange = max(eoConfs[i].AttributeVar.RealModelPoints[0].Y, eoConfs[i].AttributeVar.RealModelPoints[1].Y);

			eoConfs[i].Result.EoResult.ResultItem[0].RealValue = DefaultValue;
			eoConfs[i].Result.EoResult.ResultItem[1].RealValue = DefaultValue;

		}
	}
}

/**
* @name  AppendPoints
* @brief Read csvdata to buffer Simulation
* @param
* @retuen ErrCode(unsigned int)
*/
unsigned int AppendPoints()
{
	memset(&Buffer[0], 0, CURVE_MAX_POINTS_NUMBER * sizeof(Tag3FPoint));
	//Buffer[CURVE_MAX_POINTS_NUMBER];  //CSV读取
	PointCounts = 0;
	BufferFinished = false;

	//读取文件中的数据
	ifstream ifs;							//创建流对象

	ifs.open(csvFile, ios::in);	//打开文件

	if (!ifs.is_open())						//判断文件是否打开
	{
		cout << "Err:打开文件失败！！！";
		BufferFinished = false;
		return 0X001;
	}

	vector<string> item;		//用于存放文件中的一行数据

	string temp;				//临时存储文件中的一行数据

	while (getline(ifs, temp))  //利用 getline（）读取文件中的每一行，并放入到 item 中
	{
		item.push_back(temp);
	}

	unsigned int rowCounts = 0;

	//遍历文件中的每一行数据
	for (auto it = item.begin(); it != item.end(); it++)
	{
		rowCounts = rowCounts + 1;
		if (rowCounts > 149)   //压机CSV从150行开始是Point数据
		{

			string str;

			//其作用是把字符串分解为单词(在此处就是把一行数据分为单个数据)
			istringstream istr(*it);

			//将字符串流 istr 中的字符读入到 str 字符串中，读取方式是以逗号为分隔符 
			getline(istr, str, ',');
			//cout << str << "\t";            // str 对应第一列数据
			//atof(str.c_str())该函数将字符转化为 float 数据            
			Buffer[PointCounts].T = atof(str.c_str());

			getline(istr, str, ',');
			//cout << str << "\t";            // str 对应第二列数据 
			Buffer[PointCounts].X = atof(str.c_str());

			getline(istr, str, ',');
			//cout << str << "\t" << endl;            // str 对应第三列数据   
			Buffer[PointCounts].Y = atof(str.c_str());

			PointCounts = PointCounts + 1;
		}
	}
	BufferFinished = true;

	//system("pause");
	return 0;
}

/**
* @name  EO_Polygon_Init
* @brief Init Polygon Para
* @param pEO_Data
* @retuen ErrCode(unsigned int)
*/
unsigned int EO_Polygon_Init(TagEO_Data* pEO_Data)
{
	unsigned int ErrCode = 0;
	float xHy = 0, yHy = 0, SumX = 0, SumY = 0;
	Tag2FPoint CenterPoint;

	pEO_Data->AttributeVar.EoIndex = (unsigned int)pEO_Data->EoMap;
	pPolygon = (TagEO_Type_Polygon*)pEO_Data->ExtConf.pConfBuf;
	if ((pPolygon == 0) || (pPolygon->PointsCount == 0))
	{
		return 0xFFEE;   //指针为空
	}

	if (pEO_Data->ConfData_Base.UseGlobalHy == 0) //local Hy
	{
		xHy = pEO_Data->ConfData_Base.xHysteresis;
		yHy = pEO_Data->ConfData_Base.yHysteresis;
	}
	else
	{
		//全局迟滞，
		xHy = PubPara.LocalxHy;
		yHy = PubPara.LocalyHy;
	}

	pEO_Data->AttributeVar.PointsCount = pPolygon->PointsCount;
	pEO_Data->AttributeVar.HyPointsCount = pPolygon->PointsCount;
	CenterPoint.X = 0;
	CenterPoint.Y = 0;

	//1. Calculate CenterPoint
	if (pPolygon->PointsCount > 0)
	{
		for (unsigned int i = 0; i < pPolygon->PointsCount; i++)
		{
			CenterPoint.X = CenterPoint.X + pPolygon->aRectanglePoints[i].X / pPolygon->PointsCount;
			CenterPoint.Y = CenterPoint.Y + pPolygon->aRectanglePoints[i].Y / pPolygon->PointsCount;
		}
	}
	else
	{
		return 0XFFFE; //invalid Parameter
	}

	//2. Calc   RealModelPoint/HyModelPoints
	if (pEO_Data->AttributeVar.PointsCount > 0)
	{
		for (unsigned int j = 0; j < pEO_Data->AttributeVar.PointsCount; j++)
		{
			pEO_Data->AttributeVar.RealModelPoints[j] = pPolygon->aRectanglePoints[j];

			if ((pPolygon->aRectanglePoints[j].X > CenterPoint.X) && (pPolygon->aRectanglePoints[j].Y > CenterPoint.Y))  //1
			{
				pEO_Data->AttributeVar.HyModelPoints[j].X = pPolygon->aRectanglePoints[j].X + xHy;
				pEO_Data->AttributeVar.HyModelPoints[j].Y = pPolygon->aRectanglePoints[j].Y + yHy;
			}
			else if ((pPolygon->aRectanglePoints[j].X < CenterPoint.X) && (pPolygon->aRectanglePoints[j].Y > CenterPoint.Y)) //2
			{
				pEO_Data->AttributeVar.HyModelPoints[j].X = pPolygon->aRectanglePoints[j].X - xHy;
				pEO_Data->AttributeVar.HyModelPoints[j].Y = pPolygon->aRectanglePoints[j].Y + yHy;
			}
			else if ((pPolygon->aRectanglePoints[j].X < CenterPoint.X) && (pPolygon->aRectanglePoints[j].Y < CenterPoint.Y)) //3
			{
				pEO_Data->AttributeVar.HyModelPoints[j].X = pPolygon->aRectanglePoints[j].X - xHy;
				pEO_Data->AttributeVar.HyModelPoints[j].Y = pPolygon->aRectanglePoints[j].Y - yHy;
			}
			else if ((pPolygon->aRectanglePoints[j].X > CenterPoint.X) && (pPolygon->aRectanglePoints[j].Y < CenterPoint.Y))//4
			{
				pEO_Data->AttributeVar.HyModelPoints[j].X = pPolygon->aRectanglePoints[j].X + xHy;
				pEO_Data->AttributeVar.HyModelPoints[j].Y = pPolygon->aRectanglePoints[j].Y - yHy;
			}

			//
			pEO_Data->AttributeVar.RefHyModelPoints[j] = pEO_Data->AttributeVar.HyModelPoints[j];
			pEO_Data->AttributeVar.RefRealModelPoints[j] = pEO_Data->AttributeVar.RealModelPoints[j];

		}
	}

	//3.Tunnel Calc
	if (pPolygon->TunnelCount > 0)
	{
		//printf("###   New   !!!\n");
		Tag2FPoint* points = pEO_Data->AttributeVar.RealModelPoints;
		TagPolygon_TunnelConf* tunnelVars = pEO_Data->AttributeVar.TunnelVar;

		for (unsigned int i = 0; i < pPolygon->TunnelCount; i++)
		{
			int startIdx = pPolygon->TunnelGroup[i].TunnelStartPoint;
			int endIdx = pPolygon->TunnelGroup[i].TunnelEndPoint;

			float startX = points[startIdx].X;
			float startY = points[startIdx].Y;
			float endX = points[endIdx].X;
			float endY = points[endIdx].Y;
			bool isVerticalBoundary = ((startIdx == 0 && endIdx == 1) || (startIdx == 2 && endIdx == 3));  //这个需要和上位机约定一下，start和End的排序是否一致
			if (isVerticalBoundary) {
				tunnelVars[i].LimitEdgMin = min(startY, endY);
				tunnelVars[i].LimitEdgMax = max(startY, endY);
			}
			else {
				tunnelVars[i].LimitEdgMin = min(startX, endX);
				tunnelVars[i].LimitEdgMax = max(startX, endX);
			}

			if (startX != endX) {
				// 非垂直线 - 包括倾斜线和水平线(斜率为0)
				float slope = (endY - startY) / (endX - startX);
				tunnelVars[i].EdgeLine_a = slope;
				tunnelVars[i].EdgeLine_b = endY - slope * endX;
			}
			else {
				// 垂直线
				tunnelVars[i].EdgeLine_a = DefaultValue;
				tunnelVars[i].EdgeLine_b = startX;
			}
		}
	}

	//Any NoPass
	if ((pPolygon->EntryStart == -32768) && (pPolygon->EntryEnd == 0))
	{
		pEO_Data->AttributeVar.bNoPass[0] = true;
	}
	else if ((pPolygon->EntryStart == 0) && (pPolygon->EntryEnd == 32767))
	{
		pEO_Data->AttributeVar.bAnyPass[0] = true;
	}
	else
	{
		pEO_Data->AttributeVar.EnterStart = pPolygon->EntryStart;
		pEO_Data->AttributeVar.EnterEnd = pPolygon->EntryEnd;
	}

	if ((pPolygon->ExitStart == -32768) && (pPolygon->ExitEnd == 0))
	{
		pEO_Data->AttributeVar.bNoPass[1] = true;
	}
	else if ((pPolygon->ExitStart == 0) && (pPolygon->ExitEnd == 32767))
	{
		pEO_Data->AttributeVar.bAnyPass[1] = true;
	}
	else
	{
		pEO_Data->AttributeVar.ExitStart = pPolygon->ExitStart;
		pEO_Data->AttributeVar.ExitEnd = pPolygon->ExitEnd;
	}

	if ((pPolygon->EntryStart == -32768) && (pPolygon->EntryEnd == 0) && (pPolygon->ExitStart == -32768) && (pPolygon->ExitEnd == 0))
	{
		if (pPolygon->Contains == 0)
		{
			pEO_Data->AttributeVar.bOutside = true;
		}
		else if (pPolygon->Contains == 1)
		{
			pEO_Data->AttributeVar.bInside = true;
		}
		pEO_Data->AttributeVar.bNoPass[0] = false;
		pEO_Data->AttributeVar.bNoPass[1] = false;
	}
	//EO pEoData->CalcAssistVar.ProcessVar[i] Range Init

	for (unsigned char index = 0; index < ResultItemLength; index++)
	{
		if (index % 2 == 1)  //index为奇数时   Y
		{
			pEO_Data->CalcAssistVar.ProcessVar[index].MaxRange = max(pEO_Data->AttributeVar.RefRealModelPoints[1].Y, pEO_Data->AttributeVar.RefRealModelPoints[2].Y);
			pEO_Data->CalcAssistVar.ProcessVar[index].MinRange = min(pEO_Data->AttributeVar.RefRealModelPoints[0].Y, pEO_Data->AttributeVar.RefRealModelPoints[3].Y);
		}
		else  //index为偶数时  X
		{
			pEO_Data->CalcAssistVar.ProcessVar[index].MaxRange = max(pEO_Data->AttributeVar.RefRealModelPoints[2].X, pEO_Data->AttributeVar.RefRealModelPoints[3].X);
			pEO_Data->CalcAssistVar.ProcessVar[index].MinRange = min(pEO_Data->AttributeVar.RefRealModelPoints[0].X, pEO_Data->AttributeVar.RefRealModelPoints[1].X);
		}
		//这边有一个PeakPeakY赋初值的时候是不对的。可能引起歧义
		pEO_Data->CalcAssistVar.ProcessVar[index].RealValue = DefaultValue;   //Init
	}
	//PeakPeak_Y
	pEO_Data->CalcAssistVar.ProcessVar[PeakPeak_Y].MaxRange = DefaultValue;//max(pEO_Data->AttributeVar.RefRealModelPoints[1].Y, pEO_Data->AttributeVar.RefRealModelPoints[2].Y);
	pEO_Data->CalcAssistVar.ProcessVar[PeakPeak_Y].MinRange = DefaultValue;


	//inflexion Calc Init




	return ErrCode;
}

/**
* @name  slope
* @brief
* @param
* @retuen double
*/
float slope(Tag3FPoint a, Tag3FPoint b)
{
	if (fabs(b.X - a.X) < EPSILON)
	{ //如果X非常接近，跳过，防止除以零
		return 0.0;
	}
	return (b.Y - a.Y) / (b.X - a.X);  //基于X计算斜率
}

/**
* @name  initBuffer
* @brief  initBuffer  //初始化环形缓冲区
* @param   buffer
* @retuen void
*/
void initBuffer(CircularBuffer* buffer)
{
	buffer->index = 0;
	buffer->count = 0;
}

/**
* @name  addPoint
* @brief  addPoint  //向缓冲区添加新点
* @param   buffer  point
* @retuen void
*/
void addPoint(CircularBuffer* buffer, Tag3FPoint point)
{
	buffer->points[buffer->index] = point;
	buffer->index = (buffer->index + 1) % BUFFER_SIZE;  //环形存储
	if (buffer->count < BUFFER_SIZE)
	{
		buffer->count++;
	}
}

/**
* @name  applyFilter
* @brief  applyFilter  // 对当前缓冲区中的点进行均值滤波，窗口大小作为参数传入
* @param   buffer currentIndex filterSize
* @retuen Tag3FPoint
*/
Tag3FPoint applyFilter(CircularBuffer* buffer, int currentIndex, int filterSize)
{
	Tag3FPoint filtered = { 0.0, 0.0, 0.0 };
	int count = 0;

	// 使用filterSize大小的窗口进行均值滤波
	for (int i = currentIndex; i > currentIndex - filterSize && i >= 0; --i)
	{
		int idx = (i + BUFFER_SIZE) % BUFFER_SIZE;
		filtered.T += buffer->points[idx].T;
		filtered.X += buffer->points[idx].X;
		filtered.Y += buffer->points[idx].Y;
		count++;
	}

	if (count > 0)
	{
		filtered.T /= count;
		filtered.X /= count;
		filtered.Y /= count;
	}

	return filtered;
}

/**
* @name  findInflectionPoint
* @brief  findInflectionPoint  // 实时查找斜率变化最大的点，并返回斜率最大值、最小值
* @param   buffer filterSize
* @retuen Tag3FPoint
*/
InflectionResult findInflectionPoint(CircularBuffer* buffer, int filterSize)
{
	int inflection_point = 0;
	float max_slope_change = 0.0;
	float max_slope = -INFINITY;
	float min_slope = INFINITY;

	//只有在有足够的点时才能计算
	if (buffer->count < 3)
	{
		InflectionResult result = { buffer->points[0], 0.0, 0.0 };  //如果点不足，返回默认值
		return result;
	}

	for (int i = 1; i < buffer->count - 1; ++i)
	{
		int prev_index = (i - 1 + BUFFER_SIZE) % BUFFER_SIZE;
		int next_index = (i + 1) % BUFFER_SIZE;

		//滤波后的点
		Tag3FPoint filteredPrev = applyFilter(buffer, prev_index, filterSize);
		Tag3FPoint filteredCur = applyFilter(buffer, i, filterSize);
		Tag3FPoint filteredNext = applyFilter(buffer, next_index, filterSize);

		//计算前后两段的斜率
		float slope1 = slope(filteredPrev, filteredCur);
		float slope2 = slope(filteredCur, filteredNext);

		//更新最大最小斜率
		if (slope1 > max_slope) max_slope = slope1;
		if (slope2 > max_slope) max_slope = slope2;
		if (slope1 < min_slope) min_slope = slope1;
		if (slope2 < min_slope) min_slope = slope2;
		float slope_change = fabs(slope2 - slope1);

		//max_slope_change
		if (slope_change > max_slope_change)
		{
			max_slope_change = slope_change;
			inflection_point = i;
		}
	}

	InflectionResult result;
	result.inflectionPoint = buffer->points[inflection_point];
	result.maxSlope = max_slope;
	result.minSlope = min_slope;
	return result;
}

/**
 * @name  ProcessVarCalc
 * @brief  Calc ProcessVar
 * @param   TagEO_Data* pEoData
 * @param   Tag3FPoint* pCurPoint
 * @return  void
 */
void ProcessVarCalc(TagEO_Data* pEoData, Tag3FPoint* pCurPoint) {
	if ((!pEoData->AttributeVar.bFinishProcess) &&
		pEoData->CalcAssistVar.FormState == Entered_NoExit) {

		bProcessCalcCount += 1;

		if (pEoData->CalcAssistVar.bFirstCalcPoint) {
			pEoData->CalcAssistVar.bFirstCalcPoint = false;
			for (unsigned char index = 0; index < ResultItemLength; index++) {
				pEoData->CalcAssistVar.ProcessVar[index].RealValue = (index % 2 == 1) ? pCurPoint->Y : pCurPoint->X;
			}
			pEoData->CalcAssistVar.ProcessVar[PeakPeak_Y].RealValue = pCurPoint->Y;

			if (pEoData->CalcAssistVar.FormState == Entered_NoExit) {
				pEoData->CalcAssistVar.ProcessVar[Entry_X].RealValue = pEoData->CalcAssistVar.EnterPoint.X;
				pEoData->CalcAssistVar.ProcessVar[Entry_Y].RealValue = pEoData->CalcAssistVar.EnterPoint.Y;
			}
		}

		if (pCurPoint->X > pEoData->CalcAssistVar.ProcessVar[XMax_X].RealValue) {
			pEoData->CalcAssistVar.ProcessVar[XMax_X].RealValue = pCurPoint->X;
			pEoData->CalcAssistVar.ProcessVar[XMax_Y].RealValue = pCurPoint->Y;
		}

		if (pCurPoint->X < pEoData->CalcAssistVar.ProcessVar[XMin_X].RealValue) {
			pEoData->CalcAssistVar.ProcessVar[XMin_X].RealValue = pCurPoint->X;
			pEoData->CalcAssistVar.ProcessVar[XMin_Y].RealValue = pCurPoint->Y;
		}

		if (pCurPoint->Y > pEoData->CalcAssistVar.ProcessVar[YMax_Y].RealValue) {
			pEoData->CalcAssistVar.ProcessVar[YMax_X].RealValue = pCurPoint->X;
			pEoData->CalcAssistVar.ProcessVar[YMax_Y].RealValue = pCurPoint->Y;
		}

		if (pCurPoint->Y < pEoData->CalcAssistVar.ProcessVar[YMin_Y].RealValue) {
			pEoData->CalcAssistVar.ProcessVar[YMin_X].RealValue = pCurPoint->X;
			pEoData->CalcAssistVar.ProcessVar[YMin_Y].RealValue = pCurPoint->Y;
		}

		if (pEoData->CalcAssistVar.FormState == Entered_Exited) {
			pEoData->CalcAssistVar.ProcessVar[EXIT_X].RealValue = pEoData->CalcAssistVar.ExitPoint.X;
			pEoData->CalcAssistVar.ProcessVar[EXIT_Y].RealValue = pEoData->CalcAssistVar.ExitPoint.Y;
		}
	}
}


/**
* @name  SetTunnelError
* @brief SetTunnelError
* @param pEO_Data errorCode
* @retuen void
*/
void SetTunnelError(TagEO_Data* pEO_Data, const char* errorCode) {
	pEO_Data->CalcAssistVar.FormState = EoError;
	pEO_Data->Result.EoResult.sErrorCode = errorCode;
	pEO_Data->CalcAssistVar.bEval = true;
	pEO_Data->Result.EoResult.EoPass = false;
	bTunnelStop = true;
}

/**
* @name  EO_TunnelJudgeSingle
* @brief TunnelJudge
* @param pEO_Data pCurve
* @retuen bTunnelStop (bool)
*/
bool EO_TunnelJudgeSingle(TagEO_Data* pEO_Data, Tag3FPoint* pCurPoint)
{
	TagEO_Type_Polygon* pPolygon = (TagEO_Type_Polygon*)pEO_Data->ExtConf.pConfBuf;

	if (pPolygon->TunnelCount < 0 || pPolygon->TunnelCount > Max_Polygon_PointsCount) {
		SetTunnelError(pEO_Data, "TunnelCounts_invalid");
		return true;
	}
	if (pPolygon->TunnelCount == 0)
	{
		return false;
	}

	TagPolygon_TunnelConf* tunnelVars = pEO_Data->AttributeVar.TunnelVar;

	for (unsigned int i = 0; i < pPolygon->TunnelCount; i++) {
		int startIdx = pPolygon->TunnelGroup[i].TunnelStartPoint;
		int endIdx = pPolygon->TunnelGroup[i].TunnelEndPoint;
		float minEdge = tunnelVars[i].LimitEdgMin;
		float maxEdge = tunnelVars[i].LimitEdgMax;
		float lineA = tunnelVars[i].EdgeLine_a;
		float lineB = tunnelVars[i].EdgeLine_b;

		bool isVerticalBoundary = ((startIdx == 0 && endIdx == 1) || (startIdx == 2 && endIdx == 3));

		if (isVerticalBoundary) {
			// 垂直边界 - 包括垂直线和倾斜线
			if (pCurPoint->Y > minEdge && pCurPoint->Y < maxEdge) {
				bool crossedBoundary = false;

				if (startIdx == 0 && endIdx == 1) {
					// 左边界
					if (lineA != DefaultValue) {
						// 倾斜线
						float edgeX = (pCurPoint->Y - lineB) / lineA;
						crossedBoundary = (pCurPoint->X <= edgeX);
					}
					else {
						// 垂直线
						crossedBoundary = (pCurPoint->X < lineB);
					}

					if (crossedBoundary) {
						SetTunnelError(pEO_Data, "TunnelStop_left");
						return true;
					}
				}
				else {
					// 右边界
					if (lineA != DefaultValue) {
						// 倾斜线
						float edgeX = (pCurPoint->Y - lineB) / lineA;
						crossedBoundary = (pCurPoint->X >= edgeX);
					}
					else {
						// 垂直线
						crossedBoundary = (pCurPoint->X > lineB);
					}

					if (crossedBoundary) {
						SetTunnelError(pEO_Data, "TunnelStop_right");
						return true;
					}
				}
			}
		}
		else {
			// 水平边界 - 包括水平线和倾斜线
			if (pCurPoint->X > minEdge && pCurPoint->X < maxEdge) {
				float edgeY = pCurPoint->X * lineA + lineB;
				bool crossedBoundary = false;

				if (startIdx == 1 && endIdx == 2) {
					// 上边界
					crossedBoundary = (pCurPoint->Y >= edgeY);

					if (crossedBoundary) {
						SetTunnelError(pEO_Data, "TunnelStop_top");
						return true;
					}
				}
				else {
					// 下边界
					crossedBoundary = (pCurPoint->Y <= edgeY);

					if (crossedBoundary) {
						SetTunnelError(pEO_Data, "TunnelStop_bottom");
						return true;
					}
				}
			}
		}
	}

	return false;
}

/**
* @name  Eo_TunnelJudge
* @brief Judge Tunnel
* @param pDataBuf 曲线点
* @param PointCount 当前周期更新到的点数
* @param pbSeqFinish  点数是否结束
* @param pTunnelJudgeFinished 返回：停止区域评估结束
* @retuen bTunnelStop (bool)  外部调用
*/
bool Eo_TunnelJudge(Tag3FPoint* pDataBuf, uint32 PointCount, bool* pbSeqFinish, bool* pTunnelJudgeFinished)
{
	bTunnelStop = false;
	if (EoCount > 0)
	{
		bool bTunnelJudgeFinished = *pTunnelJudgeFinished;
		if (!bTunnelJudgeFinished)
		{
			for (unsigned char i = 0; i < EoCount; i++)
			{
				eoConfs[i].CalcAssistVar.Tunnel_CurPointCount = PointCount;
				//printf("Eo_TunnelJudge  ##    EoIndex= %d  PointCount: %d  bSeqFinish: %d    \n", i, PointCount, *pbSeqFinish);
				if (eoConfs[i].CalcAssistVar.Tunnel_CurPointCount > 0 && eoConfs[i].CalcAssistVar.Tunnel_CurPointCount < CURVE_MAX_POINTS_NUMBER)
				{
					if (eoConfs[i].EOType == ePolygon)
					{
						TagEO_Type_Polygon* pTmpPolygon;
						pTmpPolygon = (TagEO_Type_Polygon*)eoConfs[i].ExtConf.pConfBuf;
						Tag3FPoint* pTmpCurPoint, * pTmpLastPoint;
						if (eoConfs[i].CalcAssistVar.Tunnel_CurPointCount >= eoConfs[i].CalcAssistVar.Tunnel_LastPointCount)
						{
							for (unsigned long int j = eoConfs[i].CalcAssistVar.Tunnel_LastPointCount; j < eoConfs[i].CalcAssistVar.Tunnel_CurPointCount; j++)
							{
								pTmpCurPoint = pDataBuf + j;
								if (j == 0)
								{
									pTmpLastPoint = pDataBuf;
								}
								else if (j >= 1)
								{
									pTmpLastPoint = pDataBuf + (j - 1);
								}
								//rt_dbgPrint(1, 0, "### Eo_TunnelJudge j:%d  pCurPoint.X:%f \n", j, pTmpCurPoint->X);
								//Tunnel judge 
								bTunnelStop = EO_TunnelJudgeSingle(&(eoConfs[i]), pTmpCurPoint);
								if (bTunnelStop)
								{
									//rt_dbgPrint(1, 0, "### TunnelJudge: bTunnelStop:%d  sErrorCode:%s \n", bTunnelStop, eoConfs[i].Result.EoResult.sErrorCode.c_str());
									*pTunnelJudgeFinished = true;
									return bTunnelStop;
								}
							}
						}
					}

					eoConfs[i].CalcAssistVar.Tunnel_LastPointCount = PointCount;
				}

				if (*pbSeqFinish || bTunnelStop)
				{

					*pTunnelJudgeFinished = true;
					return bTunnelStop;
				}
			}
		}
	}
	else
	{
		*pTunnelJudgeFinished = true;
		return bTunnelStop;
	}
	return bTunnelStop;
}



/**
* @name  Eo_TunnelJudge
* @brief Judge Tunnel
* @param pDataBuf 曲线点
* @param PointCount 当前周期更新到的点数
* @param pbSeqFinish  点数是否结束
* @param pTunnelJudgeFinished 返回：停止区域评估结束
* @retuen bTunnelStop (bool)  外部调用
*/
Tag3FPoint* pTmpLastTunnelPoint;
bool Eo_TunnelJudgeNew(Tag3FPoint* pCurPoint, bool* pbSeqFinish, bool* pTunnelJudgeFinished)
{
	bTunnelStop = false;
	if (EoCount > 0 && !*pbSeqFinish)
	{
		bool bTunnelJudgeFinished = *pTunnelJudgeFinished;
		if (!bTunnelJudgeFinished)
		{
			for (unsigned char i = 0; i < EoCount; i++)
			{
				//printf("Eo_TunnelJudge  ##    EoIndex= %d  PointCount: %d  bSeqFinish: %d    \n", i, PointCount, *pbSeqFinish);

				if (eoConfs[i].EOType == ePolygon)
				{
					TagEO_Type_Polygon* pTmpPolygon;
					pTmpPolygon = (TagEO_Type_Polygon*)eoConfs[i].ExtConf.pConfBuf;

					//rt_dbgPrint(1, 0, "### Eo_TunnelJudge j:%d  pCurPoint.X:%f \n", j, pTmpCurPoint->X);
					//Tunnel judge 
					bTunnelStop = EO_TunnelJudgeSingle(&(eoConfs[i]), pCurPoint);
					if (bTunnelStop)
					{
						//rt_dbgPrint(1, 0, "### TunnelJudge: bTunnelStop:%d  sErrorCode:%s \n", bTunnelStop, eoConfs[i].Result.EoResult.sErrorCode.c_str());
						*pTunnelJudgeFinished = true;
						return bTunnelStop;
					}
				}


				if (*pbSeqFinish || bTunnelStop)
				{

					*pTunnelJudgeFinished = true;
					return bTunnelStop;
				}
			}
		}
	}
	else
	{
		*pTunnelJudgeFinished = true;
		return bTunnelStop;
	}
	return bTunnelStop;
}



float RecentSpeeds[Max_EO_Count][SPEED_WINDOW_SIZE]; // 保存最近N次速度
int SpeedIndex[Max_EO_Count] = { 0 }; // 当前索引位置
bool WindowFilled[Max_EO_Count] = { false }; // 窗口是否已填满


/**
* @name  EO_Polygon_IsOK
* @brief Eval the Polygon
* @param pEO_Data pCurve SeqFinished
* @retuen IsOK (bool)
*/
CircularBuffer buffer[10];
int filterSize;
bool EO_Polygon_IsOK(TagEO_Data* pEO_Data, Tag3FPoint* pCurve, bool SeqFinished)
{
	pPolygon = (TagEO_Type_Polygon*)pEO_Data->ExtConf.pConfBuf;   //Add
	pEoData = pEO_Data;
	pCurPoint = pCurve;
	pLastPoint = pCurve;
	bSeqHadFinished = SeqFinished;
	//bTunnelStop = false;
	bEvalEnd = false;
	bool bDir, bLastDir; //点和直线的关系
	//bool bHadCrossHy = false;   //进出过迟滞
	//short HyInsectPointCount = 0;

	if (pEoData->AttributeVar.PointsCount > CURVE_MAX_POINTS_NUMBER)
	{
		if (bPrintFlag)
		{
			printf("The sum of Curve Points exceeds 50,0000!");
		}
		pEoData->CalcAssistVar.FormState = EoError;
		pEoData->Result.EoResult.sErrorCode = "PointsMoreThan_50w";
		pEoData->CalcAssistVar.bEval = true;
		pEoData->Result.EoResult.EoPass = false;
		return false;
	}

	if (pEoData->CalcAssistVar.CurPointCount >= pEoData->CalcAssistVar.LastPointCount)
	{
		for (unsigned long int j = pEoData->CalcAssistVar.LastPointCount; j < pEoData->CalcAssistVar.CurPointCount; j++)
		{
			//第一个点，j=0
			//rt_dbgPrint(1, 0, "EO_Polygon_IsOK:%d  FormState:%d \n", j ,FormState);
			pCurPoint = pCurve + j;
			if (j == 0)
			{
				pLastPoint = pCurve;
			}
			else if (j >= 1)
			{
				pLastPoint = pCurve + (j - 1);
			}
			else
			{
				//err:j<0
			}

			//eGeometryRelation Geometry_Point_Polygon(Tag3FPoint* checkPoint, Tag2FPoint* pPolygon, unsigned int PointsCount)
			//点和矩形的关系
			Relation = Geometry_Point_Polygon(pCurPoint, pEoData->AttributeVar.RefRealModelPoints, pEoData->AttributeVar.PointsCount);
			LastRelation = Geometry_Point_Polygon(pLastPoint, pEoData->AttributeVar.RefRealModelPoints, pEoData->AttributeVar.PointsCount);
			HyRelation = Geometry_Point_Polygon(pCurPoint, pEoData->AttributeVar.RefHyModelPoints, pEoData->AttributeVar.PointsCount);

			if (Relation)
			{
				pEoData->CalcAssistVar.bHadPointsInPolygon = true;
			}
			//bHadCrossHy
			if (!pEoData->CalcAssistVar.bHadCrossHy)  //在迟滞框内，并且是在穿入边反复穿出
			{
				if (!HyRelation && pEoData->CalcAssistVar.bHadPointsInPolygon)
				{
					pEoData->CalcAssistVar.bHadCrossHy = true;
				}
			}

			//rt_dbgPrint(1, 0, "EO_Polygon_IsOK FormState:%d   Relation: %d    HyRelation:%d    bHadCrossHy:%d  \n", pEoData->CalcAssistVar.FormState,Relation, HyRelation, pEoData->CalcAssistVar.bHadCrossHy);

			if (Relation != true) //不在矩形内则进行停止区域判断
			{
				//Tunnel judge 
				;
			}
			else
			{
				//Containment
				//不判断是否正确穿入，只判断是否在矩形内
				//FeatureCalc  FeatureJudge
				//if (pEoData->CalcAssistVar.FormState == Entered_NoExit)  //穿入未穿出，featureCalc 
				//rt_dbgPrint(1, 0, "EO_Polygon_IsOK Relation:%d \n", Relation);
				if (pPolygon->CalcCount >= 0 && pPolygon->CalcCount <= Max_PolygonCalcCounts)
				{
					for (unsigned int n = 0; n < pPolygon->CalcCount; n++)
					{
						//pEoData->Result.ResultData.PolygonResult.CalcCount = pPolygon->CalcCount;
						//rt_dbgPrint(1, 0, "EO_Polygon_IsOK CalcCount:%d \n", pPolygon->CalcCount);
						switch (n)
						{
						case AverageY: {
							if (pPolygon->CalcConf.aRange[n].bActive)
							{
								if (pEoData->CalcAssistVar.bFirst_Ave)  // 使用结构体内的变量
								{
									pEoData->CalcAssistVar.bFirst_Ave = false;
									pEoData->Result.EoResult.ResultItem[Average_Y].isPass = true;
									pEoData->Result.EoResult.ResultItem[Average_Y].MinRange = pPolygon->CalcConf.aRange[n].Min;
									pEoData->Result.EoResult.ResultItem[Average_Y].MaxRange = pPolygon->CalcConf.aRange[n].Max;
									pEoData->CalcAssistVar.Aver_CalcPointCount = 0;
									pEoData->CalcAssistVar.Aver_TempTotal = 0;
								}
								pEoData->Result.EoResult.ResultItem[Average_Y].bActive = true;
								pEoData->CalcAssistVar.Aver_CalcPointCount = pEoData->CalcAssistVar.Aver_CalcPointCount + 1;
								pEoData->CalcAssistVar.Aver_TempTotal = pEoData->CalcAssistVar.Aver_TempTotal + pCurPoint->Y;
								pEoData->CalcAssistVar.AverageY_Data = pEoData->CalcAssistVar.Aver_TempTotal / pEoData->CalcAssistVar.Aver_CalcPointCount;

								pEoData->Result.EoResult.ResultItem[Average_Y].RealValue = pEoData->CalcAssistVar.AverageY_Data;
							}
							else
							{
								pEoData->Result.EoResult.ResultItem[Average_Y].bActive = false;
							}
						}break;
						case Time_: {
							if (pPolygon->CalcConf.aRange[n].bActive)
							{
								pEoData->Result.EoResult.ResultItem[MinTime].bActive = true;
								pEoData->Result.EoResult.ResultItem[MaxTime].bActive = true;
								if (pEoData->CalcAssistVar.bFirst_Time)
								{
									pEoData->CalcAssistVar.bFirst_Time = false;

									//MinTime
									pEoData->Result.EoResult.ResultItem[MinTime].isPass = true;
									pEoData->Result.EoResult.ResultItem[MinTime].MinRange = pPolygon->CalcConf.aRange[n].Min;
									pEoData->Result.EoResult.ResultItem[MinTime].MaxRange = pPolygon->CalcConf.aRange[n].Max;
									pEoData->Result.EoResult.ResultItem[MinTime].RealValue = pCurPoint->T;
									//MaxTime
									pEoData->Result.EoResult.ResultItem[MaxTime].isPass = true;
									pEoData->Result.EoResult.ResultItem[MaxTime].MinRange = pPolygon->CalcConf.aRange[n].Min;
									pEoData->Result.EoResult.ResultItem[MaxTime].MaxRange = pPolygon->CalcConf.aRange[n].Max;
									pEoData->Result.EoResult.ResultItem[MaxTime].RealValue = pCurPoint->T;
								}
								if (pCurPoint->T < pEoData->Result.EoResult.ResultItem[MinTime].RealValue)
								{
									pEoData->Result.EoResult.ResultItem[MinTime].RealValue = pCurPoint->T;   //Min
								}
								if (pCurPoint->T > pEoData->Result.EoResult.ResultItem[MaxTime].RealValue)
								{
									pEoData->Result.EoResult.ResultItem[MaxTime].RealValue = pCurPoint->T; //Max
								}
							}
							else
							{
								pEoData->Result.EoResult.ResultItem[MinTime].bActive = false;
								pEoData->Result.EoResult.ResultItem[MaxTime].bActive = false;
							}
						}break;
						case Speed: {
							if (pPolygon->CalcConf.aRange[n].bActive)
							{
								if (pEoData->CalcAssistVar.bFirst_Speed)
								{
									pEoData->CalcAssistVar.bFirst_Speed = false;
									pEoData->CalcAssistVar.StartX = pCurPoint->X;
									pEoData->CalcAssistVar.StartTime = pCurPoint->T;

									//MinSpeed
									pEoData->Result.EoResult.ResultItem[MinSpeed].isPass = true;
									pEoData->Result.EoResult.ResultItem[MinSpeed].MinRange = pPolygon->CalcConf.aRange[n].Min;
									pEoData->Result.EoResult.ResultItem[MinSpeed].MaxRange = pPolygon->CalcConf.aRange[n].Max;
									pEoData->Result.EoResult.ResultItem[MinSpeed].RealValue = 250.0;
									//MaxSpeed
									pEoData->Result.EoResult.ResultItem[MaxSpeed].isPass = true;
									pEoData->Result.EoResult.ResultItem[MaxSpeed].MinRange = pPolygon->CalcConf.aRange[n].Min;
									pEoData->Result.EoResult.ResultItem[MaxSpeed].MaxRange = pPolygon->CalcConf.aRange[n].Max;
									pEoData->Result.EoResult.ResultItem[MaxSpeed].RealValue = pEoData->CalcAssistVar.Speed_Data;

									pEoData->Result.EoResult.ResultItem[MinSpeed].bActive = true;
									pEoData->Result.EoResult.ResultItem[MaxSpeed].bActive = true;
								}
								else
								{
									float currentSpeed = (pCurPoint->X - pEoData->CalcAssistVar.StartX) /
										(pCurPoint->T - pEoData->CalcAssistVar.StartTime);

									// 更新起点值                  
									pEoData->CalcAssistVar.StartX = pCurPoint->X;
									pEoData->CalcAssistVar.StartTime = pCurPoint->T;

									// 保存到窗口
									pEoData->CalcAssistVar.RecentSpeeds[pEoData->CalcAssistVar.SpeedIndex] = currentSpeed;
									pEoData->CalcAssistVar.SpeedIndex = (pEoData->CalcAssistVar.SpeedIndex + 1) % SPEED_WINDOW_SIZE;

									if (pEoData->CalcAssistVar.SpeedIndex == 0) {
										pEoData->CalcAssistVar.WindowFilled = true;
									}

									// 计算平均速度
									float sum = 0.0f;
									int count = pEoData->CalcAssistVar.WindowFilled ? SPEED_WINDOW_SIZE : pEoData->CalcAssistVar.SpeedIndex;

									for (int i = 0; i < count; i++) {
										sum += pEoData->CalcAssistVar.RecentSpeeds[i];
									}

									pEoData->CalcAssistVar.Speed_Data = (count > 0) ? (sum / count) : currentSpeed;

									// 更新最大最小值
									if (pEoData->CalcAssistVar.Speed_Data < pEoData->Result.EoResult.ResultItem[MinSpeed].RealValue) {
										pEoData->Result.EoResult.ResultItem[MinSpeed].RealValue = pEoData->CalcAssistVar.Speed_Data;
									}
									if (pEoData->CalcAssistVar.Speed_Data > pEoData->Result.EoResult.ResultItem[MaxSpeed].RealValue) {
										pEoData->Result.EoResult.ResultItem[MaxSpeed].RealValue = pEoData->CalcAssistVar.Speed_Data;
									}
								}
							}
							else
							{
								pEoData->Result.EoResult.ResultItem[MinSpeed].bActive = false;
								pEoData->Result.EoResult.ResultItem[MaxSpeed].bActive = false;
							}
						}break;
						case Inflexion: {
							if (pPolygon->CalcConf.aRange[n].bActive)
							{
								pEoData->Result.EoResult.ResultItem[MinInflexion].bActive = true;
								pEoData->Result.EoResult.ResultItem[MaxInflexion].bActive = true;
								pEoData->Result.EoResult.ResultItem[InflexCoord_X].bActive = true;
								pEoData->Result.EoResult.ResultItem[InflexCoord_Y].bActive = true;

								if (pEoData->CalcAssistVar.bFirst_Inflexion)
								{
									pEoData->CalcAssistVar.bFirst_Inflexion = false;

									//初始化缓存区
									initBuffer(&(pEoData->CalcAssistVar.buffer));

									//MinInflexion
									pEoData->Result.EoResult.ResultItem[MinInflexion].isPass = true;
									pEoData->Result.EoResult.ResultItem[MinInflexion].MinRange = pPolygon->CalcConf.aRange[n].Min;
									pEoData->Result.EoResult.ResultItem[MinInflexion].MaxRange = pPolygon->CalcConf.aRange[n].Max;
									pEoData->Result.EoResult.ResultItem[MinInflexion].RealValue = 0;
									//MaxInflexion
									pEoData->Result.EoResult.ResultItem[MaxInflexion].isPass = true;
									pEoData->Result.EoResult.ResultItem[MaxInflexion].MinRange = pPolygon->CalcConf.aRange[n].Min;
									pEoData->Result.EoResult.ResultItem[MaxInflexion].MaxRange = pPolygon->CalcConf.aRange[n].Max;
									pEoData->Result.EoResult.ResultItem[MaxInflexion].RealValue = 0;
									//InflexCoord_X
									pEoData->Result.EoResult.ResultItem[InflexCoord_X].isPass = true;
									pEoData->Result.EoResult.ResultItem[InflexCoord_X].MinRange = min(pEO_Data->AttributeVar.RefRealModelPoints[0].X, pEO_Data->AttributeVar.RefRealModelPoints[1].X);
									pEoData->Result.EoResult.ResultItem[InflexCoord_X].MaxRange = max(pEO_Data->AttributeVar.RefRealModelPoints[2].X, pEO_Data->AttributeVar.RefRealModelPoints[3].X);
									pEoData->Result.EoResult.ResultItem[InflexCoord_X].RealValue = pCurPoint->X;
									//InflexCoord_Y
									pEoData->Result.EoResult.ResultItem[InflexCoord_Y].isPass = true;
									pEoData->Result.EoResult.ResultItem[InflexCoord_Y].MinRange = min(pEO_Data->AttributeVar.RefRealModelPoints[0].Y, pEO_Data->AttributeVar.RefRealModelPoints[3].Y);
									pEoData->Result.EoResult.ResultItem[InflexCoord_Y].MaxRange = max(pEO_Data->AttributeVar.RefRealModelPoints[1].Y, pEO_Data->AttributeVar.RefRealModelPoints[2].Y);
									pEoData->Result.EoResult.ResultItem[InflexCoord_Y].RealValue = pCurPoint->Y;
								}

								if (pPolygon->CalcCount > 0)
								{
									if (pPolygon->CalcConf.aRange[Inflexion].bActive)
									{
										filterSize = (int)(pPolygon->CalcConf.Inflexion_dx * Sys_Resolution + 4);
										if (filterSize < 4)
										{
											filterSize = 4;
										}
										else if (filterSize > 200)
										{
											filterSize = 200;
										}
									}
								}

								addPoint(&(pEoData->CalcAssistVar.buffer), *pCurPoint);
								InflectionResult result = findInflectionPoint(&(pEoData->CalcAssistVar.buffer), filterSize);
								pEoData->Result.EoResult.ResultItem[MaxInflexion].RealValue = result.maxSlope;
								pEoData->Result.EoResult.ResultItem[MinInflexion].RealValue = result.minSlope;
								pEoData->Result.EoResult.ResultItem[InflexCoord_X].RealValue = result.inflectionPoint.X;
								pEoData->Result.EoResult.ResultItem[InflexCoord_Y].RealValue = result.inflectionPoint.Y;
							}
							else
							{
								pEoData->Result.EoResult.ResultItem[MinInflexion].bActive = false;
								pEoData->Result.EoResult.ResultItem[MaxInflexion].bActive = false;
								pEoData->Result.EoResult.ResultItem[InflexCoord_X].bActive = false;
								pEoData->Result.EoResult.ResultItem[InflexCoord_Y].bActive = false;
							}
						}break;
						}
					}
				}
				else
				{
					pEoData->CalcAssistVar.FormState = EoError;
					pEoData->Result.EoResult.sErrorCode = "PolygonCalcCount_invalid";
					pEoData->CalcAssistVar.bEval = true;
					pEoData->Result.EoResult.EoPass = false;
					return false;  //error :TunnelCounts invalid
				}
			}

			//FormJudge
			if (!bEvalEnd)
			{
				if (j == 0)
				{
					//第一个点
					pLastPoint = pCurPoint;
					//LastForm = NoEnter_NoExit;
					if (pEoData->AttributeVar.bNoPass[0]) //无穿入，我的NoPass和Anypass是要当作输入，还是判断条件?
					{
						pEoData->CalcAssistVar.FormState = Entered_NoExit;
					}
					else
					{
						pEoData->CalcAssistVar.FormState = NoEnter_NoExit;
					}
				}
				else if (j < CURVE_MAX_POINTS_NUMBER)
				{
					pLastPoint = pCurve + j - 1;
					Point2F1.X = pCurPoint->X;
					Point2F1.Y = pCurPoint->Y;
					Point2F2.X = pLastPoint->X;
					Point2F2.Y = pLastPoint->Y;
					//FindEntryExitLine 

					if ((pEoData->AttributeVar.bOutside) || (pEoData->AttributeVar.bInside))
					{
						if (pEoData->AttributeVar.bInside)
						{
							if (Relation != true)
							{
								pEoData->CalcAssistVar.FormState = EoError;  //Inside：曲线全部包含在Polygon内；Err:存在不包含在Polygon内的点
								//NotAllInside
								pEoData->Result.EoResult.sErrorCode = "NotAllInside";
								pEoData->CalcAssistVar.bEval = true;
								pEoData->Result.EoResult.EoPass = false;
							}
							else
							{
								pEoData->CalcAssistVar.FormState = Entered_NoExit;
							}
						}
						else if ((pEoData->AttributeVar.bOutside))
						{
							if (Relation != true)
							{
								pEoData->CalcAssistVar.FormState = NoEnter_NoExit;
							}
							else
							{
								pEoData->CalcAssistVar.FormState = EoError;
								pEoData->Result.EoResult.sErrorCode = "NotAllOutside"; //Outside：曲线全部包含在Polygon内；Err:存在包含在Polygon内的点
								pEoData->CalcAssistVar.bEval = true;
								pEoData->Result.EoResult.EoPass = false;
							}
						}
					}
					switch (pEoData->CalcAssistVar.FormState)
					{
					case NoEnter_NoExit: {
						//无穿入无穿出
						//判断是否发生穿入穿出行为
						//是否要放在这里面判断相交是否正确
						//关于AnyPass的判断
						if (LastRelation == false && Relation == true)//穿入
						{
							//判断相交是否正确
							if (pEoData->AttributeVar.PointsCount > 0)
							{
								for (unsigned int i = 0; i < pEoData->AttributeVar.PointsCount; i++)
								{
									unsigned int t = 0;
									if (i != pEoData->AttributeVar.PointsCount - 1)
									{
										t = i + 1;
										//bLineEnterIntersected = Geometry_Line_Line(&(pEoData->AttributeVar.RefRealModelPoints[i]), &(pEoData->AttributeVar.RefRealModelPoints[i + 1]), &(Point2F1), &(Point2F2), &(pEoData->CalcAssistVar.EnterPoint));   //True为相交
									}
									else
									{
										t = 0;
									}
									bDir = Geometry_Point_Line(pCurPoint, &(pEoData->AttributeVar.RealModelPoints[i]), &(pEoData->AttributeVar.RealModelPoints[t]));
									bLastDir = Geometry_Point_Line(pLastPoint, &(pEoData->AttributeVar.RealModelPoints[i]), &(pEoData->AttributeVar.RealModelPoints[t]));
									if (bDir != bLastDir)  //前后两个点和直线的方向发生改变
									{
										//发生相交
										pEoData->CalcAssistVar.bLineEnterIntersected = Geometry_Line_Line(&(pEoData->AttributeVar.RefRealModelPoints[i]), &(pEoData->AttributeVar.RefRealModelPoints[t]), &(Point2F1), &(Point2F2), &(pEoData->CalcAssistVar.TempEnterPoint));   //True为相交
									}
									else
									{
										pEoData->CalcAssistVar.bLineEnterIntersected = false;
									}

									if (pEoData->CalcAssistVar.bLineEnterIntersected)//发生相交
									{
										pEoData->CalcAssistVar.bLineEnterIntersected = false;
										if (pEoData->CalcAssistVar.bHadCrossHy)
										{
											pEoData->CalcAssistVar.FormState = EoError;  //出迟滞后反复穿入
											pEoData->Result.EoResult.sErrorCode = "OutHyRepeatEnter";  //穿入方向错误
											pEoData->CalcAssistVar.bEval = true;
											pEoData->Result.EoResult.EoPass = false;
										}   //判断是否和正确边相交
										else if (((i == pEoData->AttributeVar.EnterStart) && (t == pEoData->AttributeVar.EnterEnd) || (t == pEoData->AttributeVar.EnterStart) && (i == pEoData->AttributeVar.EnterEnd)) && (!pEoData->AttributeVar.bNoPass[0]))  //这个相交边需要用个变量再给赋值
										{

											if (pEoData->CalcAssistVar.EnterPoint.X == DefaultValue)
											{
												pEoData->CalcAssistVar.EnterPoint = pEoData->CalcAssistVar.TempEnterPoint;  //穿入正确则赋EnterPoint值，记录第一次穿入的点为EnterPoint
												pEoData->CalcAssistVar.FormState = Entered_NoExit;
											}
											else
											{
												//pEoData->CalcAssistVar.EnterPoint.X ！= DefaultValue
												//是否为迟滞框内的正确边穿入
												//如果是迟滞的情况，记录一下点
												if ((HyRelation && !pEoData->CalcAssistVar.bHadCrossHy) || pEoData->CalcAssistVar.bAllowRepeatEntryExit)
												{
													//记录迟滞内交点
													if (pEoData->CalcAssistVar.HyInsectPointCount < MaxHyInsectPoint && pEoData->CalcAssistVar.HyInsectPointCount>0)
													{
														pEoData->CalcAssistVar.aHyIntersectPoint[pEoData->CalcAssistVar.HyInsectPointCount].bDirection = true;  //穿入
														pEoData->CalcAssistVar.aHyIntersectPoint[pEoData->CalcAssistVar.HyInsectPointCount].HyInterSectPoint = *pCurPoint; //记录真实点，当前点跟上一个构成穿入
													}
													pEoData->CalcAssistVar.HyInsectPointCount = pEoData->CalcAssistVar.HyInsectPointCount + 1;
													pEoData->CalcAssistVar.FormState = Entered_NoExit;
												}
												else
												{
													pEoData->CalcAssistVar.FormState = EoError;
													pEoData->Result.EoResult.sErrorCode = "EnterOutHy";  //穿出方向错误
													pEoData->CalcAssistVar.bEval = true;
													pEoData->Result.EoResult.EoPass = false;
												}
											}
											i = pEoData->AttributeVar.PointsCount;
											break;

										}
										else if (pEoData->AttributeVar.bAnyPass[0] && pEoData->CalcAssistVar.EnterPoint.X == DefaultValue)//任意穿入，直接跳到找穿出状态
										{
											//AnyPass首次穿入赋值
											pEoData->AttributeVar.EnterStart = i;
											pEoData->AttributeVar.EnterEnd = t;
											pEoData->CalcAssistVar.EnterPoint = pEoData->CalcAssistVar.TempEnterPoint;

											pEoData->CalcAssistVar.FormState = Entered_NoExit;
											i = pEoData->AttributeVar.PointsCount;
											break;
										}
										else   //包含 bNoPass[0]
										{
											pEoData->CalcAssistVar.FormState = EoError;
											pEoData->Result.EoResult.sErrorCode = "Entry_1";  //穿入方向错误
											pEoData->CalcAssistVar.bEval = true;
											pEoData->Result.EoResult.EoPass = false;
											break;
										}

									}
								}
							}
							else
							{
								// Err the PointsCount is invalid
							}

						}
						else if ((LastRelation == true && Relation == false))//穿出
						{
							pEoData->CalcAssistVar.FormState = EoError;
							pEoData->Result.EoResult.sErrorCode = "Entry_2";  //穿入方向错误
							pEoData->CalcAssistVar.bEval = true;
							pEoData->Result.EoResult.EoPass = false;
							break;
						}
						else if (Relation == true)  //一开始就在框内，这一段也应该Qnext
						{
							if (pEoData->AttributeVar.bNoPass[0]) //无穿入，我的NoPass和Anypass是要当作输入，还是判断条件?0x000001c12acf4070
							{
								pEoData->CalcAssistVar.FormState = Entered_NoExit;
							}
							else
							{
								pEoData->CalcAssistVar.FormState = NoEnter_NoExit;
							}
						}
					}break;
									   //已经找到穿入，正在找穿出； 
									   //这个过程中，迟滞能否不另出分支
					case Entered_NoExit: {
						if ((LastRelation == false && Relation == true))//穿入
						{
							pEoData->CalcAssistVar.FormState = EoError;  //穿入又穿入， 如果迟滞，Entered_NoExit这个状态不发生变更的话，就会出现穿入又穿出，这个逻辑需要理一下  
							pEoData->Result.EoResult.sErrorCode = "Entry_3";  //穿入方向错误
							pEoData->CalcAssistVar.bEval = true;
							pEoData->Result.EoResult.EoPass = false;
							break;
						}
						else if ((LastRelation == true && Relation == false) || pEoData->AttributeVar.bAnyPass[1])//穿出
						{
							if (pEoData->AttributeVar.PointsCount > 0)
							{
								for (unsigned int i = 0; i < pEoData->AttributeVar.PointsCount; i++)
								{
									unsigned int t = 0;
									if (i != pEoData->AttributeVar.PointsCount - 1)
									{
										t = i + 1;
										//	bLineEnterIntersected = Geometry_Line_Line(&(pEoData->AttributeVar.RefRealModelPoints[i]), &(pEoData->AttributeVar.RefRealModelPoints[i + 1]), &(Point2F1), &(Point2F2), &(pEoData->CalcAssistVar.EnterPoint));   //True为相交
									}
									else
									{
										t = 0;
									}
									bDir = Geometry_Point_Line(pCurPoint, &(pEoData->AttributeVar.RealModelPoints[i]), &(pEoData->AttributeVar.RealModelPoints[t]));
									bLastDir = Geometry_Point_Line(pLastPoint, &(pEoData->AttributeVar.RealModelPoints[i]), &(pEoData->AttributeVar.RealModelPoints[t]));
									if (bDir != bLastDir)  //前后两个点和直线的方向发生改变
									{
										//发生相交
										pEoData->CalcAssistVar.bLineExitIntersected = Geometry_Line_Line(&(pEoData->AttributeVar.RefRealModelPoints[i]), &(pEoData->AttributeVar.RefRealModelPoints[t]), &(Point2F1), &(Point2F2), &(pEoData->CalcAssistVar.TempExitPoint));   //True为相交
									}

									if (pEoData->CalcAssistVar.bLineExitIntersected)//发生相交
									{
										pEoData->CalcAssistVar.bLineExitIntersected = false;

										if (((i == pEoData->AttributeVar.ExitStart) && (t == pEoData->AttributeVar.ExitEnd) || (t == pEoData->AttributeVar.ExitStart) && (i == pEoData->AttributeVar.ExitEnd)) && (!pEoData->AttributeVar.bNoPass[1]))
										{
											if (pEoData->CalcAssistVar.ExitPoint.X == DefaultValue)  //未找到穿出——>真实相交边
											{
												pEoData->CalcAssistVar.ExitPoint = pEoData->CalcAssistVar.TempExitPoint;
												pEoData->CalcAssistVar.FormState = Entered_Exited;
											}
											else
											{
												if ((HyRelation && !pEoData->CalcAssistVar.bHadCrossHy) || pEoData->CalcAssistVar.bAllowRepeatEntryExit)//在迟滞框内，并且未穿出迟滞框又再次进入迟滞框
												{
													// 迟滞内的反复穿出，真实相交边==目标穿出边
													//记录迟滞内交点
													if (pEoData->CalcAssistVar.HyInsectPointCount < MaxHyInsectPoint && pEoData->CalcAssistVar.HyInsectPointCount>0)
													{
														pEO_Data->CalcAssistVar.aHyIntersectPoint[pEoData->CalcAssistVar.HyInsectPointCount].bDirection = false;  //穿入
														pEO_Data->CalcAssistVar.aHyIntersectPoint[pEoData->CalcAssistVar.HyInsectPointCount].HyInterSectPoint = *pCurPoint; //记录真实点，当前点跟上一个构成穿出
													}
													pEoData->CalcAssistVar.HyInsectPointCount = pEoData->CalcAssistVar.HyInsectPointCount + 1;
													pEoData->CalcAssistVar.FormState = Entered_Exited;
												}
												else
												{
													pEoData->CalcAssistVar.FormState = EoError;
													pEoData->Result.EoResult.sErrorCode = "ExitOutHy";  //穿出方向错误
													pEoData->CalcAssistVar.bEval = true;
													pEoData->Result.EoResult.EoPass = false;
												}
											}
											i = pEoData->AttributeVar.PointsCount;
											break;
										}
										else if (pEoData->AttributeVar.bAnyPass[1] && pEoData->CalcAssistVar.ExitPoint.X == DefaultValue)
										{
											//AnyPass首次穿出赋值
											pEoData->AttributeVar.ExitStart = i;
											pEoData->AttributeVar.ExitEnd = t;
											pEoData->CalcAssistVar.ExitPoint = pEoData->CalcAssistVar.TempExitPoint;

											pEoData->CalcAssistVar.FormState = Entered_Exited;
											i = pEoData->AttributeVar.PointsCount;
											break;
										}
										else if (((i == pEoData->AttributeVar.EnterStart) && (t == pEoData->AttributeVar.EnterEnd) || (t == pEoData->AttributeVar.EnterStart) && (i == pEoData->AttributeVar.EnterEnd)) && (!pEoData->AttributeVar.bNoPass[0]))
										{
											//迟滞的情况，这里需要注意的点是，Anypass判断迟滞边的方式 Qnext
											if ((HyRelation && !pEoData->CalcAssistVar.bHadCrossHy) || pEoData->CalcAssistVar.bAllowRepeatEntryExit)  //在迟滞框内，并且是在穿入边反复穿出
											{
												pEoData->CalcAssistVar.FormState = NoEnter_NoExit;
												//记录迟滞内交点
												if (pEoData->CalcAssistVar.HyInsectPointCount < MaxHyInsectPoint && pEoData->CalcAssistVar.HyInsectPointCount>0)
												{
													pEoData->CalcAssistVar.aHyIntersectPoint[pEoData->CalcAssistVar.HyInsectPointCount].bDirection = false;
													pEoData->CalcAssistVar.aHyIntersectPoint[pEoData->CalcAssistVar.HyInsectPointCount].HyInterSectPoint = *pCurPoint; //记录真实点，当前点跟上一个构成穿出
													pEoData->CalcAssistVar.bHyEntered = true;
												}

												pEoData->CalcAssistVar.HyInsectPointCount = pEoData->CalcAssistVar.HyInsectPointCount + 1;
											}
											else
											{
												pEoData->CalcAssistVar.FormState = EoError;
												pEoData->Result.EoResult.sErrorCode = "ExitOutHy_";  //穿出错误
												pEoData->CalcAssistVar.bEval = true;
												pEoData->Result.EoResult.EoPass = false;
											}
											i = pEoData->AttributeVar.PointsCount;
											break;
										}
										else  //NoPass[1]
										{
											pEoData->CalcAssistVar.FormState = EoError;
											pEoData->Result.EoResult.sErrorCode = "EXIT_";  //穿出方向错误
											pEoData->CalcAssistVar.bEval = true;
											pEoData->Result.EoResult.EoPass = false;
											break;
										}
									}
								}
							}
							else
							{
								//Err
							}
						}
						else  //Relation未发生改变
						{
							;
							//默认EoPass==true的情况下这个就可以不用做判断。
						}

					}break;
					case Entered_Exited: {
						if (LastRelation == false && Relation == false)
						{
							;
							//发生穿入穿出后一直在矩形外
						}
						else
						{
							//穿出边迟滞
							if (LastRelation == false && Relation == true && HyRelation)
							{
								if (pEoData->AttributeVar.PointsCount > 0)
								{
									for (unsigned int i = 0; i < pEoData->AttributeVar.PointsCount; i++)
									{
										unsigned int t = 0;
										if (i != pEoData->AttributeVar.PointsCount - 1)
										{
											t = i + 1;
											//	bLineEnterIntersected = Geometry_Line_Line(&(pEoData->AttributeVar.RefRealModelPoints[i]), &(pEoData->AttributeVar.RefRealModelPoints[i + 1]), &(Point2F1), &(Point2F2), &(pEoData->CalcAssistVar.EnterPoint));   //True为相交
										}
										else
										{
											t = 0;
										}
										bDir = Geometry_Point_Line(pCurPoint, &(pEoData->AttributeVar.RealModelPoints[i]), &(pEoData->AttributeVar.RealModelPoints[t]));
										bLastDir = Geometry_Point_Line(pLastPoint, &(pEoData->AttributeVar.RealModelPoints[i]), &(pEoData->AttributeVar.RealModelPoints[t]));
										//	rt_dbgPrint(1, 0, "EO_Polygon_IsOK Entered_Exited  bDir:%d   bLastDir: %d   RealModelPoints[i].x:%f   RealModelPoints[t].x:%f  \n", bDir, bLastDir, pEoData->AttributeVar.RealModelPoints[i].X, pEoData->AttributeVar.RealModelPoints[t].X);

										if (bDir != bLastDir)  //前后两个点和直线的方向发生改变
										{
											//发生相交
											pEoData->CalcAssistVar.bTempHyExit = Geometry_Line_Line(&(pEoData->AttributeVar.RefRealModelPoints[i]), &(pEoData->AttributeVar.RefRealModelPoints[t]), &(Point2F1), &(Point2F2), &(pEoData->CalcAssistVar.ExitPoint));   //True为相交
										}
										else
										{
											pEO_Data->CalcAssistVar.bTempHyExit = false;
										}

										if (pEoData->CalcAssistVar.bTempHyExit)
										{

											if (pEoData->CalcAssistVar.bHadCrossHy)//出真实框后再进入
											{
												pEoData->CalcAssistVar.FormState = EoError;  //出迟滞后反复穿入
												pEoData->Result.EoResult.sErrorCode = "OutHyRepeatEnter";  //穿入方向错误
												pEoData->CalcAssistVar.bEval = true;
												pEoData->Result.EoResult.EoPass = false;
												i = pEoData->AttributeVar.PointsCount;
												break;
											}
											if (((i == pEoData->AttributeVar.ExitStart) && (t == pEoData->AttributeVar.ExitEnd) || (t == pEoData->AttributeVar.ExitStart) && (i == pEoData->AttributeVar.ExitEnd)) && (!pEoData->AttributeVar.bNoPass[1]))
											{
												if ((HyRelation && !pEoData->CalcAssistVar.bHadCrossHy) || pEoData->CalcAssistVar.bAllowRepeatEntryExit)
												{
													pEoData->CalcAssistVar.FormState = Entered_NoExit;
													pEoData->CalcAssistVar.bHyExited = true;
													//记录迟滞内交点
													if (pEoData->CalcAssistVar.HyInsectPointCount < MaxHyInsectPoint && pEoData->CalcAssistVar.HyInsectPointCount>0)
													{
														pEoData->CalcAssistVar.aHyIntersectPoint[pEoData->CalcAssistVar.HyInsectPointCount].bDirection = true;  //穿入
														pEoData->CalcAssistVar.aHyIntersectPoint[pEoData->CalcAssistVar.HyInsectPointCount].HyInterSectPoint = *pCurPoint; //记录真实点，当前点跟上一个构成穿出
													}
													pEoData->CalcAssistVar.HyInsectPointCount = pEoData->CalcAssistVar.HyInsectPointCount + 1;
												}
												else
												{
													pEoData->CalcAssistVar.FormState = EoError;    //出迟滞后反复穿入
													pEoData->Result.EoResult.sErrorCode = "OutHyErrExitENTER";  //穿入方向错误
													pEoData->CalcAssistVar.bEval = true;
													pEoData->Result.EoResult.EoPass = false;
												}

												i = pEoData->AttributeVar.PointsCount;
												break;
											}
											else  //NoPass[1]
											{
												pEoData->CalcAssistVar.FormState = EoError;
												pEoData->Result.EoResult.sErrorCode = "OutHyRepeatEXIT_1";  //穿出方向错误
												pEoData->CalcAssistVar.bEval = true;
												pEoData->Result.EoResult.EoPass = false;
												i = pEoData->AttributeVar.PointsCount;
												break;
											}
										}
									}
								}
							}
							else
							{
								pEoData->CalcAssistVar.FormState = EoError;
								pEoData->Result.EoResult.sErrorCode = "OutHyRepeatEXIT_2";  //穿出方向错误
								pEoData->CalcAssistVar.bEval = true;
								pEoData->Result.EoResult.EoPass = false;
								break;
							}
						}
					}break;
					case EoPass: {
						pEoData->Result.EoResult.EoPass = true;
					}break;
					case EoError: {
						pEoData->Result.EoResult.EoPass = false;
						bEvalEnd = true;
					}break;
					default:
						break;
					}

					//ProcessVarCalc 
					ProcessVarCalc(pEoData, pCurPoint);
					//在EO框内

				}
				else
				{
					pEoData->CalcAssistVar.FormState = EoError;
					pEoData->Result.EoResult.sErrorCode = "PointsMoreThan_50w";
					pEoData->CalcAssistVar.bEval = true;
					pEoData->Result.EoResult.EoPass = false;
					return false;
				}
			}
		}

		//featureCalc结果判断
		if (SeqFinished)
		{
			if (pPolygon->CalcCount > 0)
			{
				for (unsigned int n = 0; n < pPolygon->CalcCount; n++)
				{
					switch (n)
					{
					case AverageY: {
						if ((pEoData->Result.EoResult.ResultItem[ResultItemLength + n].RealValue < pEoData->Result.EoResult.ResultItem[ResultItemLength + n].MinRange) ||
							(pEoData->Result.EoResult.ResultItem[ResultItemLength + n].RealValue > pEoData->Result.EoResult.ResultItem[ResultItemLength + n].MaxRange))
						{
							if (pEoData->Result.EoResult.ResultItem[ResultItemLength + n].isPass)
							{
								pEoData->Result.EoResult.ResultItem[ResultItemLength + n].isPass = false;
								pEoData->Result.EoResult.EoPass = false;
								pEoData->Result.EoResult.sErrorCode = "AverageY_Err";
								pEoData->AttributeVar.bEvaled = true;
								pEoData->CalcAssistVar.FormState = EoError;
							}
						}
					}break;
					case Time_: {
						if ((pEoData->Result.EoResult.ResultItem[ResultItemLength + n].RealValue < pEoData->Result.EoResult.ResultItem[ResultItemLength + n].MinRange) ||
							(pEoData->Result.EoResult.ResultItem[ResultItemLength + n].RealValue > pEoData->Result.EoResult.ResultItem[ResultItemLength + n].MaxRange))
						{
							if (pEoData->Result.EoResult.ResultItem[ResultItemLength + n].isPass)
							{
								pEoData->Result.EoResult.ResultItem[ResultItemLength + n].isPass = false;
								pEoData->Result.EoResult.EoPass = false;
								pEoData->Result.EoResult.sErrorCode = "TimeMin_Err";
								pEoData->AttributeVar.bEvaled = true;
								pEoData->CalcAssistVar.FormState = EoError;
							}
						}
						if ((pEoData->Result.EoResult.ResultItem[ResultItemLength + n + 1].RealValue < pEoData->Result.EoResult.ResultItem[ResultItemLength + n + 1].MinRange) ||
							(pEoData->Result.EoResult.ResultItem[ResultItemLength + n + 1].RealValue > pEoData->Result.EoResult.ResultItem[ResultItemLength + n + 1].MaxRange))
						{
							if (pEoData->Result.EoResult.ResultItem[ResultItemLength + n + 1].isPass)
							{
								pEoData->Result.EoResult.ResultItem[ResultItemLength + n + 1].isPass = false;
								pEoData->Result.EoResult.EoPass = false;
								pEoData->Result.EoResult.sErrorCode = "TimeMax_Err";
								pEoData->AttributeVar.bEvaled = true;
								pEoData->CalcAssistVar.FormState = EoError;
							}
						}
					}break;
					case Speed: {
						if ((pEoData->Result.EoResult.ResultItem[MinSpeed].RealValue < pEoData->Result.EoResult.ResultItem[MinSpeed].MinRange) ||
							(pEoData->Result.EoResult.ResultItem[MinSpeed].RealValue > pEoData->Result.EoResult.ResultItem[MinSpeed].MaxRange))
						{
							if (pEoData->Result.EoResult.ResultItem[MinSpeed].isPass)
							{
								pEoData->Result.EoResult.ResultItem[MinSpeed].isPass = false;
								pEoData->Result.EoResult.EoPass = false;
								pEoData->Result.EoResult.sErrorCode = "SpeedMin_Err";
								pEoData->AttributeVar.bEvaled = true;
								pEoData->CalcAssistVar.FormState = EoError;
							}
						}
						if ((pEoData->Result.EoResult.ResultItem[MaxSpeed].RealValue < pEoData->Result.EoResult.ResultItem[MaxSpeed].MinRange) ||
							(pEoData->Result.EoResult.ResultItem[MaxSpeed].RealValue > pEoData->Result.EoResult.ResultItem[MaxSpeed].MaxRange))
						{
							if (pEoData->Result.EoResult.ResultItem[MaxSpeed].isPass)
							{
								pEoData->Result.EoResult.ResultItem[MaxSpeed].isPass = false;
								pEoData->Result.EoResult.EoPass = false;
								pEoData->Result.EoResult.sErrorCode = "SpeedMax_Err";
								pEoData->AttributeVar.bEvaled = true;
								pEoData->CalcAssistVar.FormState = EoError;
							}
						}
					}break;
					case Inflexion: {

						if ((pEoData->Result.EoResult.ResultItem[MinInflexion].RealValue < pEoData->Result.EoResult.ResultItem[MinInflexion].MinRange) ||
							(pEoData->Result.EoResult.ResultItem[MinInflexion].RealValue > pEoData->Result.EoResult.ResultItem[MinInflexion].MaxRange))
						{
							if (pEoData->Result.EoResult.ResultItem[MinInflexion].isPass)
							{
								pEoData->Result.EoResult.ResultItem[MinInflexion].isPass = false;
								pEoData->Result.EoResult.EoPass = false;
								pEoData->Result.EoResult.sErrorCode = "MinInflexion_Err";
								pEoData->AttributeVar.bEvaled = true;
								pEoData->CalcAssistVar.FormState = EoError;
							}
						}
						if ((pEoData->Result.EoResult.ResultItem[MaxInflexion].RealValue < pEoData->Result.EoResult.ResultItem[MaxInflexion].MinRange) ||
							(pEoData->Result.EoResult.ResultItem[MaxInflexion].RealValue > pEoData->Result.EoResult.ResultItem[MaxInflexion].MaxRange))
						{
							if (pEoData->Result.EoResult.ResultItem[MaxInflexion].isPass)
							{
								pEoData->Result.EoResult.ResultItem[MaxInflexion].isPass = false;
								pEoData->Result.EoResult.EoPass = false;
								pEoData->Result.EoResult.sErrorCode = "MaxInflexion_Err";
								pEoData->AttributeVar.bEvaled = true;
								pEoData->CalcAssistVar.FormState = EoError;
							}
						}
						if ((pEoData->Result.EoResult.ResultItem[InflexCoord_X].RealValue < pEoData->Result.EoResult.ResultItem[InflexCoord_X].MinRange) ||
							(pEoData->Result.EoResult.ResultItem[InflexCoord_X].RealValue > pEoData->Result.EoResult.ResultItem[InflexCoord_X].MaxRange))
						{
							if (pEoData->Result.EoResult.ResultItem[InflexCoord_X].isPass)
							{
								pEoData->Result.EoResult.ResultItem[InflexCoord_X].isPass = false;
								pEoData->Result.EoResult.EoPass = false;
								pEoData->Result.EoResult.sErrorCode = "InflexCoordX _Err";
								pEoData->AttributeVar.bEvaled = true;
								pEoData->CalcAssistVar.FormState = EoError;
							}
						}
						if ((pEoData->Result.EoResult.ResultItem[InflexCoord_Y].RealValue < pEoData->Result.EoResult.ResultItem[InflexCoord_Y].MinRange) ||
							(pEoData->Result.EoResult.ResultItem[InflexCoord_Y].RealValue > pEoData->Result.EoResult.ResultItem[InflexCoord_Y].MaxRange))
						{
							if (pEoData->Result.EoResult.ResultItem[InflexCoord_Y].isPass)
							{
								pEoData->Result.EoResult.ResultItem[InflexCoord_Y].isPass = false;
								pEoData->Result.EoResult.EoPass = false;
								pEoData->Result.EoResult.sErrorCode = "InflexCoordY_Err";
								pEoData->AttributeVar.bEvaled = true;
								pEoData->CalcAssistVar.FormState = EoError;
							}
						}
					}break;
					default:
						break;
					}

				}
			}
		}



		if (SeqFinished)
		{
			if (!pEoData->AttributeVar.bFinishProcess)
			{

				if (pEoData->CalcAssistVar.ProcessVar[YMax_Y].RealValue != DefaultValue && pEoData->CalcAssistVar.ProcessVar[YMin_Y].RealValue != DefaultValue)
				{
					pEoData->CalcAssistVar.ProcessVar[PeakPeak_Y].RealValue = pEoData->CalcAssistVar.ProcessVar[YMax_Y].RealValue - pEoData->CalcAssistVar.ProcessVar[YMin_Y].RealValue;
				}
				//GetPolygonProcessMinMax 
				//Q_Next 
				//Entry_X
				//rt_dbgPrint(1, 0, "EO_Polygon_IsOK FormState:%d \n", pEoData->CalcAssistVar.FormState);
				pEoData->CalcAssistVar.ProcessVar[Entry_X].RealValue = pEoData->CalcAssistVar.EnterPoint.X;
				//Entry_Y
				pEoData->CalcAssistVar.ProcessVar[Entry_Y].RealValue = pEoData->CalcAssistVar.EnterPoint.Y;
				pEoData->CalcAssistVar.ProcessVar[EXIT_X].RealValue = pEoData->CalcAssistVar.ExitPoint.X;
				pEoData->CalcAssistVar.ProcessVar[EXIT_Y].RealValue = pEoData->CalcAssistVar.ExitPoint.Y;
				for (unsigned char i = 0; i < 13; i++)
				{
					pEoData->Result.EoResult.ResultItem[i].isPass = 1;
					pEoData->Result.EoResult.ResultItem[i].RealValue = pEoData->CalcAssistVar.ProcessVar[i].RealValue;
					pEoData->Result.EoResult.ResultItem[i].MaxRange = pEoData->CalcAssistVar.ProcessVar[i].MaxRange;
					pEoData->Result.EoResult.ResultItem[i].MinRange = pEoData->CalcAssistVar.ProcessVar[i].MinRange;
					//memcpy(&(pEO_Data->Result.EoResult.ResultItem[i].RealValue), &(ProcessVar[pEoData->EoMap]) + 4 * i, sizeof(float));
				}
				pEoData->AttributeVar.bFinishProcess = true;
			}
			if (pEoData->CalcAssistVar.FormState == EoError || (!pEoData->AttributeVar.bNoPass[0] && pEoData->CalcAssistVar.FormState == NoEnter_NoExit) || (!pEoData->AttributeVar.bNoPass[1] && pEoData->CalcAssistVar.FormState == Entered_NoExit))
			{

				//	pEoData->Result.EoResult.sErrorCode = "Others_Errors";
				if ((pEoData->CalcAssistVar.FormState == NoEnter_NoExit && pEoData->AttributeVar.bOutside) || (pEoData->CalcAssistVar.FormState == Entered_NoExit && pEoData->AttributeVar.bInside))
				{
					;
				}
				else
				{
					pEoData->CalcAssistVar.bEval = true;
					pEoData->Result.EoResult.EoPass = false;
				}
				if (pEoData->CalcAssistVar.FormState != EoError)
				{
					pEoData->Result.EoResult.sErrorCode = "Others_Errors";
				}
			}
			else
			{
				pEoData->CalcAssistVar.bEval = true;
				pEoData->Result.EoResult.EoPass = true;
			}
		}
	}
	return (pEoData->Result.EoResult.EoPass);
}

/**
* @name  EO_Polygon_ResetPara
* @brief
* @param
* @retuen
*/
void EO_Polygon_ResetPara(TagEO_Data* pEO_Data)
{
	//	pPolygon = (TagEO_Type_Polygon*)pEO_Data->ExtConf.pConfBuf;
	pEO_Data->CalcAssistVar.bFirstCalcPoint = true;
	//这个看看可要可不要
	memset(&(pEO_Data->CalcAssistVar.aHyIntersectPoint[0]), 0, 100 * sizeof(TagHyInterSectPoint));

	pEO_Data->AttributeVar.bFinishProcess = false;
	pEO_Data->AttributeVar.bEvaled = false;
	//FormJudge Data Reset
	pEO_Data->CalcAssistVar.EnterPoint.X = DefaultValue;
	pEO_Data->CalcAssistVar.EnterPoint.Y = DefaultValue;
	pEO_Data->CalcAssistVar.ExitPoint.X = DefaultValue;
	pEO_Data->CalcAssistVar.ExitPoint.Y = DefaultValue;
	pEO_Data->CalcAssistVar.TempEnterPoint.X = DefaultValue;
	pEO_Data->CalcAssistVar.TempEnterPoint.Y = DefaultValue;
	pEO_Data->CalcAssistVar.TempExitPoint.X = DefaultValue;
	pEO_Data->CalcAssistVar.TempExitPoint.Y = DefaultValue;
	pEO_Data->CalcAssistVar.bHyEntered = false;
	pEO_Data->CalcAssistVar.bHyExited = false;
	pEO_Data->CalcAssistVar.bHadCrossHy = false;
	pEO_Data->CalcAssistVar.bHadPointsInPolygon = false;
	pEO_Data->CalcAssistVar.bTempHyExit = false;
	pEO_Data->CalcAssistVar.HyInsectPointCount = 0;

	pEO_Data->CalcAssistVar.FormState = NoEnter_NoExit;
	//LastForm = NoEnter_NoExit;
	pEO_Data->CalcAssistVar.bHyEntered = false;
	pEO_Data->CalcAssistVar.bHyExited = false;

	//Result Data Reset
	for (unsigned char index = 0; index < ResultItemLength; index++)
	{
		//这边有一个PeakPeakY赋初值的时候是不对的。可能引起歧义
		pEO_Data->CalcAssistVar.ProcessVar[index].RealValue = DefaultValue;   //Init

	}
	//memset(&(pEO_Data->Result), 0, sizeof(TagEO_Result));   //Qnext
	pEO_Data->Result.EoResult.sErrorCode = "NoErr";
	// add Reset CalcCount相关计算状态
	// AverageY
	pEO_Data->CalcAssistVar.bFirst_Ave = true;
	pEO_Data->CalcAssistVar.Aver_CalcPointCount = 0;
	pEO_Data->CalcAssistVar.Aver_TempTotal = 0.0f;
	pEO_Data->CalcAssistVar.AverageY_Data = 0.0f;

	//Time_
	pEO_Data->CalcAssistVar.bFirst_Time = true;

	// Speed
	pEO_Data->CalcAssistVar.bFirst_Speed = true;
	pEO_Data->CalcAssistVar.StartX = 0.0f;
	pEO_Data->CalcAssistVar.StartTime = 0.0f;
	pEO_Data->CalcAssistVar.Speed_Data = 0.0f;
	pEO_Data->CalcAssistVar.SpeedIndex = 0;
	pEO_Data->CalcAssistVar.WindowFilled = false;
	memset(pEO_Data->CalcAssistVar.RecentSpeeds, 0, SPEED_WINDOW_SIZE * sizeof(float));

	// Inflexion
	pEO_Data->CalcAssistVar.bFirst_Inflexion = true;
	initBuffer(&(pEO_Data->CalcAssistVar.buffer));

	pEO_Data->Result.EoResult.sErrorCode = "NoErr";
	bProcessCalcCount = 0;

}

/**
* @name  EO_Envolope_Init
* @brief
* @param
* @retuen
*/
unsigned int EO_Envelope_Init(TagEO_Data* pEO_Data)
{
	unsigned int ErrCode = 0;
	float xHy = 0;
	pEnvelope = (TagEO_Type_Envelope*)pEO_Data->ExtConf.pConfBuf;
	pEO_Data->AttributeVar.bOutside = false;
	pEO_Data->AttributeVar.bInside = false;
	pEO_Data->AttributeVar.bNoPass[0] = false;
	pEO_Data->AttributeVar.bNoPass[1] = false;
	pEO_Data->AttributeVar.bAnyPass[0] = false;
	pEO_Data->AttributeVar.bAnyPass[1] = false;


	pEO_Data->AttributeVar.PointsCount = pEnvelope->iEnvelopePointsCount;

	//这里的真实框取数轴上的四个点
	if (pEnvelope->iEnvelopePointsCount > 0 && pEnvelope->aLowerPoint > 0 && pEnvelope->aUpperPoint > 0)
	{
		pEO_Data->AttributeVar.RealModelPoints[0] = pEnvelope->aLowerPoint[0];
		pEO_Data->AttributeVar.RealModelPoints[1] = pEnvelope->aUpperPoint[0];
		pEO_Data->AttributeVar.RealModelPoints[2] = pEnvelope->aUpperPoint[pEnvelope->iUpperPointsCount - 1];
		pEO_Data->AttributeVar.RealModelPoints[3] = pEnvelope->aLowerPoint[pEnvelope->iUpperPointsCount - 1];
	}
	//rt_dbgPrint(1, 0, "EO_Envelope_Init  X RealModelPoints[0]:%f  RealModelPoints[1]:%f RealModelPoints[2]:%f RealModelPoints[3]:%f\n", pEO_Data->AttributeVar.RealModelPoints[0].X, pEO_Data->AttributeVar.RealModelPoints[1].X, pEO_Data->AttributeVar.RealModelPoints[2].X, pEO_Data->AttributeVar.RealModelPoints[3].X);


	//EnterEnd 根据穿入穿出类型对穿入穿出赋值
	if (pEnvelope->iEntryExitType == 1)  //左穿入、右穿出
	{
		pEO_Data->AttributeVar.EnterStart = 0;
		pEO_Data->AttributeVar.EnterEnd = 1;
		pEO_Data->AttributeVar.ExitStart = 2;    //Qnext??这个是2 3  还是  3  2 
		pEO_Data->AttributeVar.ExitEnd = 3;
	}
	else if (pEnvelope->iEntryExitType == 2)
	{
		pEO_Data->AttributeVar.EnterStart = 2;
		pEO_Data->AttributeVar.EnterEnd = 3;
		pEO_Data->AttributeVar.ExitStart = 1;    //Qnext??这个是2 3  还是  3  2 
		pEO_Data->AttributeVar.ExitEnd = 0;
	}

	//包络只有X迟滞
	if (pEO_Data->ConfData_Base.UseGlobalHy == 0) //local Hy
	{
		xHy = pEO_Data->ConfData_Base.xHysteresis;
	}
	else
	{
		//全局迟滞，用SDO写进来？
		xHy = PubPara.LocalxHy;
	}
	//rt_dbgPrint(1, 0, "EO_Envelope_Init     xHy:%f  bUseGlobalHy:%d \n", xHy, pEO_Data->ConfData_Base.UseGlobalHy);
	//memcpy(pEO_Data->AttributeVar.HyModelPoints, pEO_Data->AttributeVar.RealModelPoints, sizeof(pEO_Data->AttributeVar.HyModelPoints));
	if (xHy != 0)
	{
		pEO_Data->AttributeVar.HyPointsCount = 4;
		//0-3 左边迟滞矩形框
		//4-7 右边迟滞矩形框
		pEO_Data->AttributeVar.HyModelPoints[0].X = pEO_Data->AttributeVar.RealModelPoints[0].X - xHy;
		pEO_Data->AttributeVar.HyModelPoints[0].Y = pEO_Data->AttributeVar.RealModelPoints[0].Y;
		pEO_Data->AttributeVar.HyModelPoints[1].X = pEO_Data->AttributeVar.RealModelPoints[1].X - xHy;
		pEO_Data->AttributeVar.HyModelPoints[1].Y = pEO_Data->AttributeVar.RealModelPoints[1].Y;
		pEO_Data->AttributeVar.HyModelPoints[2].X = pEO_Data->AttributeVar.RealModelPoints[1].X;
		pEO_Data->AttributeVar.HyModelPoints[2].Y = pEO_Data->AttributeVar.RealModelPoints[1].Y;
		pEO_Data->AttributeVar.HyModelPoints[3].X = pEO_Data->AttributeVar.RealModelPoints[0].X;
		pEO_Data->AttributeVar.HyModelPoints[3].Y = pEO_Data->AttributeVar.RealModelPoints[0].Y;


		pEO_Data->AttributeVar.HyModelPoints[4].X = pEO_Data->AttributeVar.RealModelPoints[3].X;
		pEO_Data->AttributeVar.HyModelPoints[4].Y = pEO_Data->AttributeVar.RealModelPoints[3].Y;
		pEO_Data->AttributeVar.HyModelPoints[5].X = pEO_Data->AttributeVar.RealModelPoints[2].X;
		pEO_Data->AttributeVar.HyModelPoints[5].Y = pEO_Data->AttributeVar.RealModelPoints[2].Y;
		pEO_Data->AttributeVar.HyModelPoints[6].X = pEO_Data->AttributeVar.RealModelPoints[2].X + xHy;
		pEO_Data->AttributeVar.HyModelPoints[6].Y = pEO_Data->AttributeVar.RealModelPoints[2].Y;
		pEO_Data->AttributeVar.HyModelPoints[7].X = pEO_Data->AttributeVar.RealModelPoints[3].X + xHy;
		pEO_Data->AttributeVar.HyModelPoints[7].Y = pEO_Data->AttributeVar.RealModelPoints[3].Y;
		memcpy(pEO_Data->AttributeVar.RefRealModelPoints, pEO_Data->AttributeVar.RealModelPoints, sizeof(pEO_Data->AttributeVar.RefRealModelPoints));
	}
	else
	{
		memcpy(pEO_Data->AttributeVar.HyModelPoints, pEO_Data->AttributeVar.RealModelPoints, sizeof(pEO_Data->AttributeVar.HyModelPoints));
		//Qnext???  相对坐标系
		memcpy(pEO_Data->AttributeVar.RefRealModelPoints, pEO_Data->AttributeVar.RealModelPoints, sizeof(pEO_Data->AttributeVar.RefRealModelPoints));
		memcpy(pEO_Data->AttributeVar.RefHyModelPoints, pEO_Data->AttributeVar.RealModelPoints, sizeof(pEO_Data->AttributeVar.RefRealModelPoints));
	}
	//rt_dbgPrint(1, 0, "EO_Envelope_Init## HyX1: %f  HyX2:%f \n", pEO_Data->AttributeVar.HyModelPoints[0].X, pEO_Data->AttributeVar.HyModelPoints[2].X);
	//EO pEoData->CalcAssistVar.ProcessVar[i] Range Init
	for (unsigned char index = 0; index < ResultItemLength; index++)
	{
		if (index % 2 == 1)  //index为奇数时   Y
		{
			pEO_Data->CalcAssistVar.ProcessVar[index].MaxRange = pEnvelope->Ymax;
			pEO_Data->CalcAssistVar.ProcessVar[index].MinRange = pEnvelope->Ymin;
		}
		else  //index为偶数时  X
		{
			pEO_Data->CalcAssistVar.ProcessVar[index].MaxRange = pEnvelope->XMax;
			pEO_Data->CalcAssistVar.ProcessVar[index].MinRange = pEnvelope->Xmin;
		}
		//这边有一个PeakPeakY赋初值的时候是不对的。可能引起歧义
		pEO_Data->CalcAssistVar.ProcessVar[index].RealValue = DefaultValue;   //Init
	}
	//PeakPeak_Y
	pEO_Data->CalcAssistVar.ProcessVar[PeakPeak_Y].MaxRange = DefaultValue;
	pEO_Data->CalcAssistVar.ProcessVar[PeakPeak_Y].MinRange = DefaultValue;



	//闭合的多边形已经在pConfData里面
	//需要更新出包络的迟滞框
	//我这一版要加上迟滞吗？
	return ErrCode;
}

/**
* @name  EO_Envolope_IsOK
* @brief
* @param
* @retuen
*/
bool EO_Envelope_IsOK(TagEO_Data* pEO_Data, Tag3FPoint* pCurve, bool SeqFinished)
{
	pEnvelope = (TagEO_Type_Envelope*)pEO_Data->ExtConf.pConfBuf;
	pEoData = pEO_Data;
	pCurPoint = pCurve;
	pLastPoint = pCurve;
	bSeqHadFinished = SeqFinished;
	//bTunnelStop = false;
	bEvalEnd = false;
	bool bDir, bLastDir; //点和直线的关系
	bool bHadCrossHy = false;   //进出过迟滞
	short HyInsectPointCount = 0;

	if (pEoData->AttributeVar.PointsCount > CURVE_MAX_POINTS_NUMBER)
	{
		if (bPrintFlag)
		{
			printf("The sum of Curve Points exceeds 50,0000!");
		}
		pEoData->CalcAssistVar.FormState = EoError;
		pEoData->Result.EoResult.sErrorCode = "PointsMoreThan_50w";
		pEoData->CalcAssistVar.bEval = true;
		pEoData->Result.EoResult.EoPass = false;
		return false;
	}

	if (pEoData->CalcAssistVar.CurPointCount >= pEoData->CalcAssistVar.LastPointCount)
	{
		for (unsigned long int j = pEoData->CalcAssistVar.LastPointCount; j < pEoData->CalcAssistVar.CurPointCount; j++)
		{
			//第一个点，j=0
			//rt_dbgPrint(1, 0, "EO_Envelope_IsOK:%d  envelopeFormState:%d \n", j ,envelopeFormState);
			pCurPoint = pCurve + j;
			if (j == 0)
			{
				pLastPoint = pCurve;
			}
			else if (j >= 1)
			{
				pLastPoint = pCurve + (j - 1);
			}
			else
			{
				//err:j<0
			}
			//点和包络围成的多边形的关系
			Relation = Geometry_Point_Polygon(pCurPoint, pEnvelope->aEnvelopePoint, pEoData->AttributeVar.PointsCount);
			LastRelation = Geometry_Point_Polygon(pLastPoint, pEnvelope->aEnvelopePoint, pEoData->AttributeVar.PointsCount);
			//rt_dbgPrint(1, 0, "EnvelopeIsOK## HyX1: %f  HyX2:%f   pCurPoint->X:%f\n", pEoData->EoMap, pEoData->AttributeVar.HyModelPoints[0].X, pEoData->AttributeVar.HyModelPoints[6].X, pCurPoint->X);
			//当前点和迟滞框的关系
			//后续加入坐标系需要改成refHy
			if (Relation)  //当前点在真实框内（X,Y限制）
			{
				HyRelation = Relation;
			}
			else//当前点在迟滞框外，穿入迟滞矩形+包络+穿出迟滞矩形
			{
				HyRelation = Geometry_Point_Polygon(pCurPoint, pEnvelope->aEnvelopePoint, pEoData->AttributeVar.PointsCount);
				//rt_dbgPrint(1, 0, "EnvelopeIsOK## 111   HyRelation= %d  \n",  HyRelation);
				//迟滞矩形框左
				HyRelation = HyRelation || Geometry_Point_Polygon(pCurPoint, &(pEoData->AttributeVar.HyModelPoints[0]), pEoData->AttributeVar.HyPointsCount);
				//	rt_dbgPrint(1, 0, "EnvelopeIsOK## 222   HyRelation= %d  \n", HyRelation);
					//迟滞矩形框右
				HyRelation = HyRelation || Geometry_Point_Polygon(pCurPoint, &(pEoData->AttributeVar.HyModelPoints[4]), pEoData->AttributeVar.HyPointsCount);
				//	rt_dbgPrint(1, 0, "EnvelopeIsOK## 333   HyRelation= %d  \n",  HyRelation);
			}
			if (Relation)
			{
				pEoData->CalcAssistVar.bHadPointsInPolygon = true;
			}
			//bHadCrossHy
			if (!pEoData->CalcAssistVar.bHadCrossHy)  //在迟滞框内，并且是在穿入边反复穿出
			{
				if (!HyRelation && pEoData->CalcAssistVar.bHadPointsInPolygon)
				{
					pEoData->CalcAssistVar.bHadCrossHy = true;
				}
			}
			//	rt_dbgPrint(1, 0, "EnvelopeIsOK## EoIndex= %d  FormState:% d  Relation:%d   HyRelation:%d\n", pEoData->EoMap, pEoData->CalcAssistVar.FormState, Relation, HyRelation);

				//FormJudge
			if (!bEvalEnd)
			{
				if (j == 0)
				{
					//第一个点
					pLastPoint = pCurPoint;
					if (pEoData->AttributeVar.bNoPass[0]) //无穿入，我的NoPass和Anypass是要当作输入，还是判断条件?
					{
						pEoData->CalcAssistVar.FormState = Entered_NoExit;
					}
					else
					{
						pEoData->CalcAssistVar.FormState = NoEnter_NoExit;
					}
					pEoData->CalcAssistVar.EnterPoint.X = DefaultValue;
					pEoData->CalcAssistVar.EnterPoint.Y = DefaultValue;
					pEoData->CalcAssistVar.ExitPoint.X = DefaultValue;
					pEoData->CalcAssistVar.ExitPoint.Y = DefaultValue;
					pEoData->CalcAssistVar.bHyEntered = false;

					//LastRelation = false;
				}
				else if (j < CURVE_MAX_POINTS_NUMBER)
				{
					pLastPoint = pCurve + j - 1;
					Point2F1.X = pCurPoint->X;
					Point2F1.Y = pCurPoint->Y;
					Point2F2.X = pLastPoint->X;
					Point2F2.Y = pLastPoint->Y;
					//FindEntryExitLine 

					if ((pEoData->AttributeVar.bOutside) || (pEoData->AttributeVar.bInside))
					{
						if (pEoData->AttributeVar.bInside)
						{
							if (Relation != true)
							{
								pEoData->CalcAssistVar.FormState = EoError;  //Inside：曲线全部包含在Polygon内；Err:存在不包含在Polygon内的点
								//NotAllInside
								pEoData->Result.EoResult.sErrorCode = "NotAllInside";
								pEoData->CalcAssistVar.bEval = true;
								pEoData->Result.EoResult.EoPass = false;
							}
							else
							{
								pEoData->CalcAssistVar.FormState = NoEnter_NoExit;
							}
						}
						else if ((pEoData->AttributeVar.bOutside))
						{
							if (Relation != true)
							{
								pEoData->CalcAssistVar.FormState = NoEnter_NoExit;
							}
							else
							{
								pEoData->CalcAssistVar.FormState = EoError;
								pEoData->Result.EoResult.sErrorCode = "NotAllOutside"; //Outside：曲线全部包含在Polygon内；Err:存在包含在Polygon内的点
								pEoData->CalcAssistVar.bEval = true;
								pEoData->Result.EoResult.EoPass = false;
							}
						}
					}
					switch (pEoData->CalcAssistVar.FormState)
					{
					case NoEnter_NoExit: {
						//无穿入无穿出
						//判断是否发生穿入穿出行为
						//是否要放在这里面判断相交是否正确
						//我这边认为包络没有anypass选项
						//包络是否需要包含AnyPass的选项，这个应用是不是比较少，

						if ((LastRelation == false && Relation == true) || pEoData->AttributeVar.bNoPass[0])//穿入
						{
							//判断相交是否正确
							//包络只判断穿入边和穿出边是否相交，且相交方向是否正确
							if (pEoData->AttributeVar.PointsCount > 0)
							{
								for (unsigned int i = 0; i < 2; i++)
								{
									unsigned int t = 0;
									t = 2 * i + 1;

									bDir = Geometry_Point_Line(pCurPoint, &(pEO_Data->AttributeVar.RealModelPoints[2 * i]), &(pEO_Data->AttributeVar.RealModelPoints[t]));
									bLastDir = Geometry_Point_Line(pLastPoint, &(pEO_Data->AttributeVar.RealModelPoints[2 * i]), &(pEO_Data->AttributeVar.RealModelPoints[t]));
									if (bDir != bLastDir)  //前后两个点和直线的方向发生改变
									{
										//发生相交
										pEoData->CalcAssistVar.bLineEnterIntersected = Geometry_Line_Line(&(pEoData->AttributeVar.RefRealModelPoints[2 * i]), &(pEoData->AttributeVar.RefRealModelPoints[t]), &(Point2F1), &(Point2F2), &(pEoData->CalcAssistVar.TempEnterPoint));   //True为相交
									}
									else
									{
										pEoData->CalcAssistVar.bLineEnterIntersected = false;
									}

									if (pEoData->CalcAssistVar.bLineEnterIntersected)//发生相交
									{
										if (pEoData->CalcAssistVar.bHadCrossHy)
										{
											pEoData->CalcAssistVar.FormState = EoError;   //出迟滞后反复穿入
											pEoData->Result.EoResult.sErrorCode = "OutHyRepeatEnter";  //穿入方向错误
											pEoData->CalcAssistVar.bEval = true;
											pEoData->Result.EoResult.EoPass = false;
										}
										//判断是否和正确边相交
										else if (((2 * i == pEoData->AttributeVar.EnterStart) && (t == pEoData->AttributeVar.EnterEnd) || (t == pEoData->AttributeVar.EnterStart) && (2 * i == pEoData->AttributeVar.EnterEnd)) && (!pEoData->AttributeVar.bNoPass[0]))  //这个相交边需要用个变量再给赋值
										{
											if (pEoData->CalcAssistVar.EnterPoint.X == DefaultValue)
											{
												//首次穿入
												pEoData->CalcAssistVar.EnterPoint = pEoData->CalcAssistVar.TempEnterPoint;
												pEoData->CalcAssistVar.FormState = Entered_NoExit;

											}
											else if ((!pEoData->CalcAssistVar.bHadCrossHy) && HyRelation || pEoData->CalcAssistVar.bAllowRepeatEntryExit) //判断是否为迟滞框内的穿入
											{
												pEoData->CalcAssistVar.FormState = Entered_NoExit;
											}
											else
											{
												pEoData->CalcAssistVar.FormState = EoError;
												pEoData->Result.EoResult.sErrorCode = "Entry_6";  //穿入方向错误
												pEoData->CalcAssistVar.bEval = true;
												pEoData->Result.EoResult.EoPass = false;
												break;
											}
											//	rt_dbgPrint(1, 0, "EnvelopeIsOK##  EnterPoint.X:%f \n     EnterPoint.Y: %f   \n", pEO_Data->CalcAssistVar.EnterPoint.X, pEO_Data->CalcAssistVar.EnterPoint.Y);

											//	rt_dbgPrint(1, 0, "EnvelopeIsOK##  PointCountIndex j== %d      CurPointCount:   %d    LastPointCount:   %d  \n", j, pEoData->CalcAssistVar.CurPointCount, pEoData->CalcAssistVar.LastPointCount);

												//如果是迟滞的情况，记录一下点
											if (pEO_Data->CalcAssistVar.bHyEntered)
											{
												//记录迟滞内交点
												pEO_Data->CalcAssistVar.aHyIntersectPoint[HyInsectPointCount].bDirection = true;  //穿入
												pEO_Data->CalcAssistVar.aHyIntersectPoint[HyInsectPointCount].HyInterSectPoint = *pCurPoint; //记录真实点，当前点跟上一个构成穿入
												HyInsectPointCount = HyInsectPointCount + 1;
											}
											i = 2;
											break;
										}
										else   //包含 bNoPass[0]
										{
											pEoData->CalcAssistVar.FormState = EoError;
											pEoData->Result.EoResult.sErrorCode = "Entry_4";  //穿入方向错误
											pEoData->CalcAssistVar.bEval = true;
											pEoData->Result.EoResult.EoPass = false;
											break;
										}
									}
								}
							}
							else
							{
								// Err the PointsCount is invalid
							}

						}
						else if (LastRelation == true && Relation == false)//穿出
						{
							/*rt_dbgPrint(1, 0, "EnvelopeIsOK##  Entry5###   EoIndex= %d  FormState:% d \n", pEoData->EoMap, pEoData->CalcAssistVar.FormState);
							rt_dbgPrint(1, 0, "EnvelopeIsOK##  Entry5### pCurPoint.X:%f   pCurPoint.Y: %f   \n", pCurPoint->X, pCurPoint->Y);
							rt_dbgPrint(1, 0, "EnvelopeIsOK##  Entry5### pLastPoint.X:%f   pLastPoint.Y: %f   \n", pLastPoint->X, pLastPoint->Y);
							rt_dbgPrint(1, 0, "EnvelopeIsOK##  Entry5### PointCountIndex j== %d      CurPointCount:   %d    LastPointCount:   %d  \n", j, pEoData->CalcAssistVar.CurPointCount, pEoData->CalcAssistVar.LastPointCount);*/

							pEoData->CalcAssistVar.FormState = EoError;
							pEoData->Result.EoResult.sErrorCode = "Entry_5";  //穿入方向错误
							pEoData->CalcAssistVar.bEval = true;
							pEoData->Result.EoResult.EoPass = false;
							break;
						}
						else if (Relation == true)  //一开始就在框内，这一段也应该Qnext
						{
							if (pEoData->AttributeVar.bNoPass[0]) //无穿入，我的NoPass和Anypass是要当作输入，还是判断条件?
							{
								pEoData->CalcAssistVar.FormState == Entered_NoExit;
							}
							else
							{
								pEoData->CalcAssistVar.FormState == NoEnter_NoExit;
							}
						}
						//NoEnterandNoexit,判断点是否一直位于矩形的一侧
						//如果是发生两点直接跨越矩形的情况，则这两点连成的Line要跟矩形做便便相交测试，判断相交边是否正确
					}break;
					case Entered_NoExit: {
						if ((LastRelation == false && Relation == true) || pEoData->AttributeVar.bNoPass[0])//穿入
						{
							pEoData->CalcAssistVar.FormState = EoError;  //穿入又穿入， 如果迟滞，Entered_NoExit这个状态不发生变更的话，就会出现穿入又穿出，这个逻辑需要理一下  
							pEoData->Result.EoResult.sErrorCode = "Entry_6";  //穿入方向错误
							pEoData->CalcAssistVar.bEval = true;
							pEoData->Result.EoResult.EoPass = false;
							break;
						}
						else if ((LastRelation == true && Relation == false))//穿出
						{
							//rt_dbgPrint(1, 0, "Entered_NoExit22##  PointCountIndex j== %d      CurPointCount:   %d    LastPointCount:   %d  \n", j, pEoData->CalcAssistVar.CurPointCount, pEoData->CalcAssistVar.LastPointCount);
							if (pEoData->AttributeVar.PointsCount > 0)
							{
								for (unsigned int i = 0; i < 2; i++)
								{
									unsigned int t = 0;
									t = 2 * i + 1;

									bDir = Geometry_Point_Line(pCurPoint, &(pEO_Data->AttributeVar.RealModelPoints[2 * i]), &(pEO_Data->AttributeVar.RealModelPoints[t]));
									bLastDir = Geometry_Point_Line(pLastPoint, &(pEO_Data->AttributeVar.RealModelPoints[2 * i]), &(pEO_Data->AttributeVar.RealModelPoints[t]));
									if (bDir != bLastDir)  //前后两个点和直线的方向发生改变
									{
										//发生相交
										pEoData->CalcAssistVar.bLineExitIntersected = Geometry_Line_Line(&(pEoData->AttributeVar.RefRealModelPoints[2 * i]), &(pEoData->AttributeVar.RefRealModelPoints[t]), &(Point2F1), &(Point2F2), &(pEoData->CalcAssistVar.TempExitPoint));   //True为相交
									}
									else
									{
										pEoData->CalcAssistVar.bLineExitIntersected = false;
									}

									if (pEoData->CalcAssistVar.bLineExitIntersected)//发生相交
									{
										//rt_dbgPrint(1, 0, "envelopeFormState#EnteredNoExit  IntersectPoint.X:%f \n   IntersectPoint.Y: %f \n   ", pEO_Data->CalcAssistVar.TempExitPoint.X, pEO_Data->CalcAssistVar.TempExitPoint.Y);
										//判断是否和正确边相交
										if (((2 * i == pEoData->AttributeVar.ExitStart) && (t == pEoData->AttributeVar.ExitEnd) || (t == pEoData->AttributeVar.ExitStart) && (2 * i == pEoData->AttributeVar.ExitEnd)) && (!pEoData->AttributeVar.bNoPass[1]))
										{
											if (pEoData->CalcAssistVar.ExitPoint.X == DefaultValue)  //未找到穿出
											{
												pEoData->CalcAssistVar.ExitPoint = pEoData->CalcAssistVar.TempExitPoint;
												pEoData->CalcAssistVar.FormState = Entered_Exited;
											}
											else if ((!pEoData->CalcAssistVar.bHadCrossHy) && HyRelation || pEoData->CalcAssistVar.bAllowRepeatEntryExit)
											{
												// Hy Exit
												pEoData->CalcAssistVar.FormState = Entered_Exited;
											}
											if (pEO_Data->CalcAssistVar.bHyExited)
											{
												//记录迟滞内交点
												pEO_Data->CalcAssistVar.aHyIntersectPoint[HyInsectPointCount].bDirection = false;  //穿入
												pEO_Data->CalcAssistVar.aHyIntersectPoint[HyInsectPointCount].HyInterSectPoint = *pCurPoint; //记录真实点，当前点跟上一个构成穿出
												HyInsectPointCount = HyInsectPointCount + 1;
											}
											i = 2;
											break;
										}
										if (((2 * i == pEoData->AttributeVar.EnterStart) && (t == pEoData->AttributeVar.EnterEnd) || (t == pEoData->AttributeVar.EnterStart) && (2 * i == pEoData->AttributeVar.EnterEnd)) && (!pEoData->AttributeVar.bNoPass[0]))
										{
											//迟滞的情况，这里需要注意的点是，Anypass判断迟滞边的方式 Qnext
											if ((!pEoData->CalcAssistVar.bHadCrossHy) && HyRelation || pEoData->CalcAssistVar.bAllowRepeatEntryExit)  //在迟滞框内，并且是在穿入边反复穿出
											{
												pEoData->CalcAssistVar.FormState = NoEnter_NoExit;

												//记录迟滞内交点
												pEO_Data->CalcAssistVar.aHyIntersectPoint[HyInsectPointCount].bDirection = false;
												pEO_Data->CalcAssistVar.aHyIntersectPoint[HyInsectPointCount].HyInterSectPoint = *pCurPoint; //记录真实点，当前点跟上一个构成穿出
												pEO_Data->CalcAssistVar.bHyEntered = true;
												HyInsectPointCount = HyInsectPointCount + 1;
												bHadCrossHy = false;
											}
											else
											{
												//这里倒是不影响，在包络内部的时候即便报错报不出来，Qnext?是否有风险
												bHadCrossHy = true;
												pEoData->CalcAssistVar.FormState = NoEnter_NoExit;
											}
											i = 2;
											break;
										}
										else  //NoPass[1]
										{
											pEoData->CalcAssistVar.FormState = EoError;

											pEoData->Result.EoResult.sErrorCode = "EXIT_";  //穿出方向错误
											pEoData->CalcAssistVar.bEval = true;
											pEoData->Result.EoResult.EoPass = false;
											break;
										}
									}
								}
							}
							else
							{
								//Err
							}
						}
						else  //Relation未发生改变
						{
							;
							//默认EoPass==true的情况下这个就可以不用做判断。
						}

						//rt_dbgPrint(1, 0, "Entered_NoExit##  PointCountIndex j== %d      CurPointCount:   %d    LastPointCount:   %d  \n", j, pEoData->CalcAssistVar.CurPointCount, pEoData->CalcAssistVar.LastPointCount);

					}break;
					case Entered_Exited: {
						if (LastRelation == false && Relation == false)
						{
							;
							//发生穿入穿出后一直在矩形外
						}
						else
						{
							//穿出边迟滞
							if (LastRelation == false && Relation == true && HyRelation)
							{
								if (pEoData->AttributeVar.PointsCount > 0)
								{
									for (unsigned int i = 0; i < 2; i++)
									{
										unsigned int t = 0;
										t = 2 * i + 1;

										bDir = Geometry_Point_Line(pCurPoint, &(pEO_Data->AttributeVar.RealModelPoints[2 * i]), &(pEO_Data->AttributeVar.RealModelPoints[t]));
										bLastDir = Geometry_Point_Line(pLastPoint, &(pEO_Data->AttributeVar.RealModelPoints[2 * i]), &(pEO_Data->AttributeVar.RealModelPoints[t]));
										if (bDir != bLastDir)  //前后两个点和直线的方向发生改变
										{
											//发生相交
											pEoData->CalcAssistVar.bLineRepeatIntersected = Geometry_Line_Line(&(pEoData->AttributeVar.RefRealModelPoints[2 * i]), &(pEoData->AttributeVar.RefRealModelPoints[t]), &(Point2F1), &(Point2F2), &(pEoData->CalcAssistVar.TempEnterPoint));   //True为相交
										}
										else
										{
											pEoData->CalcAssistVar.bLineRepeatIntersected = false;
										}

										if (pEoData->CalcAssistVar.bLineRepeatIntersected)//出真实框后再进入
										{

											if (pEoData->CalcAssistVar.bHadCrossHy)
											{
												pEoData->CalcAssistVar.FormState = EoError;   //出迟滞后反复穿入
												pEoData->Result.EoResult.sErrorCode = "OutHyRepeatEnter";  //穿入方向错误
												pEoData->CalcAssistVar.bEval = true;
												pEoData->Result.EoResult.EoPass = false;
												i = 2;
												break;
											}
											if (((2 * i == pEoData->AttributeVar.ExitStart) && (t == pEoData->AttributeVar.ExitEnd) || (t == pEoData->AttributeVar.ExitStart) && (2 * i == pEoData->AttributeVar.ExitEnd)) && (!pEoData->AttributeVar.bNoPass[1]))
											{
												if ((!pEoData->CalcAssistVar.bHadCrossHy) && HyRelation || pEoData->CalcAssistVar.bAllowRepeatEntryExit)
												{
													pEoData->CalcAssistVar.FormState = Entered_NoExit;
													pEO_Data->CalcAssistVar.bHyExited = true;
													//记录迟滞内交点
													pEO_Data->CalcAssistVar.aHyIntersectPoint[HyInsectPointCount].bDirection = true;  //穿入
													pEO_Data->CalcAssistVar.aHyIntersectPoint[HyInsectPointCount].HyInterSectPoint = *pCurPoint; //记录真实点，当前点跟上一个构成穿出
													HyInsectPointCount = HyInsectPointCount + 1;
												}
												i = 2;
												break;
											}
											else  //NoPass[1]
											{
												pEoData->CalcAssistVar.FormState = EoError;

												pEoData->Result.EoResult.sErrorCode = "OutHyRepeatEXIT";  //穿出方向错误
												pEoData->CalcAssistVar.bEval = true;
												pEoData->Result.EoResult.EoPass = false;
												i = 2;
												break;
											}
										}
									}
								}

							}
							else
							{
								pEoData->CalcAssistVar.FormState = EoError;
								pEoData->Result.EoResult.sErrorCode = "OutHyRepeatEXIT";  //穿出方向错误
								pEoData->CalcAssistVar.bEval = true;
								pEoData->Result.EoResult.EoPass = false;
								break;
							}
						}
					}break;
					case EoPass:
						break;
					case EoError:
						pEoData->Result.EoResult.EoPass = false;
						bEvalEnd = true;
						break;
					default:
						break;
					}

					//ProcessVarCalc 
					ProcessVarCalc(pEoData, pCurPoint);
				}
				else
				{
					pEoData->CalcAssistVar.FormState = EoError;
					pEoData->Result.EoResult.sErrorCode = "PointsMoreThan_50w";
					pEoData->CalcAssistVar.bEval = true;
					pEoData->Result.EoResult.EoPass = false;
					return false;

				}
			}
		}

		if (SeqFinished)
		{
			if (!pEoData->AttributeVar.bFinishProcess)
			{

				if (pEoData->CalcAssistVar.ProcessVar[YMax_Y].RealValue != DefaultValue && pEoData->CalcAssistVar.ProcessVar[YMin_Y].RealValue != DefaultValue)
				{
					pEoData->CalcAssistVar.ProcessVar[PeakPeak_Y].RealValue = pEoData->CalcAssistVar.ProcessVar[YMax_Y].RealValue - pEoData->CalcAssistVar.ProcessVar[YMin_Y].RealValue;
				}
				//GetPolygonProcessMinMax 
				//Q_Next 
				//Entry_X
				pEoData->CalcAssistVar.ProcessVar[Entry_X].RealValue = pEoData->CalcAssistVar.EnterPoint.X;
				//Entry_Y
				pEoData->CalcAssistVar.ProcessVar[Entry_Y].RealValue = pEoData->CalcAssistVar.EnterPoint.Y;
				pEoData->CalcAssistVar.ProcessVar[EXIT_X].RealValue = pEoData->CalcAssistVar.ExitPoint.X;
				pEoData->CalcAssistVar.ProcessVar[EXIT_Y].RealValue = pEoData->CalcAssistVar.ExitPoint.Y;
				for (unsigned char i = 0; i < ResultItemLength; i++)
				{
					pEO_Data->Result.EoResult.ResultItem[i].isPass = 1;
					pEO_Data->Result.EoResult.ResultItem[i].RealValue = pEoData->CalcAssistVar.ProcessVar[i].RealValue;
					pEO_Data->Result.EoResult.ResultItem[i].MaxRange = pEoData->CalcAssistVar.ProcessVar[i].MaxRange;
					pEO_Data->Result.EoResult.ResultItem[i].MinRange = pEoData->CalcAssistVar.ProcessVar[i].MinRange;
				}
				pEoData->AttributeVar.bFinishProcess = true;
			}
			if (pEoData->CalcAssistVar.FormState == EoError || (!pEoData->AttributeVar.bNoPass[0] && pEoData->CalcAssistVar.FormState == NoEnter_NoExit) || (!pEoData->AttributeVar.bNoPass[1] && pEoData->CalcAssistVar.FormState == Entered_NoExit))
			{
				//rt_dbgPrint(1, 0, "EnvelopeIsOK##  envelopeFormState:% d \n", envelopeFormState);
				//rt_dbgPrint(1, 0, "EnvelopeIsOK##  PointCountIndex j== %d      CurPointCount:   %d    LastPointCount:   %d  \n", j, pEoData->CalcAssistVar.CurPointCount, pEoData->CalcAssistVar.LastPointCount);
				if ((pEoData->CalcAssistVar.FormState == NoEnter_NoExit && pEoData->AttributeVar.bOutside) || (pEoData->CalcAssistVar.FormState == Entered_NoExit && pEoData->AttributeVar.bInside))
				{
					;
				}
				else
				{
					//	pEoData->Result.EoResult.sErrorCode = "Others_Errors";
					pEoData->CalcAssistVar.bEval = true;
					pEoData->Result.EoResult.EoPass = false;
				}
			}
			else
			{
				pEoData->CalcAssistVar.bEval = true;
				pEoData->Result.EoResult.EoPass = true;
			}
		}
	}
	return (pEoData->Result.EoResult.EoPass);
}

/**
* @name  EO_Envolope_ResetPara
* @brief
* @param  pEO_Data
* @retuen
*/
void EO_Envelope_ResetPara(TagEO_Data* pEO_Data)
{
	memset(&(pEO_Data->CalcAssistVar.aHyIntersectPoint[0]), 0, 100 * sizeof(TagHyInterSectPoint));

	pEO_Data->CalcAssistVar.bFirstCalcPoint = true;
	pEO_Data->AttributeVar.bFinishProcess = false;
	pEO_Data->AttributeVar.bEvaled = false;
	//FormJudge Data Reset
	pEO_Data->CalcAssistVar.EnterPoint.X = DefaultValue;
	pEO_Data->CalcAssistVar.EnterPoint.Y = DefaultValue;
	pEO_Data->CalcAssistVar.ExitPoint.X = DefaultValue;
	pEO_Data->CalcAssistVar.ExitPoint.Y = DefaultValue;
	pEO_Data->CalcAssistVar.TempEnterPoint.X = DefaultValue;
	pEO_Data->CalcAssistVar.TempEnterPoint.Y = DefaultValue;
	pEO_Data->CalcAssistVar.TempExitPoint.X = DefaultValue;
	pEO_Data->CalcAssistVar.TempExitPoint.Y = DefaultValue;
	pEO_Data->CalcAssistVar.bHyEntered = false;
	pEO_Data->CalcAssistVar.bHyExited = false;
	pEO_Data->CalcAssistVar.bHadCrossHy = false;
	pEO_Data->CalcAssistVar.bHadPointsInPolygon = false;
	pEO_Data->CalcAssistVar.bTempHyExit = false;
	pEO_Data->CalcAssistVar.HyInsectPointCount = 0;

	pEO_Data->CalcAssistVar.FormState = NoEnter_NoExit;
	//LastForm = NoEnter_NoExit;
	pEO_Data->CalcAssistVar.bHyEntered = false;
	pEO_Data->CalcAssistVar.bHyExited = false;

	//Result Data Reset
	for (unsigned char index = 0; index < ResultItemLength; index++)
	{
		//这边有一个PeakPeakY赋初值的时候是不对的。可能引起歧义
		pEO_Data->CalcAssistVar.ProcessVar[index].RealValue = DefaultValue;   //Init

	}
	//memset(&(pEO_Data->Result), 0, sizeof(TagEO_Result));   //Qnext
	pEO_Data->Result.EoResult.sErrorCode = "NoErr";
	bProcessCalcCount = 0;
}

/**
* @name  ResultData
* @brief
* @param pResultBuffer bCraftIsOK
* @retuen
*/
void ResultData(char** pBuffer, uint64* Length, bool* pbCraftIsOK)
{
	if (TestFlag)
	{
		EoCount = 2;
		eoConfs[0].EOType = eLine;
		eoConfs[0].Result.EoResult.EoPass = true;
		eoConfs[0].Result.EoResult.sErrorCode = "NoErr";
		eoConfs[0].Result.EoResult.ResultItem[0].bActive = true;
		eoConfs[0].Result.EoResult.ResultItem[0].isPass = true;
		eoConfs[0].Result.EoResult.ResultItem[0].RealValue = 0.44;
		eoConfs[0].Result.EoResult.ResultItem[1].bActive = true;
		eoConfs[0].Result.EoResult.ResultItem[1].isPass = true;
		eoConfs[0].Result.EoResult.ResultItem[1].RealValue = 0.23;

		eoConfs[1].EOType = ePolygon;
		eoConfs[1].Result.EoResult.EoPass = false;

		eoConfs[1].Result.EoResult.sErrorCode = "AverageY_Err";

		for (unsigned int index = 0; index < PolygonResultNames.size(); index++)
		{
			eoConfs[1].Result.EoResult.ResultItem[index].bActive = false;
		}

		eoConfs[1].Result.EoResult.ResultItem[4].bActive = true;
		eoConfs[1].Result.EoResult.ResultItem[4].isPass = true;
		eoConfs[1].Result.EoResult.ResultItem[4].RealValue = 0.23;

		eoConfs[1].Result.EoResult.ResultItem[13].bActive = true;
		eoConfs[1].Result.EoResult.ResultItem[13].isPass = false;
		eoConfs[1].Result.EoResult.ResultItem[13].RealValue = 0.44;
		eoConfs[1].Result.EoResult.ResultItem[13].MinRange = 0.11;
		eoConfs[1].Result.EoResult.ResultItem[13].MaxRange = 0.22;
	}

	rapidjson::Document document;
	document.SetObject();

	rapidjson::Document::AllocatorType& allocator = document.GetAllocator();

	bool CraftIsOK = *pbCraftIsOK;
	short iCurveCount = 1;
	short iCurveID = 1;
	bool bCurveIsOK = true;

	document.AddMember("CraftIsOK", CraftIsOK, allocator);
	document.AddMember("CurveCount", iCurveCount, allocator);
	document.AddMember("CurveID", iCurveID, allocator);
	document.AddMember("CurveIsOK", bCurveIsOK, allocator);

	rapidjson::Value eoResultArray(rapidjson::kArrayType);

	// Populate EoResult objects
	if (SYSTEM_MOVE_SERIES && EoCount > 0)
	{
		for (int i = 0; i < EoCount; ++i)
		{
			//dbgPrint(1, 0, "ResultData IndexEo:%d  EoType:%d\n\n", i, eoConfs[i].EOType);
			rapidjson::Value eoResult(rapidjson::kObjectType);

			eoResult.AddMember("EoIndex", i, allocator);
			eoResult.AddMember("EoType", eoConfs[i].EOType, allocator);

			eoResult.AddMember("EoPass", eoConfs[i].Result.EoResult.EoPass, allocator);

			eoResult.AddMember("ErrorCode", Value(eoConfs[i].Result.EoResult.sErrorCode.c_str(), allocator), allocator);

			rapidjson::Value resultItemArray(rapidjson::kArrayType);

			switch (eoConfs[i].EOType)
			{
			case eLine:
				//Entry_X  Entry_y
				for (unsigned int index = 0; index < LineResultNames.size(); index++)
				{
					rapidjson::Value resultItem(rapidjson::kObjectType);
					string tempName = LineResultNames.at(index);
					resultItem.AddMember("Name", Value(tempName.c_str(), allocator), allocator);
					resultItem.AddMember("Active", eoConfs[i].Result.EoResult.ResultItem[index].bActive, allocator);
					if (eoConfs[i].Result.EoResult.ResultItem[index].bActive)
					{
						//IsPass
						resultItem.AddMember("IsPass", eoConfs[i].Result.EoResult.ResultItem[index].isPass, allocator);
						resultItem.AddMember("RealValue", eoConfs[i].Result.EoResult.ResultItem[index].RealValue, allocator);

						//只有计算项有最值，这个需不需要加进来呢 Qnext
						resultItem.AddMember("MinRange", eoConfs[i].Result.EoResult.ResultItem[index].MinRange, allocator);
						resultItem.AddMember("MaxRange", eoConfs[i].Result.EoResult.ResultItem[index].MaxRange, allocator);
					}
					else
					{
						;  //不激活
					}
					resultItemArray.PushBack(resultItem, allocator);
				}

				break;
			case ePolygon:

				for (unsigned int index = 0; index < PolygonResultNames.size(); index++)
				{
					rapidjson::Value resultItem(rapidjson::kObjectType);
					string tempName = PolygonResultNames.at(index);
					resultItem.AddMember("Name", Value(tempName.c_str(), allocator), allocator);
					resultItem.AddMember("Active", eoConfs[i].Result.EoResult.ResultItem[index].bActive, allocator);
					if (eoConfs[i].Result.EoResult.ResultItem[index].bActive)
					{
						//IsPass
						resultItem.AddMember("IsPass", eoConfs[i].Result.EoResult.ResultItem[index].isPass, allocator);
						resultItem.AddMember("RealValue", eoConfs[i].Result.EoResult.ResultItem[index].RealValue, allocator);

						//只有计算项有最值，这个需不需要加进来呢 Qnext
						resultItem.AddMember("MinRange", eoConfs[i].Result.EoResult.ResultItem[index].MinRange, allocator);
						resultItem.AddMember("MaxRange", eoConfs[i].Result.EoResult.ResultItem[index].MaxRange, allocator);
					}
					else
					{
						;  //不激活
					}
					resultItemArray.PushBack(resultItem, allocator);
				}
				break;

			case eEnvelope:
				//EnvelopeResultNames
				for (unsigned int index = 0; index < EnvelopeResultNames.size(); index++)
				{
					rapidjson::Value resultItem(rapidjson::kObjectType);
					string tempName = EnvelopeResultNames.at(index);
					resultItem.AddMember("Name", Value(tempName.c_str(), allocator), allocator);
					resultItem.AddMember("Active", eoConfs[i].Result.EoResult.ResultItem[index].bActive, allocator);
					if (eoConfs[i].Result.EoResult.ResultItem[index].bActive)
					{
						//IsPass
						resultItem.AddMember("IsPass", eoConfs[i].Result.EoResult.ResultItem[index].isPass, allocator);
						resultItem.AddMember("RealValue", eoConfs[i].Result.EoResult.ResultItem[index].RealValue, allocator);

						//只有计算项有最值，这个需不需要加进来呢 Qnext
						resultItem.AddMember("MinRange", eoConfs[i].Result.EoResult.ResultItem[index].MinRange, allocator);
						resultItem.AddMember("MaxRange", eoConfs[i].Result.EoResult.ResultItem[index].MaxRange, allocator);
					}
					else
					{
						;  //不激活
					}
					resultItemArray.PushBack(resultItem, allocator);
				}
				break;

			default:
				break;
			}
			eoResult.AddMember("ResultItem", resultItemArray, allocator);
			eoResultArray.PushBack(eoResult, allocator);
		}
	}

	document.AddMember("EoResult", eoResultArray, allocator);
	// CurveResult object
	rapidjson::Value curveResultObject(rapidjson::kObjectType);
	curveResultObject.AddMember("Xmin_X", CurResult.Xmin_X, allocator);
	curveResultObject.AddMember("Xmin_Y", CurResult.Xmin_Y, allocator);
	curveResultObject.AddMember("Xmax_X", CurResult.Xmax_X, allocator);
	curveResultObject.AddMember("Xmax_Y", CurResult.Xmax_Y, allocator);
	curveResultObject.AddMember("Ymin_X", CurResult.Ymin_X, allocator);
	curveResultObject.AddMember("Ymin_Y", CurResult.Ymin_Y, allocator);
	curveResultObject.AddMember("Ymax_X", CurResult.Ymax_X, allocator);
	curveResultObject.AddMember("Ymax_Y", CurResult.Ymax_Y, allocator);
	curveResultObject.AddMember("PeakPeakX", CurResult.PeakPeakX, allocator);
	curveResultObject.AddMember("PeakPeakY", CurResult.PeakPeakY, allocator);
	curveResultObject.AddMember("Tmax_T", CurResult.Tmax_T, allocator);
	curveResultObject.AddMember("Tmax_X", CurResult.Tmax_X, allocator);
	curveResultObject.AddMember("Tmax_Y", CurResult.Tmax_Y, allocator);
	// Qnext Add Other Memember

	document.AddMember("CurveResult", curveResultObject, allocator);

	// Pack the JSON into a string
	rapidjson::StringBuffer buffer;
	rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
	document.Accept(writer);
	// Print the packed JSON string

	//std::cout << buffer.GetString() << std::endl;

	std::string json_string = buffer.GetString();
	// Calculate length of JSON string
	*Length = json_string.length() + 1;// or json_string.size();
	if (*pBuffer != NULL)
	{
		//dbgPrint(1, 0, "free  EoResult  Buff\n\n");
		free(*pBuffer);
	}
	else
	{
		;// dbgPrint(1, 0, "EoResult  Buff  Is  NULL\n\n");
	}
	*pBuffer = (char*)malloc(*Length);
	memcpy(*pBuffer, buffer.GetString(), *Length);   //这个长度一定要+1把"/0"带上

	//rt_dbgPrint(1, 0, "Eo Result:%d\n\n", *Length);
}

/*************************************                 Simu               ******************************************************/
/**
* @name  GetAntiForce
* @brief
* @param TargetPos
* @retuen Antiforce
*/
float GetAntiForce(float TargetPos)
{
	//定义变量
	float aFs[6] = { NA,NA,NA,NA,NA,NA };
	float aPs[5] = { 0,10,15,24,36 };
	float fK = 100;
	float fExponent = 4;
	float fAntiForce = 0;
	int iseed = 1;
	float fRand = 0;
	if (aFs[0] == NA)
	{
		aFs[0] = 0;
		aFs[1] = fK * (aPs[1] - aPs[0]) + aFs[0];
		aFs[2] = sin((aPs[2] - aPs[1]) * 6.28) * 500 + aFs[1];
		aFs[3] = 0 + aFs[2];
		aFs[4] = pow(aFs[3] - aPs[2], fExponent) + aFs[3];
		aFs[5] = 0 + aFs[4];
	}
	if (TargetPos <= aPs[0])
	{
		fAntiForce = aFs[0];
	}
	else if (TargetPos < aPs[1])
	{
		fAntiForce = fK * (TargetPos - aFs[0]) + aFs[0];
	}
	else if (TargetPos < aPs[2])
	{
		fAntiForce = sin(TargetPos - aFs[1] * 6.28) * 500 + aFs[1];
	}
	else if (TargetPos < aPs[3])
	{
		fAntiForce = fK * (TargetPos - aFs[0]) + aFs[0];
	}
	else if (TargetPos >= aPs[3])
	{
		fAntiForce = pow(TargetPos - aFs[3], fExponent) + aFs[3];
	}
	else
	{
		fAntiForce = 1000000;
	}

	iseed = (iseed * 214013 + 2531011) % 4294967296;
	fRand = iseed / (double)(4294967296);
	fAntiForce = fAntiForce + fRand * 10;

	return fAntiForce;
}

/**
* @name  Delay
* @brief
* @param
* @retuen oldValue(double)
*/
double Delay(double newValue)
{
	short int iDelayTimes = 8;
	double oldValue = 0;
	static double aBuf[10] = { 0 };   //是否要加static
	static short index = 0;
	aBuf[index] = newValue;
	if (index == 0)
	{
		oldValue = aBuf[iDelayTimes - 1];
	}
	else
	{
		oldValue = aBuf[index - 1];
	}
	index = index + 1;
	if (index >= iDelayTimes)
	{
		index = 0;
	}
	return oldValue;
}

/**
* @name  Delay1
* @brief
* @param
* @retuen oldValue(double)
*/
double Delay1(double newValue)
{
	short int iDelayTimes = 8;
	double oldValue = 0;
	static double aBuf[10] = { 0 };   //是否要加static
	static short index = 0;
	aBuf[index] = newValue;
	if (index == 0)
	{
		oldValue = aBuf[iDelayTimes - 1];
	}
	else
	{
		oldValue = aBuf[index - 1];
	}
	index = index + 1;
	if (index >= iDelayTimes)
	{
		index = 0;
	}
	return oldValue;
}

/**
* @name  SiumPoints
* @brief Detects a rising edge of a boolean
* @param Boolean signal to be checked
* @retuen ErrCode(unsigned short int)
*/
bool R_Trig(bool preSignal, bool curSignal)
{
	return !preSignal && curSignal;
}

/**
* @name  MarchSium
* @brief Simulation
* @param psMarchSiumIn psMarchSiumOut
* @retuen
* @Areas External calls
*/
void MarchSium(MarchSiumInData* psMarchSiumIn, MarchSiumOutData* psMarchSiumOut)
{
	bool bCur, bLast = false;
	float fMotorGain = 3.2E-6;
	float fEncoderGain = 1E-5;
	double TargetPosition;
	float fAntiforce = 0;
	double pos, pos1;
	unsigned int icount = 0;
	switch (psMarchSiumIn->_MotorCW)
	{
	case 0:
		psMarchSiumOut->_MotorSw = 0x5240;
		break;
	case 6:
		psMarchSiumOut->_MotorSw = 0x5221;
		break;
	case 7:
		psMarchSiumOut->_MotorSw = 0x5233;
		break;
	case 0x1F:
		psMarchSiumOut->_MotorSw = 0x5237;
		break;
	default:
		break;
	}

	if (psMarchSiumOut->_MotorErrorCode != 0)
	{
		psMarchSiumOut->_MotorSw = 0x5238;
	}


	if ((psMarchSiumIn->_MotorCW && 0x80 == 0x80))
	{
		bCur = true;
		bLast = false;
	}
	else
	{
		bCur = false;
		bLast = false;
	}

	if (R_Trig(bCur, bLast))
	{
		psMarchSiumOut->_MotorErrorCode = 0;
		psMarchSiumOut->_MotorSw = 0x5237;
	}

	if (psMarchSiumIn->_ModesOfOp == 7 || psMarchSiumIn->_ModesOfOp == 8)  //Pos
	{
		TargetPosition = psMarchSiumIn->_TargetPos * fMotorGain;
		fAntiforce = GetAntiForce(TargetPosition);
		psMarchSiumOut->_Force = fAntiforce;
		pos = Delay(TargetPosition);
		psMarchSiumOut->_MotorRealPostion = (long int)(pos / fMotorGain);

		pos1 = TargetPosition - fAntiforce / 10000;
		psMarchSiumOut->_EncoderPosition = (int)(Delay1(pos1) / fEncoderGain);
	}
	else
	{
		//Error ModesofOP
	}


	if (psMarchSiumOut->_ModesOfOpDis != psMarchSiumIn->_ModesOfOp)
	{
		icount = icount + 1;
		if (icount > 100)
		{
			psMarchSiumOut->_ModesOfOpDis = psMarchSiumIn->_ModesOfOp;
			icount = 0;
		}
	}
}



