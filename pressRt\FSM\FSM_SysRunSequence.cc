#include "PushResult.h"
#include "FSM_SysReadyEntry.h"
#include "FSM_SysFaultEntry.h"
#include "rttimer.h"
#include "FSM_SysRunSequence.h"
#include "SysShareMemoryDefine.h"
#include "SysVarDefine.h"
#include "FSM_SysReady.h"
#include "EO.h"
#include "EoThread.h"
/** FsmSysRunSequence
* @param[in]     None
* @param[out]    None
* @return        ErrorCode
*/
static const char* RunSeqStateDesc[] = {"RunSeq_State_PartIdReady","RunSeq_State_ExeCuteSequence", "RunSeq_State_WaitSaveResult","RunSeq_State_Finish"};
uint8 LastContinueSingal;
uint8 LastWaitSingal;
ERunSeq_State	eRunSequenceState, eLastRunSequenceState;
ErrorInfo RunSeqErrorInfo;
Tag3FPoint curPoint;
ErrorInfo  FsmSysRunSequence(sEXCW* psExCW,sEXSW  *psExSW)
{
	switch (eRunSequenceState)
	{
	case RunSeq_State_PartIdReady:
	{
		if (SysError(&pSysShareData->sErrInfoPara) >= 0)
		{
			eRunSequenceState = RunSeq_State_WaitSaveResult;
		}
		else
		{
			if (!pSysShareData->uNeedSnMakerFlag)
			{
				eRunSequenceState = RunSeq_State_ExeCuteSequence;
			}
		}

		if (Seq.bStopRequest)
		{
			eRunSequenceState = RunSeq_State_WaitSaveResult;
		}
	}
		break;
	case RunSeq_State_ExeCuteSequence:		//此处需要注意特别处理的是:回原点的过程中就应该开始存储文件
	{	
		Seq.bAbortCurrentItem = psExCW->JumpToNextItem;
		Seq.ContinueSingal = psExCW->uContinues.usContinueSiganal;
		if (LastContinueSingal != Seq.ContinueSingal)
		{
			//rt_dbgPrint(1, 0, "LastContinueSingal[%d] ContinueSingal[%d]\n", LastContinueSingal, Seq.ContinueSingal);
			LastContinueSingal = Seq.ContinueSingal;
		}

		if (!Seq.bPauseRequest || !Seq.bStopRequest)
		{
			if (SysError(&pSysShareData->sErrInfoPara) > 0)		//非警告
			{
				Seq.bPauseRequest = true;
			}
		}

#ifdef PRODUCT_LINE == 1
		bool bTunnelJudgeFinished = false;
		curPoint.T = fTime;
		curPoint.X = pSysShareData->sSysFbkVar.fPosFbk;
		curPoint.Y = pSysShareData->sSysFbkVar.fSenosrVarFbk;
		Seq.bEoTunnelTrig = Eo_TunnelJudgeNew(&curPoint,&bCollctFinish,&bTunnelJudgeFinished);
#endif

		RunSeqErrorInfo = SequenceExecute(&Seq);
		psExSW->bHadJumpToNextItem = Seq.bHadAbortCurrentItem;
		psExSW->uWaits.usWaitSiganal =Seq.WaitSingal;
		if (LastWaitSingal != Seq.WaitSingal)
		{
			//rt_dbgPrint(1, 0, "LastWaitSingal[%d] WaitSingal[%d]\n", LastWaitSingal, Seq.WaitSingal);
			LastWaitSingal = Seq.WaitSingal;
		}

		psExSW->bSequenceBusy = Seq.bBusy;

		if (Seq.bBusy)
		{
			if (Seq.bSequencePaused && !Seq.bSequenceStoped)
			{
				psExSW->bSequencePaused = true;
				if (pSysShareData->sExSW.eControlType == Explorer)
					pSysShareData->SCCW.RunSequence = false;

				if ((SysError(&pSysShareData->sErrInfoPara) >= 0))
				{
					//rt_dbgPrint(1, 0, "Seq.bBusy  SysError\n");
					FsmSysFaultEntry();
				}
				else
				{
					FsmSysReadyEntry();
				}
			}
		}
		else
		{
			if (pSysShareData->sExSW.eControlType == Explorer)
				pSysShareData->SCCW.RunSequence = false;

			eRunSequenceState = RunSeq_State_WaitSaveResult;
		}
	}
		break;
	case RunSeq_State_WaitSaveResult:
	{
		if (!psChartsData->bCollecFinish)
		{
			if (psChartsData->uPointsCount != 0 && psChartsData->uHadPushPoints != psChartsData->uPointsCount)
			{
				//do
				//{
				//	//rt_dbgPrint(1, 0, "uPointsCount:%d  uHadPushPoints:%d\n",
				//	//	psChartsData->uPointsCount,
				//	//	psChartsData->uHadPushPoints);
				//	PushChartFrame();
				//} while (psChartsData->uHadPushPoints != psChartsData->uPointsCount);

				if (psChartsData->uHadPushPoints == psChartsData->uPointsCount)
				{
					psChartsData->bCollecFinish = true;
				}
			}
			else
			{
				psChartsData->bCollecFinish = true;
			}
		}

		if (psChartsData->bChartEvalFinish &&!pSysShareData->bStartSaveResult)
		{
			//rt_dbgPrint(1, 0, "uPointsCount:%d \n", psChartsData->uPointsCount);
			eRunSequenceState = RunSeq_State_Finish;
		}
	}
	break;
	case RunSeq_State_Finish:
	{
		psExSW->bSequenceBusy = false;
		psExSW->bSequenceEnd = true;
		psExSW->bSequencePaused = false;
		pSysShareData->bHadDealPlcSn = false;

		if (psChartsData->bChartEvalOK)
		{
			psExSW->bResultNOK = false;
			psExSW->bResultOK = true;
		}
		else
		{
			psExSW->bResultNOK = true;
			psExSW->bResultOK = false;
		}

		psExSW->bPartIdUsed = false;
		psExSW->uWaits.usWaitSiganal = 0;
		psExSW->bHadJumpToNextItem = false;
		psExSW->bSequenceAbnormal = false;

		if(bExecutePowerCmd)
			bExecutePowerCmd = false;

		if (((SysError(&pSysShareData->sErrInfoPara) >= 0))|| RunSeqErrorInfo.ErrCode)
			FsmSysFaultEntry();
		else
			FsmSysReadyEntry();
	}
		break;
	default:
		break;
	}

	//if (eRunSequenceState != eLastRunSequenceState)
	//{
	//	dbgPrint(1, 0, "FSM SeqState:[%s ->> %s]\n",
	//		RunSeqStateDesc[eLastRunSequenceState], 
	//		RunSeqStateDesc[eRunSequenceState]);
	//	eLastRunSequenceState = eRunSequenceState;
	//}

	ErrorInfoPack(&RunSeqErrorInfo, "FsmSysRunSequence","");
	return RunSeqErrorInfo;
}
