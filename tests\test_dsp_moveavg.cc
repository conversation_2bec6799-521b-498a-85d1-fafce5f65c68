#include <gtest/gtest.h>
extern "C" { #include "dsp.h" }

TEST(MoveAvg, InitInvalid) {
  sMoveAvgFilter f = {};
  EXPECT_EQ(InitMoveAvgFilter(&f, 0), -1);
  EXPECT_EQ(f.Capcity, 1);
  EXPECT_EQ(InitMoveAvgFilter(&f, 257), -1);
  EXPECT_EQ(f.Capcity, 1);
}

TEST(MoveAvg, RunWindowAndWrap) {
  sMoveAvgFilter f = {}; ASSERT_EQ(InitMoveAvgFilter(&f, 4), 0);
  float v=0;
  v=MoveAvgFilter(&f, 4);  EXPECT_NEAR(v, 4.0, 1e-6);
  v=MoveAvgFilter(&f, 6);  EXPECT_NEAR(v, (4+6)/2.0, 1e-6);
  v=MoveAvgFilter(&f, 8);  EXPECT_NEAR(v, (4+6+8)/3.0, 1e-6);
  v=MoveAvgFilter(&f,10);  EXPECT_NEAR(v, (4+6+8+10)/4.0, 1e-6);
  v=MoveAvgFilter(&f,12);  EXPECT_NEAR(v, (6+8+10+12)/4.0, 1e-6);
}