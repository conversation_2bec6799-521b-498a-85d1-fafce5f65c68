#ifndef GlobalLocalVar_H_
#define GlobalLocalVar_H_

#include "core/base.h"
#include "FieldbusAux.h"

#pragma pack(1)
typedef struct
{
	str16			sKey;
	str255			sName;
	EDataType		eDataType;
	void* pVar = NULL;
	void* pInitVar = NULL;
	uint8			uInitValLen;
}SVarUnit;

typedef struct
{
	uint8				uVarCount;
	SVarUnit			aSVariableVar[GLOBALVAR_MAX_NUMBER];
}SVariableVar;


extern SVariableVar      sSysGlobalVar;
extern SVariableVar      sLocalVar;
extern void ResetLocalVar();
extern ErrorInfo UpdateLocalVarSdoObject();
extern ErrorInfo UpdateGlobalVarSetInfo(bool bUpdateTrig);

extern bool FindGlobalLocalVar(SFieldbusConfigUnit* pMatchVar, SFieldbusConfigUnit* pTargetVar);
extern ErrorInfo ResolverLocalVar(char* psLoaclVar);
#pragma pack()
#endif